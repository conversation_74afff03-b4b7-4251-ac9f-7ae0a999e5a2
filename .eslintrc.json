{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "parserOptions": {"project": ["tsconfig.json"], "createDefaultProgram": true}, "extends": ["plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates", "plugin:@typescript-eslint/recommended"], "plugins": ["@typescript-eslint", "eslint-plugin-unicorn", "prefer-arrow", "jsdoc", "import"], "rules": {"@angular-eslint/component-class-suffix": "warn", "@angular-eslint/component-selector": ["warn", {"type": "element", "prefix": "kebab-case"}], "@angular-eslint/contextual-lifecycle": "warn", "@angular-eslint/directive-class-suffix": "warn", "@angular-eslint/directive-selector": ["warn", {"type": "attribute", "prefix": "", "style": "camelCase"}], "@angular-eslint/no-conflicting-lifecycle": "warn", "@angular-eslint/no-host-metadata-property": "warn", "@angular-eslint/no-input-rename": "warn", "@angular-eslint/no-inputs-metadata-property": "warn", "@angular-eslint/no-output-native": "warn", "@angular-eslint/no-output-on-prefix": "warn", "@angular-eslint/no-output-rename": "warn", "@angular-eslint/no-outputs-metadata-property": "warn", "@angular-eslint/use-lifecycle-interface": "warn", "@angular-eslint/use-pipe-transform-interface": "warn", "@typescript-eslint/adjacent-overload-signatures": "warn", "@typescript-eslint/ban-types": ["warn", {"types": {"Object": {"message": "Avoid using the `Object` type. Did you mean `object`?"}, "Function": {"message": "Avoid using the `Function` type. Prefer a specific function type, like `() => void`."}, "Boolean": {"message": "Avoid using the `Boolean` type. Did you mean `boolean`?"}, "Number": {"message": "Avoid using the `Number` type. Did you mean `number`?"}, "String": {"message": "Avoid using the `String` type. Did you mean `string`?"}, "Symbol": {"message": "Avoid using the `Symbol` type. Did you mean `symbol`?"}}}], "@typescript-eslint/consistent-type-assertions": "warn", "@typescript-eslint/consistent-type-definitions": "warn", "@typescript-eslint/dot-notation": "warn", "@typescript-eslint/explicit-member-accessibility": ["off", {"accessibility": "explicit"}], "@typescript-eslint/member-delimiter-style": ["warn", {"multiline": {"delimiter": "none", "requireLast": true}, "singleline": {"delimiter": "semi", "requireLast": false}}], "@typescript-eslint/member-ordering": ["error", {"default": ["instance-field", "constructor", "public-method", "protected-method", "private-method"]}], "@typescript-eslint/naming-convention": ["warn", {"selector": "enumMember", "format": ["UPPER_CASE"]}], "@typescript-eslint/no-empty-interface": "warn", "@typescript-eslint/no-inferrable-types": ["warn", {"ignoreParameters": true}], "@typescript-eslint/no-misused-new": "warn", "@typescript-eslint/no-namespace": "warn", "@typescript-eslint/no-non-null-assertion": "warn", "@typescript-eslint/no-shadow": ["warn", {"hoist": "all"}], "@typescript-eslint/no-unused-expressions": "warn", "@typescript-eslint/prefer-for-of": "warn", "@typescript-eslint/prefer-function-type": "warn", "@typescript-eslint/prefer-namespace-keyword": "warn", "@typescript-eslint/prefer-readonly": "warn", "@typescript-eslint/quotes": ["warn", "single", {"allowTemplateLiterals": true, "avoidEscape": true}], "@typescript-eslint/restrict-plus-operands": "warn", "@typescript-eslint/semi": ["warn", "never"], "@typescript-eslint/triple-slash-reference": ["warn", {"path": "always", "types": "prefer-import", "lib": "always"}], "@typescript-eslint/type-annotation-spacing": "warn", "@typescript-eslint/unified-signatures": "warn", "arrow-body-style": "warn", "arrow-parens": ["off", "always"], "no-console": ["error", {"allow": [""]}], "complexity": ["error", 15], "constructor-super": "warn", "curly": "warn", "eol-last": "warn", "eqeqeq": ["warn", "always"], "guard-for-in": "warn", "id-blacklist": "warn", "id-match": "warn", "import/no-deprecated": "error", "import/order": "off", "jsdoc/check-alignment": "warn", "jsdoc/newline-after-description": "off", "jsdoc/no-types": "warn", "max-len": ["warn", {"code": 300}], "new-parens": "warn", "no-bitwise": "warn", "no-caller": "warn", "no-cond-assign": "warn", "no-debugger": "warn", "no-eval": "warn", "no-fallthrough": "warn", "no-magic-numbers": "off", "no-multiple-empty-lines": "warn", "no-new-wrappers": "warn", "no-throw-literal": "warn", "no-trailing-spaces": "warn", "no-undef-init": "warn", "no-underscore-dangle": ["warn", {"allow": ["_kind", "_KIND"]}], "no-unsafe-finally": "warn", "no-unused-labels": "warn", "no-var": "warn", "object-shorthand": "warn", "one-var": ["warn", "never"], "prefer-arrow/prefer-arrow-functions": "warn", "prefer-const": "warn", "quote-props": ["warn", "as-needed"], "radix": "warn", "space-before-function-paren": "off", "unicorn/filename-case": "warn", "use-isnan": "warn"}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended"], "rules": {"@angular-eslint/template/banana-in-box": "warn", "@angular-eslint/template/eqeqeq": "warn", "@angular-eslint/template/no-negated-async": "warn", "@angular-eslint/template/prefer-control-flow": "warn"}}]}