{"extends": "stylelint-config-sass-guidelines", "plugins": ["stylelint-scss", "stylelint-config-prettier"], "rules": {"at-rule-name-case": "lower", "at-rule-semicolon-newline-after": "always", "at-rule-semicolon-space-before": "never", "color-hex-case": null, "color-hex-length": "long", "color-named": null, "comment-no-empty": true, "declaration-block-no-duplicate-properties": [true, {"ignore": ["consecutive-duplicates"]}], "declaration-block-no-redundant-longhand-properties": true, "declaration-block-no-shorthand-property-overrides": true, "declaration-property-value-disallowed-list": null, "font-family-no-duplicate-names": true, "font-weight-notation": "numeric", "function-disallowed-list": ["darken", "lighten"], "function-calc-no-invalid": true, "function-calc-no-unspaced-operator": true, "function-linear-gradient-no-nonstandard-direction": true, "function-url-no-scheme-relative": true, "function-parentheses-space-inside": null, "indentation": null, "keyframe-declaration-no-important": true, "length-zero-no-unit": null, "max-nesting-depth": null, "media-feature-colon-space-after": "always", "media-feature-colon-space-before": "never", "number-leading-zero": "always", "media-feature-name-no-unknown": true, "no-duplicate-at-import-rules": true, "no-duplicate-selectors": true, "no-extra-semicolons": true, "no-unknown-animations": null, "order/order": null, "order/properties-alphabetical-order": null, "property-no-unknown": true, "property-no-vendor-prefix": null, "selector-attribute-operator-space-after": "never", "selector-attribute-operator-space-before": "never", "selector-attribute-brackets-space-inside": "never", "selector-class-pattern": null, "selector-combinator-space-after": "always", "selector-combinator-space-before": "always", "selector-descendant-combinator-no-non-space": true, "selector-type-case": "lower", "selector-type-no-unknown": true, "selector-pseudo-class-no-unknown": true, "selector-pseudo-class-case": "lower", "string-no-newline": true, "selector-max-compound-selectors": null, "selector-no-qualifying-type": null, "selector-no-vendor-prefix": null, "selector-pseudo-element-case": "lower", "scss/at-extend-no-missing-placeholder": null, "scss/at-function-pattern": null, "scss/at-mixin-pattern": null, "unit-case": "lower", "unit-no-unknown": true, "value-keyword-case": "lower", "value-list-comma-newline-after": "never-multi-line", "value-list-comma-newline-before": "never-multi-line", "value-list-comma-space-after": "always"}}