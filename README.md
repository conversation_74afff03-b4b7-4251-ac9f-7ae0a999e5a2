# Foyer digiclaims-courtier Front

[![Quality Gate Status](https://sonarsinistres.foyer.lu/api/project_badges/measure?project=foyer-digiclaims-courtier-front&metric=alert_status&token=eebed9c8446d73271c4fd39f141deb0fe5f1957f)](https://sonarsinistres.foyer.lu/dashboard?id=foyer-digiclaims-courtier-front)
[![Bugs](https://sonarsinistres.foyer.lu/api/project_badges/measure?project=foyer-digiclaims-courtier-front&metric=bugs&token=eebed9c8446d73271c4fd39f141deb0fe5f1957f)](https://sonarsinistres.foyer.lu/dashboard?id=foyer-digiclaims-courtier-front)
[![Code Smells](https://sonarsinistres.foyer.lu/api/project_badges/measure?project=foyer-digiclaims-courtier-front&metric=code_smells&token=eebed9c8446d73271c4fd39f141deb0fe5f1957f)](https://sonarsinistres.foyer.lu/dashboard?id=foyer-digiclaims-courtier-front)
[![Coverage](https://sonarsinistres.foyer.lu/api/project_badges/measure?project=foyer-digiclaims-courtier-front&metric=coverage&token=eebed9c8446d73271c4fd39f141deb0fe5f1957f)](https://sonarsinistres.foyer.lu/dashboard?id=foyer-digiclaims-courtier-front)

This project was generated with [Angular CLI](https://github.com/angular/angular-cli) version 15.

## K8S status

K8s dev status: ![K8s dev status](https://argocd.apps.dev.foyer.cloud/api/badge?name=foyer-digiclaims-courtier-front.sinistres-marche-belge.svc&revision=true)

K8s stg status: ![K8s stg status](https://argocd.apps.stg.foyer.cloud/api/badge?name=foyer-digiclaims-courtier-front.sinistres-marche-belge.svc&revision=true)

K8s ppr status: ![K8s ppr status](https://argocd.apps.ppr.foyer.cloud/api/badge?name=foyer-digiclaims-courtier-front.sinistres-marche-belge.svc&revision=true)

K8s prd status: ![K8s prd status](https://argocd.apps.prd.foyer.cloud/api/badge?name=foyer-digiclaims-courtier-front.sinistres-marche-belge.svc&revision=true)

## URL to launch the app

U: https://foyer-digiclaims-courtier-front.sinistres-marche-belge.apps.dev.foyer.cloud/

B: https://foyer-digiclaims-courtier-front.sinistres-marche-belge.apps.stg.foyer.cloud/

R: https://foyer-digiclaims-courtier-front.sinistres-marche-belge.apps.ppr.foyer.cloud/

P: https://foyer-digiclaims-courtier-front.sinistres-marche-belge.apps.prd.foyer.cloud/

## URL to launch the app via wam

U: https://digiclaims-courtier-u.foyer.lu/

B: https://digiclaims-courtier-b.foyer.lu/

R: https://digiclaims-courtier-r.foyer.lu/

P: https://digiclaims-courtier.foyer.lu/

## Development server

Run `ng serve` for a dev server. Navigate to `http://localhost:4200/`. The application will automatically reload if you change any of the source files.

## Code scaffolding

Run `ng generate component component-name` to generate a new component. You can also use `ng generate directive|pipe|service|class|guard|interface|enum|module`.

## Build

Run `ng build` to build the project. The build artifacts will be stored in the `dist/` directory.

## Running unit tests

Run `ng test` to execute the unit tests via [Karma](https://karma-runner.github.io).

## Running end-to-end tests

Run `ng e2e` to execute the end-to-end tests via a platform of your choice. To use this command, you need to first add a package that implements end-to-end testing capabilities.

## Further help

To get more help on the Angular CLI use `ng help` or go check out the [Angular CLI Overview and Command Reference](https://angular.io/cli) page.

## Github pre-commit hook

Executer la commande: ln -s ../../.github/hooks/pre-commit.sh .git/hooks/pre-commit
Puis redémarrer l'ide

## Troubleshoot

lors du clone du repository , vous pouvez rencontrer un soucis avec les path des fichier qui sont trop long, il suffit de les autoriser

> git config --system core.longpaths true
