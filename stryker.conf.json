{"$schema": "./node_modules/@stryker-mutator/core/schema/stryker-schema.json", "_comment": "This config was generated using 'stryker init'. Please see the guide for more information: https://stryker-mutator.io/docs/stryker-js/guides/angular", "mutate": ["src/app/services/**/*.ts", "src/app/utils/**/*.ts", "!src/**/*.mock.ts", "!src/**/*.spec.ts", "!src/test.ts", "!src/environments/*.ts"], "testRunner": "karma", "karma": {"configFile": "karma.conf.js", "projectType": "angular-cli", "config": {"browsers": ["ChromeHeadless"]}}, "reporters": ["progress", "clear-text", "html", "json"], "coverageAnalysis": "perTest", "checkers": ["typescript"], "tsconfigFile": "tsconfig.json", "ignoreStatic": true}