{"name": "foyer-digiclaims-courtier-front", "version": "0.160.0-SNAPSHOT", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build --configuration production --cross-origin=use-credentials", "test-coverage": "ng test --code-coverage --progress=false --browsers=ChromeHeadless --watch=false", "build:all": "npm run build", "build:app": "npm run build", "watch": "ng build --watch --configuration development", "test": "ng test", "ci:test": "ng test --watch=false --progress=false --browsers=ChromeHeadless", "ci:build": "npm run build:app", "ci:coverage": "ng test --code-coverage --progress=false --browsers=ChromeHeadless --watch=false", "fmt": "prettier --write .", "fmt-dry-run": "prettier --check .", "lint": "ng lint", "lint-and-fix": "ng lint --fix"}, "private": true, "dependencies": {"@angular/animations": "17.3.12", "@angular/cdk": "17.3.10", "@angular/common": "17.3.12", "@angular/compiler": "17.3.12", "@angular/core": "17.3.12", "@angular/forms": "17.3.12", "@angular/google-maps": "^17.3.10", "@angular/material": "^17.3.10", "@angular/platform-browser": "17.3.12", "@angular/platform-browser-dynamic": "17.3.12", "@angular/router": "17.3.12", "@foyer/authentication": "^11.15.0", "@foyer/ng-i18n": "^2.2.0", "@foyer/ng-input-file": "^1.0.13", "@foyer/ng-select": "^13.21.1", "date-fns": "^2.29.2", "filepond": "^4.32.7", "foyer-design-system": "^2.31.6", "intl-messageformat": "^10.1.4", "intl-pluralrules": "^1.3.1", "karma-verbose-reporter": "^0.0.8", "ngx-filepond": "^7.0.3", "ngx-mask": "^19.0.6", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "^0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.12", "@angular-eslint/builder": "17.5.3", "@angular-eslint/eslint-plugin": "17.5.3", "@angular-eslint/eslint-plugin-template": "17.5.3", "@angular-eslint/schematics": "17.5.3", "@angular-eslint/template-parser": "17.5.3", "@angular/cli": "^17.3.12", "@angular/compiler-cli": "^17.3.12", "@foyer/node-publisher": "^2.0.2", "@stryker-mutator/karma-runner": "^6.3.1", "@stryker-mutator/typescript-checker": "^6.3.1", "@types/jasmine": "~4.3.0", "@typescript-eslint/eslint-plugin": "^7.18.0", "@typescript-eslint/parser": "^7.18.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsdoc": "^50.6.3", "eslint-plugin-prefer-arrow": "^1.2.3", "eslint-plugin-unicorn": "latest", "jasmine-core": "~4.5.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.0.0", "ng-mocks": "^14.13.2", "prettier": "^3.5.3", "stylelint": "^14.14.1", "stylelint-prettier": "^2.0.0", "tslint-to-eslint-config": "^2.6.0", "typescript": "^5.4.5"}, "publisher": {"jibReleaseRepo": "Sinistres-MB/kube.cd", "jibReleasePath": "sinistres-marche-belge/foyer-digiclaims-courtier-front/version.yml", "jibOrganization": "sinistres-marche-belge", "jibName": "foyer-digiclaims-courtier-front"}}