Checklist:

- [ ] Clean code (import inutile, code mort, code commenté, code factorisé, ...)
- [ ] Pas de logs, prints, ... inutile
- [ ] Le naming (variables, methodes, class, ...)
- [ ] Gestion des erreurs / éviter les exceptions
- [ ] Code documenté (swagger + conception fonctionelle et technique à jour)
- [ ] Respect des principes SOLID (https://fr.wikipedia.org/wiki/SOLID_(informatique)
- [ ] Pas de "hard-coding" (surtout pas d'api key!)
- [ ] Couvrir les cas de tests du ticket en tests unitaires

Angular:

- [ ] Typer les variables, éviter "any"
- [ ] HttpClientTestingModule ne doit figurer que dans les imports des wrappers → Si ça plante c'est qu'un des services wrappers + mock n'est pas implémenté dans les providers
- [ ] Pas d'id dans les components shared
- [ ] Favoriser l'utilisation des fonctions pures
- [ ] Eviter les appels a des fonctions dans les expressions des templates, préférer l'utilisation de pipes ou variables précalculés

Rxjs:

- [ ] Mettre les subscribes dans le template et eviter dans le code ts autant que possible
- [ ] Eviter les subscribes imbriqués
- [ ] Eviter la réassignation d'observable
- [ ] Eviter de passer des observables en input des composants
- [ ] Eviter de passer des observables en parametre des fonctions de service
- [ ] Unsubscribe toutes les subscriptions
- [ ] Utiliser pipe et flatMap lorsque l'on doit consommer des flux de différents observables

sources : https://confluence.foyer.lu/display/DSMB/Code+Review
