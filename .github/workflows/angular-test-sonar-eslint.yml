name: Run Test for Angular with esLint and sonar template

on:
    workflow_call: {}

jobs:
    test:
        name: Test
        runs-on: k8s-default

        env:
            SONAR_HOST_URL: ${{ vars.SONAR_HOST_URL }}
            SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}

        steps:
            - name: Checkout
              uses: actions/checkout@v3

            - name: Setup java
              uses: gh-actions/setup-java@latest
              with:
                  java-version: '17'
                  cache: none

            - name: Setup nodejs
              uses: gh-actions/setup-node@latest
              id: node-modules
              with:
                  node-version: '20'
                  cache: node-modules

            - name: Install
              if: steps.node-modules.outputs.cache-hit != 'true'
              run: npm ci

            - name: Setup version
              id: version
              run: |
                  echo "current_version=$(node -p "require('./package.json').version")" >> $GITHUB_OUTPUT
            - name: Sonar
              run: |
                  npm run ci:coverage
                  set +e
                  ./node_modules/.bin/eslint 'src/app/**/*.ts' -f ./sonarqube_formatter.js -o eslint-report.json
                  sh "/opt/sonar-scanner/bin/sonar-scanner"
              env:
                  SONAR_SCANNER_OPTS: -Dsonar.projectVersion=${{ steps.version.outputs.current_version }}
