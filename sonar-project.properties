sonar.projectKey=lu.foyer:foyer-digiclaims-courtier-front
sonar.projectName=foyer-digiclaims-courtier-front
sonar.sources=src
sonar.exclusions=**/node_modules/**,**/karma.conf.js,**/src/config.js,**/src/app/adapters/**,**/src/app/providers/**,**/*.mock.ts,**/*wrapper*.ts,**/src/main.ts,**/src/test.ts,**/src/app/app*.ts,**/src/environments/*,**/modal.service.ts,**/error-popup.component.ts,**/notification-snackbar.component.ts
sonar.sourceEncoding=UTF-8
sonar.tests=src
sonar.test.inclusions=**/*.spec.ts
sonar.ts.tslint.configPath=tslint.json
sonar.typescript.lcov.reportPaths=coverage/foyer-digiclaims-courtier-front/lcov.info
sonar.externalIssuesReportPaths=eslint-report.json
#sonar.verbose=true
