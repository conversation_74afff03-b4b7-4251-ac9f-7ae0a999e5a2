type WindowWithConfig = Window & { Config: Record<string, string> }

const globalConfig: Record<string, string> =
    (window as unknown as WindowWithConfig).Config || {}

export const global = {
    authentication: {
        clientId: globalConfig['OIDC_CLIENT_ID'] || '',
        issuer: globalConfig['OIDC_ISSUER'] || '',
        scope: globalConfig['OIDC_SCOPE'] || '',
        authBackUri: globalConfig['AUTH_BACK_URI'] || '',
        logoutUrl: globalConfig['OIDC_LOGOUT_URL'] || '',
    },
    sinistreMarcheBelgeExternalApiUrl:
        globalConfig['SINISTRE_MARCHE_BELGE_EXTERNAL_API_URL'] || '',
    environementFoyer: (globalConfig['ENV'] || undefined) as
        | 'u'
        | 'b'
        | 'p'
        | undefined,
    situationContratApiUrl: globalConfig['SITUATION_CONTRAT_API_URL'] || '',
    translationApiUrl: globalConfig['TRANSLATION_API_URL'] || '',
    refPersonnesApiUrl: globalConfig['REF_PERSONNES_API_URL'] || '',
    fileTransferApiUrl: globalConfig['FILE_TRANSFER_API'] || '',
    featureWithLanguageSelector:
        globalConfig['IS_WITH_FEATURE_LANGUAGE_SELECTOR'] || '',
    refAdressesApiUrl: globalConfig['REF_ADRESSES_API_URL'] || '',
    googleMapsApiKey: globalConfig['GOOGLE_MAPS_API_KEY'] || '',
}
