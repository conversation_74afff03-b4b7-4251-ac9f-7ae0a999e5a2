@use 'foyer-design-system/packages/CORE/src/main.scss' as *;
@use 'foyer-design-system/packages/font-Barlow/src/main.scss' as *;
@use 'foyer-design-system/packages/MaterialIcons/src/main.scss' as *;
@use 'foyer-design-system/packages/GearIcons/src/main.scss' as *;
@use 'foyer-design-system/packages/AppBarTop/src/main.scss' as *;
@use 'foyer-design-system/packages/AppLayout/src/main.scss' as *;
@use 'foyer-design-system/packages/SideNav/src/main.scss' as *;
@use 'foyer-design-system/packages/AppBarBottom/src/main.scss' as *;
@use 'foyer-design-system/packages/GridFlex/src/main.scss' as *;
@use 'foyer-design-system/packages/ButtonContained/src/main.scss' as *;
@use 'foyer-design-system/packages/ButtonGroup/src/main.scss' as *;
@use 'foyer-design-system/packages/ButtonIcon/src/main.scss' as *;
@use 'foyer-design-system/packages/ButtonOutlined/src/main.scss' as *;
@use 'foyer-design-system/packages/ButtonText/src/main.scss' as *;
@use 'foyer-design-system/packages/ButtonToggle/src/main.scss' as *;
@use 'foyer-design-system/packages/Form/src/main.scss' as *;
@use 'foyer-design-system/packages/FormGroup/src/main.scss' as *;
@use 'foyer-design-system/packages/Input/src/main.scss' as *;
@use 'foyer-design-system/packages/Badge/src/main.scss' as *;
@use 'foyer-design-system/packages/Checkbox/src/main.scss' as *;
@use 'foyer-design-system/packages/Radio/src/main.scss' as *;
@use 'foyer-design-system/packages/Select/src/main.scss' as *;
@use 'foyer-design-system/packages/Textarea/src/main.scss' as *;
@use 'foyer-design-system/packages/Header/src/main.scss' as *;
@use 'foyer-design-system/packages/ChoiceCard/src/main.scss' as *;
@use 'foyer-design-system/packages/List/src/main.scss' as *;
@use 'foyer-design-system/packages/Message/src/main.scss' as *;
@use 'foyer-design-system/packages/Panel/src/main.scss' as *;
@use 'foyer-design-system/packages/Modal/src/main.scss' as *;
@use 'foyer-design-system/packages/Placeholder/src/main.scss' as *;
@use 'foyer-design-system/packages/Snackbar/src/main.scss' as *;
@use 'foyer-design-system/packages/Table/src/main.scss' as *;
@use 'foyer-design-system/packages/Tabs/src/main.scss' as *;
@use 'foyer-design-system/packages/Tooltip/src/main.scss' as *;
@use 'foyer-design-system/packages/Loader/src/main.scss' as *;
@use 'foyer-design-system/packages/DataTile/src/main.scss' as *;
@use 'foyer-design-system/packages/Utilities/src/main.scss' as *;
@use 'foyer-design-system/packages/Slider/src/main.scss' as *;
@use 'foyer-design-system/packages/Dropdown/src/main.scss' as *;
@use 'foyer-design-system/packages/Pagination/src/main.scss' as *;
@use 'foyer-design-system/packages/ModalSide/src/main.scss' as *;
@use 'foyer-design-system/packages/ButtonSplit/src/main.scss' as *;
@use 'foyer-design-system/packages/SelectAdvanced/src/main.scss' as *;
@use 'foyer-design-system/packages/FilterBar/src/main.scss' as *;
@use 'foyer-design-system/packages/TableOfContents/src/main.scss' as *;
@use 'foyer-design-system/packages/MaterialIcons/src/variables' as icons;
@use 'foyer-design-system/packages/GearIcons/src/variables' as *;
@use 'foyer-design-system/packages/CORE/src/variables' as var;
@use 'styles/datepicker.scss' as *;
@use 'styles/override-fds.scss' as *;

html,
body {
    height: 100%;
}
body {
    margin: 0;
}

.person-panel {
    padding: 0;
    margin-bottom: 0;
}

.title {
    color: var(--fds-feather-grey-800);
    font-weight: 700;
    font-size: 20px;
    margin-bottom: 4px;
}

.LicensePlate {
    --border-width: 1px;
    --left-bar-width: 8px;
    --horizontal-padding: 8px;
    position: relative;
    display: inline-block;
    text-align: center;
    padding-right: var(--horizontal-padding);
    padding-left: calc(var(--horizontal-padding) + var(--left-bar-width));
    min-width: 80px;
    border-radius: var.$border-radius-default;
    border: var(--border-width) solid #af0808;
    font-size: 14px;
    font-weight: 600;
    color: #af0808;

    &::before {
        content: '';
        position: absolute;
        left: calc(1 * var(--border-width));
        top: calc(1 * var(--border-width));
        bottom: calc(1 * var(--border-width));
        width: var(--left-bar-width);
        border-top-left-radius: inherit;
        border-bottom-left-radius: inherit;
        background: #0751b3;
    }
}

.poll-question {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    span {
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        color: var(--fds-feather-grey-800);
    }
}
