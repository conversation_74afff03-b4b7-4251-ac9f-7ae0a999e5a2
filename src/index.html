<!doctype html>
<html lang="fr">
    <head>
        <meta charset="utf-8" />
        <title>Digiclaims Courtier</title>
        <base href="/" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" type="image/x-icon" href="favicon.ico" />
        <script src="datadog.js"></script>
        <script src="config.js?version={{BUILD_TIME}}"></script>
        <script>
            ;(function (h, o, t, j, a, r) {
                h.hj =
                    h.hj ||
                    function () {
                        ;(h.hj.q = h.hj.q || []).push(arguments)
                    }
                h._hjSettings = { hjid: 3650667, hjsv: 6 }
                const head = o.getElementsByTagName('head')[0]
                const scriptElement = o.createElement('script')
                scriptElement.async = 1
                scriptElement.src = t + h._hjSettings.hjid + j + h._hjSettings.hjsv
                head.appendChild(scriptElement)
            })(window, document, 'https://static.hotjar.com/c/hotjar-', '.js?sv=')
        </script>
        <script>
            ;(function () {
                const key = window.Config?.GOOGLE_MAPS_API_KEY
                if (!key) {
                    console.error('Google Maps API key missing')
                    return
                }
                const script = document.createElement('script')
                script.src = `https://maps.googleapis.com/maps/api/js?key=${key}&loading=async`
                script.async = true
                script.defer = true
                document.head.appendChild(script)
            })()
        </script>
    </head>
    <body>
        <app-root></app-root>
    </body>
</html>
