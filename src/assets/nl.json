{"enums": {"declaration-kind": {"urn:api:sinistres:declaration:states:cree": "Concept", "urn:api:sinistres:declaration:states:soumis": "Ingediend", "urn:api:sinistres:declaration:states:abandonne": "Stopgezet"}, "origine-degats": {"CHEZ_MON_CLIENT": "<PERSON><PERSON><PERSON> <PERSON>", "CHEZ_MON_VOISIN": "<PERSON><PERSON><PERSON> <PERSON> buur van mijn klant", "AUTRE": "<PERSON><PERSON>"}, "code-garantie": {"ACC": "Verkeersongeval", "ASSV": "<PERSON><PERSON><PERSON>", "ASSN": "<PERSON><PERSON><PERSON>", "ASEP": "<PERSON><PERSON><PERSON>en", "ATTN": "Aanslagen en arbeidsconflicten", "BAG": "Gestolen of beschadigde bagage", "BAGN": "Gestolen of beschadigde bagage", "BDGA": "Glasbreuk auto", "TREN": "Machinebreuk", "BDGN": "Glasbreuk", "CNN": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CP": "Beschermde bestuurder", "PJN": "Verdediging en verhaal", "DGEN": "<PERSON><PERSON><PERSON> veroorzaakt door water", "DELN": "Elektrische schade", "DMS": "Eenvoudige materiële schade", "EVER": "Groene energie", "INCA": "Brand auto", "INCN": "Brand en aanverwante risico’s", "ASEA": "Bijstand bij mobiliteit", "JARD": "Pack tuin", "JAPI": "Pack tuin en zwembad", "PHYP": "Pack hypothecaire lening", "PPRO": "Pack pro", "PEXN": "Operationeel verlies", "PJFN": "Rechtsbescherming over de grenzen heen", "PJHB": "Rechtsbijstand woning", "PJVB": "Rechtsbijstand privéleven", "FPI": "Indemniteitsbescherming", "ARAG": "Juridische bescherming", "RCA": "Burgerlijke aansprakelijkheid auto", "RCIN": "Burgerlijke aansprakelijkheid gebouw", "RCN": "Burgerlijke aansprakelijkheid privéleven", "TGN": "<PERSON>, hagel, sneeuw", "VREP": "Gestalde voertuigen", "VOLN": "<PERSON><PERSON><PERSON>", "VOLA": "Diefstal auto"}, "filter-date": {"AUJOURDHUI": "Vandaag", "SEMAINE": "Deze week", "MOIS": "Deze maand"}, "zones-maison-exterieur": {"TOITURE": "Dak", "PORTE": "<PERSON><PERSON>", "FENETRE": "<PERSON><PERSON><PERSON>", "JARDIN": "<PERSON><PERSON>", "MOBILIER_JARDIN": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PISCINE": "Zwembad", "CLOTURE": "Omheining", "DEPENDANCE": "Bijgebouw", "FACADE": "Gevels"}, "zones-maison-interieur": {"COMBLES": "Zolder", "CHAMBRE": "Kamer/bureau", "SALLE_EAU": "Badkamer", "CUISINE": "Keuken", "SALON": "<PERSON><PERSON><PERSON><PERSON>", "SOUS_SOL": "<PERSON><PERSON>"}, "objets-de-dommage": {"BIJOUX": "<PERSON><PERSON><PERSON>", "DEGRADATION_IMMOBILIERE": "<PERSON><PERSON><PERSON> aan on<PERSON>erend goed", "MOBILIER": "Inboedel", "OBJETS_DE_VALEUR": "Waardevolle voorwerpen", "MACHINE_A_LAVER": "Wasmachine", "CHAUDIERE": "Verwarmingsketel", "LAVE_VAISSELLE": "Vaatwasmachine", "SECHE_LINGE": "<PERSON><PERSON><PERSON><PERSON>", "PLAQUE_DE_CUISSON": "Kookplaat", "REFRIGERATEUR": "Koelkast", "TELEVISION": "Televisie", "VITRES": "<PERSON><PERSON>", "PORTES_VITREE": "Glazen deur", "PLAQUE_VITROCERAMIQUE_OU_INDUCTION": "Kookplaat (keramisch/inductie)", "AUTRE": "<PERSON><PERSON>"}, "circonstance-key": {"ELECTRICITE_COURT_CIRCUIT": "Elektriciteit - Kortsluiting", "MAZOUT": "Stookolie", "CHANGEMENT_DE_TEMPERATURE": "Temperatuurverandering", "CHUTE_D_UN_ELEMENT_DU_BATIMENT": "<PERSON> van een onder<PERSON><PERSON> van het gebouw", "CHUTE_D_ARBRE": "<PERSON> van een boom", "DEGATS_DES_EAUX_CAUSES_AU_TIERS": "Waterschade veroorzaakt aan derden", "LESIONS_CORPORELLES_A_UN_TIERS": "<PERSON><PERSON><PERSON><PERSON><PERSON> aan een derde", "DOMMAGES_A_UN_VEHICULE_TIERS": "<PERSON><PERSON><PERSON> aan een voert<PERSON><PERSON> van <PERSON>", "DOMMAGES_A_UN_BATIMENT_TIERS": "<PERSON><PERSON><PERSON> aan een geb<PERSON><PERSON> van <PERSON>", "JOYRIDING": "Joyriding", "DEGRADATIONS_IMMOBILIERES": "<PERSON><PERSON><PERSON> aan eigendommen", "FOUDRE": "Bliksem", "EXPLOSION_IMPLOSION": "Explosie/implosie", "HEURT": "Stoot", "CHUTE_D_OBJETS": "Vallende voorwerpen", "DOMMAGES_AUX_PLANTATIONS_PAR_LES_ANIMAUX": "<PERSON><PERSON><PERSON> aan aanplantingen door dieren", "TEMPETE": "Storm", "GRELE": "<PERSON><PERSON>", "PRESSION_DE_LA_NEIGE": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CONFLIT_DU_TRAVAIL": "Arbeidsconflict", "ATTENTATS": "Aanslagen", "TREMBLEMENT_DE_TERRE": "Aardbeving", "OPACIFICATION": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BRIS_DE_VITRAGES": "Glasbreuk", "FUMEE_SUIE": "Rook - roet", "INFILTRATION_PAR_TOITURE": "Doorsijpeling via het dak", "DEFAUT_D_ETANCHEITE_D_UN_SANITAIRE": "G<PERSON><PERSON> aan waterdichtheid in een sanitaire unit", "DEGATS_DES_EAUX_PROVENANT_DU_VOISIN": "Waterschade veroorzaakt door een buur", "EFFRACTION": "<PERSON><PERSON><PERSON>", "VOL_DANS_UN_LOGEMENT_ETUDIANT_OU_TEMPORAIRE": "Diefstal uit studentenhuisvesting of tijdelijke accommodatie", "DEBORDEMENT": "Overstort", "INONDATION": "Overstroming", "MERULE": "Huiszwam", "MOUVEMENT_DE_TERRAIN": "Aardverschuiving", "RUPTURE_DE_CANALISATION": "Le<PERSON>breuk", "INCENDIE": "Brand", "REFOULEMENT_OU_DEBORDEMENT_D_EGOUTS": "Overlopen of opstu<PERSON> van riolen", "VOL_AVEC_AGRESSION": "<PERSON><PERSON><PERSON> met geweld", "VOL_OU_PERTE_DE_CLES": "<PERSON><PERSON><PERSON> of verl<PERSON> van s<PERSON>ls", "VOL_VEHICULE": "Diefstal voertuig", "AUTRE": "<PERSON><PERSON>"}, "pourcentage-tva": {"TRENTE_CINQ": "35%", "CINQUANTE": "50%", "CENT": "100%", "AUTRE": "<PERSON><PERSON>"}, "cause-sinistre": {"COLLISION_ENTRE_VEHICULES": "Collision entre véhicules", "COLLISION_AVEC_UN_OBJET_BATIMENT": "Collision avec un objet / bâtiment", "BRIS_DE_VITRE": "<PERSON><PERSON> de vitre", "VOL": "Vol", "VANDALISME": "Vandalisme", "INCENDIE": "<PERSON><PERSON><PERSON>", "DEGATS_CAUSES_PAR_UN_ANIMAL": "Dégâts causés par un animal", "COLLISION_AVEC_UNE_PERSONNE_OU_UN_VELO": "Collision avec une personne ou un vélo (usager faible)", "FORCE_DE_LA_NATURE": "Force de la nature"}, "type-animal": {"BLAIREAU": "<PERSON><PERSON>", "CERF": "<PERSON><PERSON>", "CHEVREUIL": "Chev<PERSON>uil", "RENARD": "<PERSON><PERSON>", "SANGLIER": "Sanglier", "AUTRE_ANIMAL": "Autre animal"}, "type-vol": {"VOL_TOTAL": "Vol total", "EFFRACTION": "Effraction"}, "type-force-nature": {"TEMPETE": "Tempête", "GRELE": "<PERSON><PERSON><PERSON><PERSON>", "INNONDATION": "Innondation"}, "element-vehicule-endommage": {"VITRE": "Vitre", "PHARE": "Ph<PERSON>", "RETROVISEUR": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "oui-non-inconnu": {"OUI": "O<PERSON>", "NON": "Non", "INCONNU": "Inconnu"}, "resultat-test": {"POSITIF": "<PERSON><PERSON><PERSON><PERSON>", "NEGATIF": "Négatif"}, "pays": {"AD": "<PERSON><PERSON><PERSON>", "AE": "Émirats Arabes Unis", "AF": "Afghanistan", "AG": "Antigua-et-Barbuda", "AI": "<PERSON><PERSON><PERSON>", "AL": "Albanie", "AM": "<PERSON><PERSON><PERSON>", "AO": "Angola", "AN": "Antilles néerlandaises", "AQ": "Antarctique", "AR": "Argentine", "AS": "Samoa Américaines", "AT": "<PERSON><PERSON><PERSON>", "AU": "Australie", "AW": "Aruba", "AX": "Îles Åland", "AZ": "Azerbaïdjan", "BA": "Bosnie-Herzégovine", "BB": "Barbade", "BD": "Bangladesh", "BE": "Belgique", "BF": "Burkina Faso", "BG": "Bulgarie", "BH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BI": "Burundi", "BJ": "<PERSON><PERSON><PERSON>", "BL": "Saint-Bart<PERSON><PERSON><PERSON><PERSON>", "BM": "<PERSON><PERSON><PERSON>", "BN": "<PERSON><PERSON><PERSON><PERSON>", "BO": "<PERSON><PERSON><PERSON>", "BQ": "Bonaire, Saint-Eustache et Saba", "BR": "Brésil", "BS": "Bahamas", "BT": "<PERSON><PERSON><PERSON>", "BV": "Île Bouvet", "BW": "Botswana", "BY": "Bélarus", "BZ": "Belize", "CA": "Canada", "CC": "Îles Cocos (Keeling)", "CD": "République Démocratique du Congo", "CF": "République Centrafricaine", "CG": "Congo", "CH": "Suisse", "CI": "Côte d'Ivoire", "CK": "Îles Cook", "CL": "<PERSON><PERSON>", "CM": "<PERSON><PERSON><PERSON>", "CN": "<PERSON>e", "CO": "<PERSON><PERSON><PERSON>", "CR": "Costa Rica", "CU": "Cuba", "CV": "Cap-Vert", "CW": "Curaçao", "CX": "Île Christmas", "CY": "Chypre", "CZ": "République Tchèque", "DE": "Allemagne", "DJ": "Djibouti", "DK": "Danemark", "DM": "<PERSON>", "DO": "République Dominicaine", "DZ": "Algérie", "EC": "Équateur", "EE": "Estonie", "EG": "Égypte", "EH": "Sahara Occidental", "ER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ES": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Éthiopie", "FI": "<PERSON><PERSON>", "FJ": "<PERSON><PERSON><PERSON>", "FK": "Îles Malouines", "FM": "États Fédérés de Micronésie", "FO": "Îles Féroé", "FR": "France", "GA": "Gabon", "GB": "Royaume-Uni", "GD": "Grenade", "GE": "Géorgie", "GF": "<PERSON><PERSON>", "GG": "Guernesey", "GH": "Ghana", "GI": "Gibraltar", "GL": "Groenland", "GM": "<PERSON><PERSON><PERSON>", "GN": "Guinée", "GP": "Guadeloupe", "GQ": "<PERSON><PERSON><PERSON>", "GR": "<PERSON><PERSON><PERSON><PERSON>", "GS": "Géorgie du Sud et les Îles Sandwich du Sud", "GT": "Guatemala", "GU": "Guam", "GW": "Guinée-Bissau", "GY": "Guyana", "HK": "Hong Kong", "HM": "Îles Heard et McDonald", "HN": "Honduras", "HR": "Croatie", "HT": "<PERSON><PERSON><PERSON>", "HU": "Hong<PERSON>", "ID": "Indonésie", "IE": "<PERSON><PERSON><PERSON>", "IL": "<PERSON><PERSON><PERSON>", "IM": "Île de Man", "IN": "Inde", "IO": "Territoire Britannique de l'Océan Indien", "IQ": "<PERSON><PERSON>", "IR": "Iran", "IS": "Islande", "IT": "Italie", "JE": "Jersey", "JM": "Jamaïque", "JO": "<PERSON><PERSON>", "JP": "Japon", "KE": "Kenya", "KG": "Kirghizistan", "KH": "<PERSON><PERSON><PERSON>", "KI": "Kiribati", "XK": "Kosovo", "KM": "Comores", "KN": "Saint-Kitts-et-Nevis", "KP": "Corée du Nord", "KR": "Corée du Sud", "KW": "<PERSON><PERSON><PERSON><PERSON>", "KY": "Îles Caïmanes", "KZ": "Kazakhstan", "LA": "Laos", "LB": "Liban", "LC": "Sainte-<PERSON><PERSON>", "LI": "Liechtenstein", "LK": "Sri Lanka", "LR": "Libéria", "LS": "Lesotho", "LT": "Lituanie", "LU": "Luxembourg", "LV": "<PERSON><PERSON><PERSON>", "LY": "Libye", "MA": "Maroc", "MC": "Monaco", "MD": "<PERSON><PERSON><PERSON>", "ME": "Monténégro", "MF": "<PERSON><PERSON><PERSON> (partie française)", "MG": "Madagascar", "MH": "Îles Marshall", "MK": "Macé<PERSON>ine du Nord", "ML": "Mali", "MM": "Myanmar", "MN": "<PERSON><PERSON>", "MO": "Macao", "MP": "Îles Mariannes du Nord", "MQ": "Martinique", "MR": "Mauri<PERSON><PERSON>", "MS": "Montserrat", "MT": "Malte", "MU": "<PERSON>", "MV": "Maldives", "MW": "Malawi", "MX": "Mexique", "MY": "Malaisie", "MZ": "Mozambique", "NA": "<PERSON><PERSON><PERSON>", "NC": "Nouvelle-Calédonie", "NE": "Niger", "NF": "Île Norfolk", "NG": "Nigéria", "NI": "Nicaragua", "NL": "Pays-Bas", "NO": "Norvège", "NP": "Népal", "NR": "Nauru", "NU": "<PERSON><PERSON>", "NZ": "Nouvelle-Zélande", "OM": "Oman", "PA": "Panama", "PE": "<PERSON><PERSON><PERSON>", "PF": "Polynésie Française", "PG": "Papouasie-Nouvelle-Guinée", "PH": "Philippines", "PK": "Pakistan", "PL": "Pologne", "PM": "Saint-Pierre-et-Miquelon", "PN": "Pitcairn", "PR": "Porto Rico", "PS": "Palestine", "PT": "Portugal", "PW": "<PERSON><PERSON><PERSON>", "PY": "Paraguay", "QA": "Qatar", "RE": "Réunion", "RO": "Roumanie", "RS": "<PERSON><PERSON>", "RU": "<PERSON><PERSON>", "RW": "Rwanda", "SA": "<PERSON><PERSON>", "SB": "Îles Salomon", "SC": "Seychelles", "SD": "<PERSON><PERSON><PERSON>", "SE": "<PERSON><PERSON><PERSON>", "SG": "Singapour", "SH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ascension et Tristan <PERSON>", "SI": "Slovénie", "SJ": "Svalbard et <PERSON>", "SK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SL": "Sierra Leone", "SM": "Saint-<PERSON>", "SN": "Sénégal", "SO": "<PERSON><PERSON>", "SR": "Suriname", "SS": "Soudan du Sud", "ST": "Sao Tomé-et-Principe", "SV": "El Salvador", "SX": "<PERSON><PERSON><PERSON> (partie néerlandaise)", "SY": "Syrie", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "TC": "Îles Turques-et-Caïques", "TD": "Tchad", "TF": "Terres Australes Françaises", "TG": "Togo", "TH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TJ": "Tadjikistan", "TK": "Tokelau", "TL": "Timor-Leste", "TM": "Turkménistan", "TN": "<PERSON><PERSON><PERSON>", "TO": "Tonga", "TR": "<PERSON><PERSON><PERSON><PERSON>", "TT": "Trinité-et-Tobago", "TA": "Tanzanie ", "TV": "Tuvalu", "TW": "Taïwan", "TZ": "Tanzanie", "UA": "Ukraine", "UG": "<PERSON><PERSON><PERSON>", "UM": "Îles Mineures Éloignées des États-Unis", "US": "États-Unis", "UY": "Uruguay", "UZ": "Ouzbékistan", "VA": "Saint-Siège (État de la Cité du Vatican)", "VC": "Saint-Vincent-et-les Grenadines", "VE": "Venezuela", "VG": "Îles Vierges Britanniques", "VI": "Îles Vierges des États-Unis", "VN": "<PERSON><PERSON><PERSON><PERSON>", "VU": "Vanuatu", "WF": "Wallis-et-Futuna", "WS": "Samoa", "YE": "<PERSON><PERSON><PERSON>", "YT": "Mayotte", "ZA": "Afrique du Sud", "ZM": "Zambie", "ZW": "Zimbabwe", "ZR": "<PERSON><PERSON><PERSON><PERSON>"}, "zones-dommages-auto": {"PARE_CHOCS_AVANT": "Pare-chocs avant", "CAPOT": "<PERSON><PERSON>", "PARE_BRISE": "Pare-brise", "AILE_AVANT_GAUCHE": "Aile avant gauche", "ROUE_AVANT_GAUCHE": "Roue avant gauche", "AILE_AVANT_DROITE": "Aile avant droite", "ROUE_AVANT_DROITE": "Roue avant droite", "PORTE_AVANT_GAUCHE": "Porte avant gauche", "VITRE_AVANT_GAUCHE": "Vitre avant gauche", "PORTE_AVANT_DROITE": "Porte avant droite", "VITRE_AVANT_DROITE": "Vitre avant droite", "TOIT_OUVRANT": "<PERSON>it ouvrant", "TOIT": "Toit", "PORTE_ARRIERE_GAUCHE": "Porte arrière gauche", "VITRE_ARRIERE_GAUCHE": "Vitre arrière gauche", "PORTE_ARRIERE_DROITE": "Porte arrière droite", "VITRE_ARRIERE_DROITE": "Vitre arrière droite", "AILE_ARRIERE_GAUCHE": "<PERSON>le arri<PERSON> gauche", "ROUE_ARRIERE_GAUCHE": "<PERSON><PERSON>e arrière gauche", "AILE_ARRIERE_DROITE": "<PERSON>le arri<PERSON> droite", "ROUE_ARRIERE_DROITE": "<PERSON><PERSON><PERSON> arrière droite", "LUNETTE_ARRIERE": "<PERSON><PERSON><PERSON>", "COFFRE": "<PERSON><PERSON><PERSON>", "PARE_CHOCS_ARRIERE": "Pare-chocs arrière", "DOMMAGES_INTERIEURS": "Dommages intérieurs", "SOUBASSEMENT": "Soubassement", "NON_DETERMINE": "Non déterminé"}, "type-immobilisation-vehicule": {"OUI_CHEZ_REPARATEUR": "<PERSON><PERSON>, chez le réparateur", "OUI_AILLEURS": "O<PERSON>, ailleurs", "NON": "Non"}}, "common": {"is-required": "Het veld {field} is verplicht", "is-option-required": "Selecteer een optie", "min": "Minimum: {min}", "max": "Maximaal: {max}", "selectionner-garanties": "Selecteer een wa<PERSON><PERSON>", "circonstance-sinistre": "<PERSON>ms<PERSON><PERSON><PERSON><PERSON><PERSON> van het schadegeval", "detail-dommage": "Kennisgeving", "pieces-jointes": "Bijlagen", "notification": {"save-declaration-error": "Fout bij het registreren van de aangifte", "save-declaration-success": "Aangifte succesvol geregistreerd", "circonstance-is-missing-in-parametrage": "Geen instellingen gevonden voor de omstandigheid", "declaration-sinistre-error": "Fout bij de aang<PERSON><PERSON> van het <PERSON>, probeer opnieuw", "pieces-jointes-error": "Fout in bijlagen. Corrigeer en probeer opnieuw.", "prestataire-service-error": "Erreur lors de la récupération des prestataires. Veuillez réessayer"}, "oui": "<PERSON>a", "non": "<PERSON><PERSON>", "true": "<PERSON>a", "false": "<PERSON><PERSON>", "facultatif": "facultatief", "caracteres-restants": "Resterende tekens", "caractere-restant": "Resterend teken", "loading": "Bezig met laden...", "precedent": "Vorige", "suivant": "Volgende", "prochaine-etape": "Volgende stap", "declarer-sinistre": "Schadegeval aangeven", "resume-declaration": "<PERSON><PERSON><PERSON> van de <PERSON>", "reprendre-plus-tard": "Later verdergaan", "enregistrer-brouillon": "Opslaan als concept", "blesse": "<PERSON><PERSON><PERSON>", "authentication": {"access-denied": "<PERSON>egang verboden", "access-denied-message": "Je hebt geen toegang tot deze pagina. Als het probleem zich blijft voordoen, neem dan contact op met de applicatieondersteuning <strong>3500</strong>.", "access-denied-action": "<PERSON><PERSON>a op<PERSON>w laden", "access-denied-action-dashboard": "Terug naar het dashboard", "session-expired": "<PERSON><PERSON> verlopen", "session-expired-message": "Het spijt ons, je sessie is verlopen. Je wordt doorgestuurd naar de authenticatiepagina. Als het probleem zich blijft voordoen, neem dan contact op met de applicatie-ondersteuning op <strong>3500</strong>.", "session-expired-action": "De authenticatiepagina laden"}, "risque-panel": {"numero": "Nr. {numeroRisque}", "depuis-le": "{codeProduit} - {typeRisque} - sinds {dateEffet}"}, "liste-declaration": {"modifie-le": "Gewijzigd op", "numero-police": "Polisnummer", "risque": "Risque", "nom-preneur": "Verzekeringnemer", "garanties": "Waarborgen", "circonstance": "Circonstance", "date-de-survenance": "<PERSON><PERSON> van het voor<PERSON>", "numero-dossier": "Do<PERSON><PERSON><PERSON><PERSON>", "statut": "Status", "abandonner-confirm-modal": {"title": "<PERSON>t g<PERSON><PERSON><PERSON> stopzetten", "etes-vous-sur": "Weet je zeker dat je de aangifte wil stopzetten?", "confirm": "Bevestigen", "cancel": "<PERSON><PERSON><PERSON>"}, "abandonner-error": "Fout bij het stop<PERSON><PERSON> van de aangifte", "aucune-declaration-trouvee": "<PERSON><PERSON> a<PERSON> g<PERSON>", "filter-bar-liste-declaration": {"search-placeholder": "Zoeken op overeenkomst", "col-garanties": {"title": "Waarborgen"}, "col-statut": {"title": "Status"}, "col-date-de-survenance": {"title": "<PERSON><PERSON> van het voor<PERSON>"}}}, "born": {"WOMEN": "Geboren op", "OTHER": "Geboren op"}, "years-old": "{nombre} jaren", "aucun-resultat-trouve": "geen resultaat gevonden", "pagination": {"lignes-par-page": "Regels per pagina", "pagination-boundaries": " {lowerBoundary} - {higherBoundary} van {totalRecords} items"}, "wizard": {"declarer-un-sinistre": "<PERSON><PERSON> s<PERSON> a<PERSON>n", "liste-des-declarations": "<PERSON><PERSON><PERSON> van verklaringen", "sur-de-continuer-confirm-modal": {"title": "Weet je zeker dat je wilt doorgaan?", "etes-vous-sur": "Als je dit venster sluit, verlies je alle niet-opgeslagen gegevens. Weet je zeker dat je wil doorgaan?", "confirm": "De aangifte verlaten", "cancel": "Annuleren en op de pagina blijven"}, "language": {"fr": "Fran<PERSON><PERSON>", "nl": "Nederlands"}}, "filter-bar": {"filtres": "Filters", "trie-par": "Gesorteerd op", "date": "Datum", "ordre-croissant": "stijgende volgorde", "fermer": "Sluiten", "reinitialiser": "Alle filters resetten"}, "details-sinistre-habitation": {"title": "Claimdetails", "present": "Was de klant aanwezig op het moment van de feiten?", "origine": "Waar bevindt zich de oorsprong van de scha<PERSON>?", "intervention-police-ou-pompiers": "Werd er beroep gedaan op de politie/Brandweer ?", "avec-numero-proces-verbal": "Werd er een proces-verbaal opgesteld?", "numero-proces-verbal": "<PERSON><PERSON><PERSON> van het proces-verbaal", "numero-proces-verbal-placeholder": "<PERSON><PERSON>r een nummer van het proces-verbaal in", "tiers-implique": "Was er een derde partij betrokken bij het schadegeval?"}, "circonstances-du-sinistres": {"title": "<PERSON>ms<PERSON><PERSON><PERSON><PERSON><PERSON> van het schadegeval", "circonstance-principale": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van het incident", "informations-complementaires-concernant-le-sinistre": "Aanvullende informatie over het scha<PERSON>geval", "informations-complementaires-concernant-le-sinistre-placeholder": "Je kunt de omstandigheden van het incident nader toelichten", "circonstance-is-required": "De omstandigheden van het schadegeval zijn vereist"}, "resume-sinistre-habitation": {"title": "Overzicht schadegeval", "modifier": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> wijzigen", "reference-producteur": "<PERSON><PERSON> referentie", "garantie-concernee": "Waarborg in kwestie"}, "resume-sinistre-auto": {"title": "Overzicht schadegeval", "modifier": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> wijzigen", "reference-producteur": "Référence producteur", "resume-detail-sinistre-auto": {"title": "Cause et détails du sinistre", "cause": "Cause", "details-sinistre": "<PERSON>é<PERSON> du sinistre"}}, "resume-circonstance": {"presence-informations-complementaires": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> van aanvullende informatie"}, "resume-tiers": {"liste-tiers": "Liste des tiers impliqués", "tiers": "Tiers {index}", "adresse": "<PERSON><PERSON><PERSON>"}, "resume-pieces-jointes": {"nombre-pieces-jointes": "Aantal bijlagen"}, "garanties-concernees": {"garantie-concernee": "Waarborg in kwestie", "autre-garantie": "<PERSON><PERSON>"}, "dommages-habitation": {"title": "<PERSON><PERSON><PERSON>", "degats-pieces": {"title": "Onderdelen die schade hebben opgelopen", "description": "Selecteer op het diagram de delen van het interieur van het huis die schade hebben opgelopen."}, "degats-exterieurs": {"title": "Buitenelementen die schade hebben opgelopen", "description": "Selecteer op het diagram de buitendelen van het huis die schade hebben opgelopen."}}, "objets-de-dommage": {"title": "Beschadigde voorwerpen", "autres-objets-de-dommage": {"objets-endommages": "Beschadigde voorwerpen", "ajouter-un-objet-de-dommage": "<PERSON><PERSON> s<PERSON> toe<PERSON>n", "ajouter-un-objet-de-dommage-placeholder": "het object toevoegen"}}, "file-transfer": {"server-error": "Fout bij het toevoegen van de bijlage. Probeer het opnieuw."}, "stepper-parcours-declaration": {"garanties": {"title": "Waarborgen", "sub-title": {"preneur": "Verzekeringnemer", "survenance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "garanties": "Waarborgen"}}, "circonstance": {"title": "<PERSON>ms<PERSON><PERSON><PERSON><PERSON><PERSON> van het schadegeval", "sub-title": {"detail-sinistre": "Details van het schadegeval", "circonstance-sinistre": "<PERSON>ms<PERSON><PERSON><PERSON><PERSON><PERSON> van het schadegeval", "preneur": "Verzekeringnemer", "survenance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cause-sinistre": "<PERSON><PERSON><PERSON><PERSON> van een ramp"}}, "dommages": {"title": "<PERSON><PERSON><PERSON>", "sub-title": {"elements-exterieurs": "Buitenelementen die schade hebben opgelopen", "pieces": "Beschadigde onderdelen", "objets-dommage": "Schadeobjecten", "autres-objets-dommage": "<PERSON><PERSON> schadeobjecten", "etat-vehicule": "<PERSON><PERSON><PERSON> van het voertuig", "localisation-dommages": "<PERSON><PERSON><PERSON>"}}, "garage": {"title": "Garage", "sub-title": {"trouver-garage": "Zoek een garage"}}, "modalites": {"title": "Modaliteiten", "sub-title": {"tva": "BTW", "compte-bancaire": "Bankrekening"}}, "pieces-jointes": {"title": "Bijlagen", "sub-title": {"pieces-jointes": "Bijlagen"}}}, "modalites": {"title": "Modaliteiten", "regime-tva": "BTW-regime", "preneur-soumis-tva": "Is de verzekeringsnemer BTW-plichtig?", "pourcentage-tva-recupere": "Kan het percentage BTW worden teruggevorderd?", "tva-6-pourcent": "Is er een 6% BTW van toepassing?", "compte-bancaire": "Bankrekening", "compte-bancaire-placeholder": "Voer uw IBAN in", "is-iban": "<PERSON>oer een geldige bankrekening in.", "tva": "BTW %"}, "survenance-habitation": {"title": "Survenance", "date-survenance": "Date de survenance", "date-survenance-format": "Date de survenance", "heure-survenance": "Heure de survenance"}, "survenance-auto": {"title": "Survenance", "date-survenance": "Date de survenance", "date-survenance-format": "Date de survenance", "heure-survenance": "Heure de survenance", "pays": "Pays", "lieu-survenance": "Lieu de survenance", "complement-adresse": "Complément d'adresse", "complement-adresse-placeholder": "Exemple: <PERSON><PERSON><PERSON>, <PERSON>, Tra<PERSON>il, ..."}, "cause-sinistre": {"title": "Cause du sinistre"}, "details-sinistre-auto": {"title": "<PERSON>é<PERSON> du sinistre", "intervention-police-ou-pompiers": "Y-a-t-il eu intervention de la police/pompiers ?", "avec-numero-proces-verbal": "Existe-t-il un numéro du procès verbal ?", "numero-proces-verbal": "N° du procès verbal ?", "numero-proces-verbal-placeholder": "Saisir un numéro de procès verbal", "conducteur-test-alcoolemie": "Conducteur soumis à un test d'alcoolémie ?", "taux-alcoolemie": "Taux d'alcoolémie", "taux-alcoolemie-placeholder": "mg/L d'air", "taux-alcoolemie-unite": "{value} mg/L d'air", "conducteur-test-stup": "Conducteur soumis à un test de stupéfiants ?", "resultat-stup": "Le résultat du test est-il positif ?", "type-animal": "Type d'animal", "nature-vol": "Nature du vol", "type-evenement": "Type d'événement", "element-vehicule-endommage": "Élément du véhicule endommagé", "tiers-implique": "Un tiers est-il impliqué dans le sinistre ?", "tiers-identifie": "Un tiers a-t-il été identifié ?"}, "tiers": {"title": "Liste des tiers impliqués", "nom": "Nom", "nom-placeholder": "<PERSON><PERSON> le nom", "prenom": "Prénom", "prenom-placeholder": "<PERSON><PERSON> le prénom", "numero-plaque": "Numéro <PERSON>", "numero-plaque-placeholder": "<PERSON><PERSON> le numéro de plaque", "marque-vehicule": "Marque du véhicule", "marque-vehicule-placeholder": "Saisir la marque du véhicule", "compagnie-adverse": "Compagnie <PERSON>", "compagnie-adverse-placeholder": "Saisir la compagnie adverse", "numero-contrat-assurance": "Numéro du contrat d'assurance", "numero-contrat-assurance-placeholder": "<PERSON><PERSON> le numéro du contrat d'assurance", "contrat-assurance-chez-compagnie-adverse": "Contrat d'assurance chez la compagnie adverse", "contrat-assurance-chez-compagnie-adverse-placeholder": "Saisir le contrat d'assurance chez la compagnie adverse", "tiers-blesse": "Le tiers a-t-il été blessé ?", "ajouter-tiers": "Ajouter le tiers", "ajouter-autre-tiers": "Ajouter un autre tiers", "modifier-tiers": "Modifier le tiers"}, "country-select": {"rechercher-pays": "<PERSON><PERSON><PERSON>"}, "dommages-auto": {"title": "Dommages", "localisation-dommages": {"title": "Localisation des dommages", "degats-anterieurs": "Dégats antérieurs", "zones-dommages-auto": {"title": "Zones dommages"}}}, "adresse": {"pays": "Pays", "code-postal": "Code postal", "localite": "Localité", "numero-rue": "<PERSON><PERSON><PERSON><PERSON>", "nom-rue": "Nom de la rue"}, "garage": {"title": "Garage", "trouver-un-garage": "Trouver un garage", "search-input-localite-or-garage-no-result-placeholder": "Aucun résultat", "aucun-garage-error": "Veuillez rechercher un garage ou le saisir manuellement", "presence-franchise-anglaise": "L'assuré bénéficiant d'une franchise anglaise dans le cadre de sa garantie Omnium,", "etes-vous-sure-garage-non-agree": "êtes-vous certain de vouloir sélectionner un garage non agréé ?", "recherche-garage": {"nom-localite-ou-code-postal-garage": "Nom du garage, localité ou code postal", "nom-localite-placeholder": "Nom du garage, localité ou code postal", "distance": "Distance", "distance-placeholder": "Sélectionner une distance"}, "garage-list": {"avantages-garage-agree-foyer": {"title": "Avantages du garage ag<PERSON><PERSON>", "service-rapide": {"title": "Service rapide : ", "description": "Bénéficiez d'un service rapide et d'une expertise accélérée."}, "mobilite-assuree": {"title": "Mobilité assurée : ", "description": "Vous recevez un véhicule de remplacement pendant la durée des réparations."}, "reparation-garantie": {"title": "Réparation de qualité : ", "description": "Profitez d'une réparation de qualité avec une garantie sur les travaux réalisés."}, "pas-avance-frais": {"title": "Pas d'avance sur les frais : ", "description": "Si le véhicule est couvert en omnium ou que l’assuré est en droit, Foyer paie directement la facture du réparateur, sauf franchise contractuelle applicable."}}, "aucun-garage-trouve": "Aucun garage trouvé", "je-souhaite-renseigner-manuellement-garage": "Je souhaite renseigner manuellement le garage", "garage-panel": {"badge-garage-agree-foyer": "Garage ag<PERSON>", "distance-in-km": "Distance : {distance} km"}}, "garage-autre": {"nom-garage": "Nom du garage"}}, "conducteur": {"title": "Conducteur", "le-conducteur-designe-au-contrat": "Le conducteur désigné au contrat", "une-autre-personne": "Une autre personne", "conducteur-blesse": "Le conducteur a-t-il été blessé ?", "conducteur-principal": "Conducteur principal", "conducteur-secondaire": "Conducteur secondaire", "conducteur-autre": {"nom": "Nom", "nom-placeholder": "Nom du conducteur", "prenom": "Prénom", "prenom-placeholder": "Prénom du conducteur", "date-de-naissance": "Date de naissance", "date-de-naissance-placeholder": "Date de naissance", "date-de-delivrance-permis": "Délivrance du permis de conduire", "date-de-delivrance-permis-placeholder": "Date de délivrance du permis de conduire"}}}, "pages": {"selection-risque": {"rechercher-une-police": "<PERSON>en polis zoeken", "ajouter-le-numero-police": "Het polisnummer toevoegen", "declaration-de-sinistre": "<PERSON><PERSON><PERSON><PERSON>", "rechercher-le-contrat": "Het contract zoeken", "date-survenance": "datum van het voorval", "date-survenance-format": "DD.MM.JJJJ", "risque-ne-peut-etre-selectionne": "Dit risico kan niet worden geselecteerd om een schadegeval aan te geven.", "notification": {"no-contrat-found": "Dit contract bestaat niet. Voer een geldig contractnummer in.", "no-risk-no-fun": "Selecteer een risico om verder te gaan met de aang<PERSON>e.", "search-contrat-technical-error": "Technisch probleem bij het zoeken naar een contract. Probeer het later nog eens.", "contrat-non-habitation": "Het polisnummer komt niet overeen met een woonpolis.", "declaration-auto-non-autorise": "Automatische aangifte niet toegestaan"}}, "garanties": {"reference-producteur": "<PERSON><PERSON><PERSON><PERSON>", "garanties-required": "Selecteer ten minste <PERSON><PERSON>", "garanties-group": {"organisme-responsable-garanties": {"la-garantie-geree-par": "De volgende waarborg wordt beheerd door ", "les-garanties-gerees-par": "De volgende waarborg wordt beheerd door ", "arag-belgium": {"title": "ARAG Belgium", "coordonnees": " Mailadres: <EMAIL>. Telefoon: +32 2 643 12 06"}, "europ-assistance": {"title": "Europ Assistance", "coordonnees": " Mailadres: <EMAIL>. Telefoon: +32 2 533 78 43"}, "foyer-arag-sa": {"title": "<PERSON><PERSON>er Arag S.A", "coordonnees": " Mailadres: <EMAIL>. Telefoon: +352 437 432 290"}}}}, "liste-declaration": {"title": "<PERSON><PERSON><PERSON>", "declarer-nouveau-sinistre": "<PERSON><PERSON> nieuw schade<PERSON> aangeven"}, "declaration-terminee": {"declaration-terminee-title": "De verklaring is compleet", "declaration-terminee-detail": "Je aangifte van het schadegeval is succesvol verwerkt. Een speciale beheerder neemt je dossier onder zijn hoede, met het dossiernummer <strong>{numeroDossierSinistre}</strong> als belangrijkste referentie voor alle toekomstige communicatie.", "declarer-nouveau-sinistre": "<PERSON><PERSON> nieuw schade<PERSON> aangeven", "voir-sinistres-declares": "De aangegeven schadegevallen bekijken"}, "pieces-jointes": {"pieces-justificatives": "Bewijsstukken", "description": "Je kunt de documenten die verband houden met het schadegeval hier indienen om ervoor te zorgen dat je verzoek efficiënt wordt verwerkt."}}}