{"enums": {"declaration-kind": {"urn:api:sinistres:declaration:states:cree": "Brouillon", "urn:api:sinistres:declaration:states:soumis": "<PERSON><PERSON><PERSON>", "urn:api:sinistres:declaration:states:abandonne": "Abandonné"}, "origine-degats": {"CHEZ_MON_CLIENT": "Chez mon client", "CHEZ_MON_VOISIN": "Chez le voisin de mon client", "AUTRE": "<PERSON><PERSON>"}, "code-garantie": {"ACC": "Accident de la circulation", "ASSV": "Assistance", "ASSN": "Assistance", "ASEP": "Mobilé Assistance personnes", "ATTN": "Attentats et conflits du travail", "BAG": "Bagages volés ou endommagés", "BAGN": "Bagages volés ou endommagés", "BDGA": "<PERSON>ris de glaces auto", "TREN": "<PERSON><PERSON> <PERSON> machine", "BDGN": "Bris de vitrages", "CNN": "Catastrophes naturelles", "CP": "Conducteur protégé", "PJN": "Défense et recours", "DGEN": "Dégâts des eaux", "DELN": "Dégâts électriques", "DMS": "Dégâts matériels simple", "EVER": "Energies vertes", "INCA": "Incendie auto", "INCN": "Incendie et connexes", "ASEA": "Mobilié assistance", "JARD": "Pack jardin", "JAPI": "Pack jardin et piscine", "PHYP": "Pack prêt hypothécaire\"", "PPRO": "Pack pro", "PEXN": "Perte d'exploitation", "PJFN": "PJ frontaliers", "PJHB": "PJ habitation", "PJVB": "PJ vie privée", "FPI": "Protection indemnitaire", "ARAG": "Protection juridique", "RCA": "Responsabilité civile automobile", "RCIN": "Responsabilité civile immeuble", "RCN": "Responsabilité civile vie privée", "TGN": "<PERSON><PERSON><PERSON><PERSON>, gr<PERSON><PERSON>, neige", "VREP": "Véhicules au repos", "VOLN": "Vol", "VOLA": "Vol automobile"}, "filter-date": {"AUJOURDHUI": "<PERSON><PERSON><PERSON>'hui", "SEMAINE": "<PERSON><PERSON> se<PERSON>", "MOIS": "Ce mois-ci"}, "zones-maison-exterieur": {"TOITURE": "Toiture", "PORTE": "Portes", "FENETRE": "Fenêtres", "JARDIN": "Jardin", "MOBILIER_JARDIN": "<PERSON><PERSON><PERSON> extérieur", "PISCINE": "Piscine", "CLOTURE": "Clôtures", "DEPENDANCE": "Dépendance", "FACADE": "Façades"}, "zones-maison-interieur": {"COMBLES": "Combles", "CHAMBRE": "Chambre / Bureau", "SALLE_EAU": "Salle d'eau", "CUISINE": "<PERSON><PERSON><PERSON><PERSON>", "SALON": "Salon / Séjour", "SOUS_SOL": "Sous-sol"}, "objets-de-dommage": {"BIJOUX": "Bijoux", "DEGRADATION_IMMOBILIERE": "Dégradation immobilière", "MOBILIER": "Mobilier", "OBJETS_DE_VALEUR": "Objets de valeurs", "MACHINE_A_LAVER": "Machine à laver", "CHAUDIERE": "<PERSON><PERSON><PERSON>", "LAVE_VAISSELLE": "<PERSON><PERSON> vaisselle", "SECHE_LINGE": "Sèche-linge", "PLAQUE_DE_CUISSON": "Plaque de cu<PERSON>on", "REFRIGERATEUR": "Réfrigérateur", "TELEVISION": "Télévision", "VITRES": "Vitres", "PORTES_VITREE": "Porte vitrée", "PLAQUE_VITROCERAMIQUE_OU_INDUCTION": "Plaque vitrocéramique / Induction", "AUTRE": "<PERSON><PERSON>"}, "circonstance-key": {"ELECTRICITE_COURT_CIRCUIT": "Electricité - court circuit", "MAZOUT": "Ma<PERSON><PERSON>", "CHANGEMENT_DE_TEMPERATURE": "Changement de température", "CHUTE_D_UN_ELEMENT_DU_BATIMENT": "Chute d'un élément du bâtiment", "CHUTE_D_ARBRE": "Chute d'arbre", "DEGATS_DES_EAUX_CAUSES_AU_TIERS": "Dégâts des eaux causés au tiers", "LESIONS_CORPORELLES_A_UN_TIERS": "Lésions corporelles à un tiers", "DOMMAGES_A_UN_VEHICULE_TIERS": "Dommages à un véhicule tiers", "DOMMAGES_A_UN_BATIMENT_TIERS": "Dommages à un bâtiment tiers", "JOYRIDING": "Joyriding", "DEGRADATIONS_IMMOBILIERES": "Dégradations immobilières", "FOUDRE": "Foudre", "EXPLOSION_IMPLOSION": "Explosion - Implosion", "HEURT": "<PERSON><PERSON>", "CHUTE_D_OBJETS": "Chute d'objets", "DOMMAGES_AUX_PLANTATIONS_PAR_LES_ANIMAUX": "Dommages aux plantations par les animaux", "TEMPETE": "Tempête", "GRELE": "<PERSON><PERSON><PERSON><PERSON>", "PRESSION_DE_LA_NEIGE": "Pression de la neige", "CONFLIT_DU_TRAVAIL": "Conflit du travail", "ATTENTATS": "Attentats", "TREMBLEMENT_DE_TERRE": "Tremblement de terre", "OPACIFICATION": "Opacification", "BRIS_DE_VITRAGES": "Bris de vitrages", "FUMEE_SUIE": "Fumée - Su<PERSON>", "INFILTRATION_PAR_TOITURE": "Infiltration par toiture", "DEFAUT_D_ETANCHEITE_D_UN_SANITAIRE": "Défaut d'étanchéïté d'un sanitaire", "DEGATS_DES_EAUX_PROVENANT_DU_VOISIN": "Dégâts des eaux provenant du voisin", "EFFRACTION": "Effraction", "VOL_DANS_UN_LOGEMENT_ETUDIANT_OU_TEMPORAIRE": "Vol dans un logement étudiant ou temporaire", "DEBORDEMENT": "Débordement", "INONDATION": "Inondation", "MERULE": "<PERSON><PERSON><PERSON><PERSON>", "MOUVEMENT_DE_TERRAIN": "Mouvement de terrain", "RUPTURE_DE_CANALISATION": "Rupture canalisation", "INCENDIE": "<PERSON><PERSON><PERSON>", "REFOULEMENT_OU_DEBORDEMENT_D_EGOUTS": "Refoulement ou débordement d'égouts", "VOL_AVEC_AGRESSION": "Vol avec agression", "VOL_OU_PERTE_DE_CLES": "Vol ou perte de clés", "VOL_VEHICULE": "Vol véhicule", "AUTRE": "<PERSON><PERSON>"}, "pourcentage-tva": {"TRENTE_CINQ": "35%", "CINQUANTE": "50%", "CENT": "100%", "AUTRE": "<PERSON><PERSON>"}, "cause-sinistre": {"COLLISION_ENTRE_VEHICULES": "Collision entre véhicules", "COLLISION_AVEC_UN_OBJET_BATIMENT": "Collision avec un objet / bâtiment", "BRIS_DE_VITRE": "<PERSON><PERSON> de vitre", "VOL": "Vol", "VANDALISME": "Vandalisme", "INCENDIE": "<PERSON><PERSON><PERSON>", "DEGATS_CAUSES_PAR_UN_ANIMAL": "Dégâts causés par un animal", "COLLISION_AVEC_UNE_PERSONNE_OU_UN_VELO": "Collision avec une personne ou un vélo (usager faible)", "FORCE_DE_LA_NATURE": "Force de la nature"}, "type-animal": {"BLAIREAU": "<PERSON><PERSON>", "CERF": "<PERSON><PERSON>", "CHEVREUIL": "Chev<PERSON>uil", "RENARD": "<PERSON><PERSON>", "SANGLIER": "Sanglier", "AUTRE_ANIMAL": "Autre animal"}, "type-vol": {"VOL_TOTAL": "Vol total", "EFFRACTION": "Effraction"}, "type-force-nature": {"TEMPETE": "Tempête", "GRELE": "<PERSON><PERSON><PERSON><PERSON>", "INNONDATION": "Innondation"}, "element-vehicule-endommage": {"VITRE": "Vitre", "PHARE": "Ph<PERSON>", "RETROVISEUR": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "oui-non-inconnu": {"OUI": "O<PERSON>", "NON": "Non", "INCONNU": "Inconnu"}, "resultat-test": {"POSITIF": "<PERSON><PERSON><PERSON><PERSON>", "NEGATIF": "Négatif"}, "pays": {"AD": "<PERSON><PERSON><PERSON>", "AE": "Émirats Arabes Unis", "AF": "Afghanistan", "AG": "Antigua-et-Barbuda", "AI": "<PERSON><PERSON><PERSON>", "AL": "Albanie", "AM": "<PERSON><PERSON><PERSON>", "AO": "Angola", "AN": "Antilles néerlandaises", "AQ": "Antarctique", "AR": "Argentine", "AS": "Samoa Américaines", "AT": "<PERSON><PERSON><PERSON>", "AU": "Australie", "AW": "Aruba", "AX": "Îles Åland", "AZ": "Azerbaïdjan", "BA": "Bosnie-Herzégovine", "BB": "Barbade", "BD": "Bangladesh", "BE": "Belgique", "BF": "Burkina Faso", "BG": "Bulgarie", "BH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BI": "Burundi", "BJ": "<PERSON><PERSON><PERSON>", "BL": "Saint-Bart<PERSON><PERSON><PERSON><PERSON>", "BM": "<PERSON><PERSON><PERSON>", "BN": "<PERSON><PERSON><PERSON><PERSON>", "BO": "<PERSON><PERSON><PERSON>", "BQ": "Bonaire, Saint-Eustache et Saba", "BR": "Brésil", "BS": "Bahamas", "BT": "<PERSON><PERSON><PERSON>", "BV": "Île Bouvet", "BW": "Botswana", "BY": "Bélarus", "BZ": "Belize", "CA": "Canada", "CC": "Îles Cocos (Keeling)", "CD": "République Démocratique du Congo", "CF": "République Centrafricaine", "CG": "Congo", "CH": "Suisse", "CI": "Côte d'Ivoire", "CK": "Îles Cook", "CL": "<PERSON><PERSON>", "CM": "<PERSON><PERSON><PERSON>", "CN": "<PERSON>e", "CO": "<PERSON><PERSON><PERSON>", "CR": "Costa Rica", "CU": "Cuba", "CV": "Cap-Vert", "CW": "Curaçao", "CX": "Île Christmas", "CY": "Chypre", "CZ": "République Tchèque", "DE": "Allemagne", "DJ": "Djibouti", "DK": "Danemark", "DM": "<PERSON>", "DO": "République Dominicaine", "DZ": "Algérie", "EC": "Équateur", "EE": "Estonie", "EG": "Égypte", "EH": "Sahara Occidental", "ER": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ES": "<PERSON><PERSON><PERSON><PERSON>", "ET": "Éthiopie", "FI": "<PERSON><PERSON>", "FJ": "<PERSON><PERSON><PERSON>", "FK": "Îles Malouines", "FM": "États Fédérés de Micronésie", "FO": "Îles Féroé", "FR": "France", "GA": "Gabon", "GB": "Royaume-Uni", "GD": "Grenade", "GE": "Géorgie", "GF": "<PERSON><PERSON>", "GG": "Guernesey", "GH": "Ghana", "GI": "Gibraltar", "GL": "Groenland", "GM": "<PERSON><PERSON><PERSON>", "GN": "Guinée", "GP": "Guadeloupe", "GQ": "<PERSON><PERSON><PERSON>", "GR": "<PERSON><PERSON><PERSON><PERSON>", "GS": "Géorgie du Sud et les Îles Sandwich du Sud", "GT": "Guatemala", "GU": "Guam", "GW": "Guinée-Bissau", "GY": "Guyana", "HK": "Hong Kong", "HM": "Îles Heard et McDonald", "HN": "Honduras", "HR": "Croatie", "HT": "<PERSON><PERSON><PERSON>", "HU": "Hong<PERSON>", "ID": "Indonésie", "IE": "<PERSON><PERSON><PERSON>", "IL": "<PERSON><PERSON><PERSON>", "IM": "Île de Man", "IN": "Inde", "IO": "Territoire Britannique de l'Océan Indien", "IQ": "<PERSON><PERSON>", "IR": "Iran", "IS": "Islande", "IT": "Italie", "JE": "Jersey", "JM": "Jamaïque", "JO": "<PERSON><PERSON>", "JP": "Japon", "KE": "Kenya", "KG": "Kirghizistan", "KH": "<PERSON><PERSON><PERSON>", "KI": "Kiribati", "XK": "Kosovo", "KM": "Comores", "KN": "Saint-Kitts-et-Nevis", "KP": "Corée du Nord", "KR": "Corée du Sud", "KW": "<PERSON><PERSON><PERSON><PERSON>", "KY": "Îles Caïmanes", "KZ": "Kazakhstan", "LA": "Laos", "LB": "Liban", "LC": "Sainte-<PERSON><PERSON>", "LI": "Liechtenstein", "LK": "Sri Lanka", "LR": "Libéria", "LS": "Lesotho", "LT": "Lituanie", "LU": "Luxembourg", "LV": "<PERSON><PERSON><PERSON>", "LY": "Libye", "MA": "Maroc", "MC": "Monaco", "MD": "<PERSON><PERSON><PERSON>", "ME": "Monténégro", "MF": "<PERSON><PERSON><PERSON> (partie française)", "MG": "Madagascar", "MH": "Îles Marshall", "MK": "Macé<PERSON>ine du Nord", "ML": "Mali", "MM": "Myanmar", "MN": "<PERSON><PERSON>", "MO": "Macao", "MP": "Îles Mariannes du Nord", "MQ": "Martinique", "MR": "Mauri<PERSON><PERSON>", "MS": "Montserrat", "MT": "Malte", "MU": "<PERSON>", "MV": "Maldives", "MW": "Malawi", "MX": "Mexique", "MY": "Malaisie", "MZ": "Mozambique", "NA": "<PERSON><PERSON><PERSON>", "NC": "Nouvelle-Calédonie", "NE": "Niger", "NF": "Île Norfolk", "NG": "Nigéria", "NI": "Nicaragua", "NL": "Pays-Bas", "NO": "Norvège", "NP": "Népal", "NR": "Nauru", "NU": "<PERSON><PERSON>", "NZ": "Nouvelle-Zélande", "OM": "Oman", "PA": "Panama", "PE": "<PERSON><PERSON><PERSON>", "PF": "Polynésie Française", "PG": "Papouasie-Nouvelle-Guinée", "PH": "Philippines", "PK": "Pakistan", "PL": "Pologne", "PM": "Saint-Pierre-et-Miquelon", "PN": "Pitcairn", "PR": "Porto Rico", "PS": "Palestine", "PT": "Portugal", "PW": "<PERSON><PERSON><PERSON>", "PY": "Paraguay", "QA": "Qatar", "RE": "Réunion", "RO": "Roumanie", "RS": "<PERSON><PERSON>", "RU": "<PERSON><PERSON>", "RW": "Rwanda", "SA": "<PERSON><PERSON>", "SB": "Îles Salomon", "SC": "Seychelles", "SD": "<PERSON><PERSON><PERSON>", "SE": "<PERSON><PERSON><PERSON>", "SG": "Singapour", "SH": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ascension et Tristan <PERSON>", "SI": "Slovénie", "SJ": "Svalbard et <PERSON>", "SK": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SL": "Sierra Leone", "SM": "Saint-<PERSON>", "SN": "Sénégal", "SO": "<PERSON><PERSON>", "SR": "Suriname", "SS": "Soudan du Sud", "ST": "Sao Tomé-et-Principe", "SV": "El Salvador", "SX": "<PERSON><PERSON><PERSON> (partie néerlandaise)", "SY": "Syrie", "SZ": "<PERSON><PERSON><PERSON><PERSON>", "TC": "Îles Turques-et-Caïques", "TD": "Tchad", "TF": "Terres Australes Françaises", "TG": "Togo", "TH": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TJ": "Tadjikistan", "TK": "Tokelau", "TL": "Timor-Leste", "TM": "Turkménistan", "TN": "<PERSON><PERSON><PERSON>", "TO": "Tonga", "TR": "<PERSON><PERSON><PERSON><PERSON>", "TT": "Trinité-et-Tobago", "TA": "Tanzanie ", "TV": "Tuvalu", "TW": "Taïwan", "TZ": "Tanzanie", "UA": "Ukraine", "UG": "<PERSON><PERSON><PERSON>", "UM": "Îles Mineures Éloignées des États-Unis", "US": "États-Unis", "UY": "Uruguay", "UZ": "Ouzbékistan", "VA": "Saint-Siège (État de la Cité du Vatican)", "VC": "Saint-Vincent-et-les Grenadines", "VE": "Venezuela", "VG": "Îles Vierges Britanniques", "VI": "Îles Vierges des États-Unis", "VN": "<PERSON><PERSON><PERSON><PERSON>", "VU": "Vanuatu", "WF": "Wallis-et-Futuna", "WS": "Samoa", "YE": "<PERSON><PERSON><PERSON>", "YT": "Mayotte", "ZA": "Afrique du Sud", "ZM": "Zambie", "ZW": "Zimbabwe", "ZR": "<PERSON><PERSON><PERSON><PERSON>"}, "zones-dommages-auto": {"PARE_CHOCS_AVANT": "Pare-chocs avant", "CAPOT": "<PERSON><PERSON>", "PARE_BRISE": "Pare-brise", "AILE_AVANT_GAUCHE": "Aile avant gauche", "ROUE_AVANT_GAUCHE": "Roue avant gauche", "AILE_AVANT_DROITE": "Aile avant droite", "ROUE_AVANT_DROITE": "Roue avant droite", "PORTE_AVANT_GAUCHE": "Porte avant gauche", "VITRE_AVANT_GAUCHE": "Vitre avant gauche", "PORTE_AVANT_DROITE": "Porte avant droite", "VITRE_AVANT_DROITE": "Vitre avant droite", "TOIT_OUVRANT": "<PERSON>it ouvrant", "TOIT": "Toit", "PORTE_ARRIERE_GAUCHE": "Porte arrière gauche", "VITRE_ARRIERE_GAUCHE": "Vitre arrière gauche", "PORTE_ARRIERE_DROITE": "Porte arrière droite", "VITRE_ARRIERE_DROITE": "Vitre arrière droite", "AILE_ARRIERE_GAUCHE": "<PERSON>le arri<PERSON> gauche", "ROUE_ARRIERE_GAUCHE": "<PERSON><PERSON>e arrière gauche", "AILE_ARRIERE_DROITE": "<PERSON>le arri<PERSON> droite", "ROUE_ARRIERE_DROITE": "<PERSON><PERSON><PERSON> arrière droite", "LUNETTE_ARRIERE": "<PERSON><PERSON><PERSON>", "COFFRE": "<PERSON><PERSON><PERSON>", "PARE_CHOCS_ARRIERE": "Pare-chocs arrière", "DOMMAGES_INTERIEURS": "Dommages intérieurs", "SOUBASSEMENT": "Soubassement", "NON_DETERMINE": "Non déterminé"}, "type-immobilisation-vehicule": {"OUI_CHEZ_REPARATEUR": "<PERSON><PERSON>, chez le réparateur", "OUI_AILLEURS": "O<PERSON>, ailleurs", "NON": "Non"}}, "common": {"is-required": "Le champs {field} est requis", "is-option-required": "Veuillez sélectionner une option", "min": "Minimum: {min}", "max": "Maximum: {max}", "selectionner-garanties": "Sélectionner les garanties", "circonstance-sinistre": "Circonstance du sinistre", "detail-dommage": "<PERSON><PERSON><PERSON> du dommage", "pieces-jointes": "Pièces jointes", "notification": {"save-declaration-error": "Erreur lors de l'enregistrement de la déclaration", "save-declaration-success": "Déclaration enregistrée avec succès", "circonstance-is-missing-in-parametrage": "Aucun paramétrage trouvé pour la circonstance", "declaration-sinistre-error": "Erreur lors de la déclaration du sinistre. Veuillez réessayer", "pieces-jointes-error": "Erreur au niveau des pièces jointes. Veuillez corriger et rééssayer", "prestataire-service-error": "Erreur lors de la récupération des prestataires. Veuillez réessayer"}, "oui": "O<PERSON>", "non": "Non", "true": "O<PERSON>", "false": "Non", "facultatif": "Facultatif", "caracteres-restants": "Caractères restants", "caractere-restant": "Caractère restant", "loading": "Chargement en cours...", "precedent": "Précedent", "suivant": "Suivant", "prochaine-etape": "Prochaine étape", "declarer-sinistre": "<PERSON><PERSON><PERSON><PERSON><PERSON> le sinistre", "resume-declaration": "Résumé de <PERSON> déc<PERSON>ation", "reprendre-plus-tard": "Reprendre plus tard", "enregistrer-brouillon": "Enregistrer le brouillon", "blesse": "<PERSON><PERSON><PERSON>", "authentication": {"access-denied": "Accès interdit", "access-denied-message": "L'accès à cette page vous a été interdit. Si le problème persiste, merci de contacter le support applicatif au <strong>3500</strong>.", "access-denied-action": "Recharger la page", "access-denied-action-dashboard": "Retour au dashboard", "session-expired": "Session expirée", "session-expired-message": "<PERSON>és<PERSON><PERSON>, votre session a expiré. Vous allez être rerédigé vers la page d'authentification. Si le problème persiste, merci de contacter le support applicatif au <strong>3500</strong>.", "session-expired-action": "Charger la page d'authentification"}, "risque-panel": {"numero": "N° {numeroRisque}", "depuis-le": "{codeProduit} - {typeRisque} - Depuis le {dateEffet}"}, "liste-declaration": {"modifie-le": "<PERSON><PERSON><PERSON><PERSON> le", "numero-police": "N° de police", "risque": "Risque", "nom-preneur": "Preneur", "garanties": "Garanties", "circonstance": "Circonstance", "date-de-survenance": "Date de survenance", "numero-dossier": "Numéro du dossier", "statut": "Statut", "abandonner-confirm-modal": {"title": "Abandonner la déclaration", "etes-vous-sur": "Êtes-vous sûr de vouloir abandonner la déclaration ?", "confirm": "Confirmer", "cancel": "Annuler"}, "abandonner-error": "Erreur lors de l'abandon de la déclaration", "aucune-declaration-trouvee": "<PERSON><PERSON>ne déclaration trouvée", "filter-bar-liste-declaration": {"search-placeholder": "Rechercher par contrat", "col-garanties": {"title": "Garanties"}, "col-statut": {"title": "Statut"}, "col-date-de-survenance": {"title": "Date de survenance"}}}, "born": {"WOMEN": "<PERSON><PERSON> le", "OTHER": "<PERSON><PERSON>"}, "years-old": "{nombre} ans", "aucun-resultat-trouve": "Aucun résultat trouvé", "pagination": {"lignes-par-page": "Lignes par page", "pagination-boundaries": " {lowerBoundary} - {higherBoundary} sur {totalRecords} éléments"}, "wizard": {"declarer-un-sinistre": "Déclarer un sinistre", "liste-des-declarations": "Liste des déclarations", "sur-de-continuer-confirm-modal": {"title": "Êtes-vous sûr de vouloir continuer ?", "etes-vous-sur": "Si vous fermez cette fenêtre, vous perdrez toutes les données non enregistrées. Êtes-vous sûr de vouloir continuer ?", "confirm": "<PERSON><PERSON><PERSON> la déclaration", "cancel": "Annuler et rester sur la page"}, "language": {"fr": "Fran<PERSON><PERSON>", "nl": "Nederlands"}}, "filter-bar": {"filtres": "Filtres", "trie-par": "Trié par", "date": "Date", "ordre-croissant": "ordre croissant", "fermer": "<PERSON><PERSON><PERSON>", "reinitialiser": "Rénitialiser tous les filtres"}, "details-sinistre-habitation": {"title": "<PERSON>é<PERSON> du sinistre", "present": "Le client était-il présent au moment des faits ?", "origine": "Quelle est l'origine des dégâts ?", "intervention-police-ou-pompiers": "Y-a-t-il eu intervention de la police/pompiers ?", "avec-numero-proces-verbal": "Existe-t-il un numéro du procès verbal ?", "numero-proces-verbal": "N° du procès verbal ?", "numero-proces-verbal-placeholder": "Saisir un numéro de procès verbal", "tiers-implique": "Un tiers est-il impliqué dans le sinistre ?"}, "circonstances-du-sinistres": {"title": "Circonstances du sinistre", "circonstance-principale": "Circonstance du sinistre", "informations-complementaires-concernant-le-sinistre": "Informations complémentaires concernant le sinistre", "informations-complementaires-concernant-le-sinistre-placeholder": "Vous pouvez détailler les circonstances du sinistre", "circonstance-is-required": "La circonstance du sinistre est requise"}, "resume-sinistre-habitation": {"title": "Résumé du sinistre", "modifier": "Modifier les détails du sinistre", "reference-producteur": "Référence producteur", "garantie-concernee": "Garantie <PERSON>"}, "resume-sinistre-auto": {"title": "Résumé du sinistre", "modifier": "Modifier les détails du sinistre", "reference-producteur": "Référence producteur", "resume-detail-sinistre-auto": {"title": "Cause et détails du sinistre", "cause": "Cause", "details-sinistre": "<PERSON>é<PERSON> du sinistre"}, "resume-conducteur": {"title": "Conducteur", "conducteur-designe": "Le conducteur désigné au contrat", "conducteur-blesse": "Le conducteur a-t-il été blessé ?", "le-conducteur": "Le conducteur", "date-de-naissance": "Date de naissance", "delivrance-permis": "Délivrance du permis de conduire", "adresse": "<PERSON><PERSON><PERSON>"}}, "resume-circonstance": {"presence-informations-complementaires": "Présence d'informations complémentaires"}, "resume-tiers": {"liste-tiers": "Liste des tiers impliqués", "tiers": "Tiers {index}", "adresse": "<PERSON><PERSON><PERSON>"}, "resume-pieces-jointes": {"nombre-pieces-jointes": "Nombre de pièces jointes"}, "garanties-concernees": {"garantie-concernee": "Garantie <PERSON>", "autre-garantie": "Autre garantie"}, "dommages-habitation": {"title": "Dommages", "degats-pieces": {"title": "Pièces ayant subi des dégats", "description": "Séléctionnez sur ce schéma les éléments intérieurs de l'habitation ayant subit des dégats."}, "degats-exterieurs": {"title": "Eléments extérieurs ayant subi des dégats", "description": "Sélectionnez sur ce schéma les éléments extérieurs de l'habitation ayant subit des dégats."}}, "objets-de-dommage": {"title": "Objets endom<PERSON>s", "autres-objets-de-dommage": {"objets-endommages": "Objets endom<PERSON>s", "ajouter-un-objet-de-dommage": "Ajouter l'objet", "ajouter-un-objet-de-dommage-placeholder": "Veuillez ajouter un objet"}}, "file-transfer": {"server-error": "Erreur lors de l'ajout de la pièce jointe. Veuillez réessayer."}, "stepper-parcours-declaration": {"garanties": {"title": "Garanties", "sub-title": {"preneur": "Preneur", "survenance": "Survenance", "garanties": "Garanties"}}, "circonstance": {"title": "Circonstances du sinistre", "sub-title": {"detail-sinistre": "<PERSON><PERSON><PERSON> du sinistre", "circonstance-sinistre": "Circonstance du sinistre", "preneur": "Preneur", "survenance": "Survenance", "cause-sinistre": "Cause du sinistre"}}, "dommages": {"title": "Dommages", "sub-title": {"elements-exterieurs": "Éléments extérieurs ayant subis des dommages", "pieces": "Pièces ayant subis des dommages", "objets-dommage": "Objets de dommage", "autres-objets-dommage": "Autre objets de dommage", "etat-vehicule": "Etat du véhicule", "localisation-dommages": "Localisation des dommages"}}, "garage": {"title": "Garage", "sub-title": {"trouver-garage": "Trouver un garage"}}, "modalites": {"title": "Modalités", "sub-title": {"tva": "TVA", "compte-bancaire": "Compte bancaire"}}, "pieces-jointes": {"title": "Pièces jointes", "sub-title": {"pieces-jointes": "Pièces jointes"}}}, "modalites": {"title": "Modalités", "regime-tva": "Régime TVA", "preneur-soumis-tva": "Le preneur est-il soumis à la TVA ?", "pourcentage-tva-recupere": "Le pourcentage de TVA peut etre récupéré ?", "tva-6-pourcent": "La TVA à 6% s'applique t-elle ?", "compte-bancaire": "Compte bancaire", "compte-bancaire-placeholder": "Numéro IBAN", "is-iban": "Veuillez saisir un compte bancaire valide.", "tva": "TVA %"}, "survenance-habitation": {"title": "Survenance", "date-survenance": "Date de survenance", "date-survenance-format": "Date de survenance", "heure-survenance": "Heure de survenance"}, "survenance-auto": {"title": "Survenance", "date-survenance": "Date de survenance", "date-survenance-format": "Date de survenance", "heure-survenance": "Heure de survenance", "pays": "Pays", "lieu-survenance": "Lieu de survenance", "complement-adresse": "Complément d'adresse", "complement-adresse-placeholder": "Exemple: <PERSON><PERSON><PERSON>, <PERSON>, Tra<PERSON>il, ..."}, "cause-sinistre": {"title": "Cause du sinistre"}, "details-sinistre-auto": {"title": "<PERSON>é<PERSON> du sinistre", "intervention-police-ou-pompiers": "Y-a-t-il eu intervention de la police/pompiers ?", "avec-numero-proces-verbal": "Existe-t-il un numéro du procès verbal ?", "numero-proces-verbal": "N° du procès verbal ?", "numero-proces-verbal-placeholder": "Saisir un numéro de procès verbal", "conducteur-test-alcoolemie": "Conducteur soumis à un test d'alcoolémie ?", "taux-alcoolemie": "Taux d'alcoolémie", "taux-alcoolemie-placeholder": "mg/L d'air", "taux-alcoolemie-unite": "{value} mg/L d'air", "conducteur-test-stup": "Conducteur soumis à un test de stupéfiants ?", "resultat-stup": "Le résultat du test est-il positif ?", "type-animal": "Type d'animal", "nature-vol": "Nature du vol", "type-evenement": "Type d'événement", "element-vehicule-endommage": "Élément du véhicule endommagé", "tiers-implique": "Un tiers est-il impliqué dans le sinistre ?", "tiers-identifie": "Un tiers a-t-il été identifié ?"}, "tiers": {"title": "Liste des tiers impliqués", "nom": "Nom", "nom-placeholder": "<PERSON><PERSON> le nom", "prenom": "Prénom", "prenom-placeholder": "<PERSON><PERSON> le prénom", "numero-plaque": "Numéro <PERSON>", "numero-plaque-placeholder": "<PERSON><PERSON> le numéro de plaque", "marque-vehicule": "Marque du véhicule", "marque-vehicule-placeholder": "Saisir la marque du véhicule", "compagnie-adverse": "Compagnie <PERSON>", "compagnie-adverse-placeholder": "Saisir la compagnie adverse", "numero-contrat-assurance": "Numéro du contrat d'assurance", "numero-contrat-assurance-placeholder": "<PERSON><PERSON> le numéro du contrat d'assurance", "contrat-assurance-chez-compagnie-adverse": "Contrat d'assurance chez la compagnie adverse", "contrat-assurance-chez-compagnie-adverse-placeholder": "Saisir le contrat d'assurance chez la compagnie adverse", "tiers-blesse": "Le tiers a-t-il été blessé ?", "ajouter-tiers": "Ajouter le tiers", "ajouter-autre-tiers": "Ajouter un autre tiers", "modifier-tiers": "Modifier le tiers"}, "country-select": {"rechercher-pays": "<PERSON><PERSON><PERSON>"}, "dommages-auto": {"title": "Dommages", "localisation-dommages": {"title": "Localisation des dommages", "degats-anterieurs": "Dégats antérieurs", "zones-dommages-auto": {"title": "Zones dommages"}}}, "adresse": {"pays": "Pays", "code-postal": "Code postal", "localite": "Localité", "numero-rue": "<PERSON><PERSON><PERSON><PERSON>", "nom-rue": "Nom de la rue"}, "garage": {"title": "Garage", "trouver-un-garage": "Trouver un garage", "search-input-localite-or-garage-no-result-placeholder": "Aucun résultat", "aucun-garage-error": "Veuillez rechercher un garage ou le saisir manuellement", "presence-franchise-anglaise": "L'assuré bénéficiant d'une franchise anglaise dans le cadre de sa garantie Omnium,", "etes-vous-sure-garage-non-agree": "êtes-vous certain de vouloir sélectionner un garage non agréé ?", "recherche-garage": {"nom-localite-ou-code-postal-garage": "Nom du garage, localité ou code postal", "nom-localite-placeholder": "Nom du garage, localité ou code postal", "distance": "Distance", "distance-placeholder": "Sélectionner une distance"}, "garage-list": {"avantages-garage-agree-foyer": {"title": "Avantages du garage ag<PERSON><PERSON>", "service-rapide": {"title": "Service rapide : ", "description": "Bénéficiez d'un service rapide et d'une expertise accélérée."}, "mobilite-assuree": {"title": "Mobilité assurée : ", "description": "Vous recevez un véhicule de remplacement pendant la durée des réparations."}, "reparation-garantie": {"title": "Réparation de qualité : ", "description": "Profitez d'une réparation de qualité avec une garantie sur les travaux réalisés."}, "pas-avance-frais": {"title": "Pas d'avance sur les frais : ", "description": "Si le véhicule est couvert en omnium ou que l’assuré est en droit, Foyer paie directement la facture du réparateur, sauf franchise contractuelle applicable."}}, "aucun-garage-trouve": "Aucun garage trouvé", "je-souhaite-renseigner-manuellement-garage": "Je souhaite renseigner manuellement le garage", "garage-panel": {"badge-garage-agree-foyer": "Garage ag<PERSON>", "distance-in-km": "Distance : {distance} km"}}, "garage-autre": {"nom-garage": "Nom du garage"}}, "conducteur": {"title": "Conducteur", "le-conducteur-designe-au-contrat": "Le conducteur désigné au contrat", "une-autre-personne": "Une autre personne", "conducteur-blesse": "Le conducteur a-t-il été blessé ?", "conducteur-principal": "Conducteur principal", "conducteur-secondaire": "Conducteur secondaire", "conducteur-autre": {"nom": "Nom", "nom-placeholder": "Nom du conducteur", "prenom": "Prénom", "prenom-placeholder": "Prénom du conducteur", "date-de-naissance": "Date de naissance", "date-de-naissance-placeholder": "Date de naissance", "date-de-delivrance-permis": "Délivrance du permis de conduire", "date-de-delivrance-permis-placeholder": "Date de délivrance du permis de conduire"}}}, "pages": {"selection-risque": {"rechercher-une-police": "Numéro de police", "ajouter-le-numero-police": "Ajouter le numero de police", "declaration-de-sinistre": "Déclaration de sinistre", "rechercher-le-contrat": "Rechercher le contrat", "date-survenance": "Date de survenance", "date-survenance-format": "JJ.MM.AAAA", "risque-ne-peut-etre-selectionne": "Ce risque ne peut être sélectionné pour déclarer un sinistre.", "notification": {"no-contrat-found": "Ce contrat n'existe pas. Veuillez encoder un numéro de contrat valide.", "no-risk-no-fun": "Veuillez sélectionner un risque pour pouvoir continuer la déclaration", "search-contrat-technical-error": "Problème technique lors de la recherche de contrat. Veuillez réessayer plus tard", "contrat-inconnu": "Le numéro de police ne correspond pas à un contrat habitation ou auto.", "declaration-auto-non-autorise": "Déclaration auto non autorisé"}}, "garanties": {"reference-producteur": "Référence producteur", "garanties-required": "Veuillez sélectionner au moins une garantie", "garanties-group": {"organisme-responsable-garanties": {"la-garantie-geree-par": "La garantie suivante est gérée par ", "les-garanties-gerees-par": "Les garanties suivantes sont gérées par ", "arag-belgium": {"title": "ARAG Belgium", "coordonnees": " Adresse e-mail: <EMAIL>. Téléphone : +32 2 643 12 06"}, "europ-assistance": {"title": "Europ Assistance", "coordonnees": " Adresse e-mail: <EMAIL>. Téléphone : +32 2 533 78 43"}, "foyer-arag-sa": {"title": "<PERSON><PERSON>er Arag S.A", "coordonnees": " Adresse e-mail: <EMAIL>. Téléphone : +352 437 432 290"}}}}, "liste-declaration": {"title": "Liste des déclarations", "declarer-nouveau-sinistre": "Déclarer un nouveau sinistre"}, "declaration-terminee": {"declaration-terminee-title": "La déclaration est terminée", "declaration-terminee-detail": "Votre déclaration de sinistre a été enregistrée avec succès. Un gestionnaire dédié prendra en charge votre dossier, avec le numéro de dossier <strong>{numeroDossierSinistre}</strong> comme référence principale pour toutes les communications à venir.", "declarer-nouveau-sinistre": "Déclarer un nouveau sinistre", "voir-sinistres-declares": "Voir les sinistres déclarés"}, "pieces-jointes": {"pieces-justificatives": "Pièces justificatives", "description": "Vous pouvez déposer les documents relatifs au sinistre ici pour permettre un traitement efficace de votre demande."}}}