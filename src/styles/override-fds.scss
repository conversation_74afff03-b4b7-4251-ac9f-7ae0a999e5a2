.Table-responsive-container {
    border-radius: 4px;
    box-shadow: 0 0 0 1px var(--fds-feather-grey-300);
    background-image: none;
}

.Pagination {
    .Select,
    .SelectAdvanced {
        --Select-min-height_priv: 40px;
    }
}

.Table {
    --Table-v-padding: 6px;
    --Table-h-padding: 16px;
    table-layout: auto;
    width: 100%;

    thead {
        height: 32px;

        th {
            color: var(--fds-feather-grey-700);
            font-weight: 700;
            font-size: 10px;
            line-height: 16px;
            letter-spacing: 0.5px;
            white-space: nowrap;

            &:not([scope='row']) {
                font-size: 10px;
            }

            &:last-child {
                padding-right: var(--Table-h-padding);
            }

            &:first-child {
                padding-left: var(--Table-h-padding);
            }

            &:has(> i) {
                cursor: pointer;
            }

            div {
                justify-content: right;
            }
        }

        .dossier-col,
        .date-col,
        .montant-col {
            width: 120px;
        }
    }

    tbody {
        .table-line {
            display: contents;
        }

        tr {
            .truncated-cell {
                text-overflow: ellipsis;
                white-space: nowrap;
                overflow: hidden;
                max-width: 0;
            }

            td {
                color: var(--fds-feather-grey-800);
                font-weight: 500;
                font-size: 14px;
                line-height: 20px;

                &:last-child {
                    padding-right: var(--Table-h-padding);
                }

                &:first-child {
                    padding-left: var(--Table-h-padding);
                }
            }
        }
    }

    &.has-odd-background thead tr th {
        --Table-cell-background: var(--fds-primary-100);
    }

    &.has-odd-background tbody tr:nth-of-type(even) td:not(.is-empty),
    &.has-odd-background
        tbody
        .table-line:nth-of-type(even)
        tr
        td:not(.is-empty) {
        --Table-cell-background: var(--fds-feather-grey-100);
    }
}

.Form-field-label {
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
}

.Form {
    padding: 0;
}

.LAYOUT-SECTION {
    height: 100%;
    display: flex;
    align-items: center;
}

.GridFlex-row {
    justify-content: center;
}
