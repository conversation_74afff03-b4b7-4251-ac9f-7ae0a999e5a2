@use 'foyer-design-system/packages/MaterialIcons/src/variables' as icons;
@use 'foyer-design-system/packages/CORE/src/variables' as var;

.cdk-overlay-container {
    z-index: var.$modal-z-index + 1;

    mat-datepicker-content {
        mat-calendar {
            .mat-calendar-table-header {
                & > tr > th {
                    font-family: 'Barlow', serif;
                    font-size: 12px;
                    color: var(--fds-feather-grey-700);
                }
            }

            .mat-button-wrapper,
            .mat-calendar-body-label,
            .mat-calendar-body-cell-content,
            .mat-calendar-body-disabled
                > .mat-calendar-body-cell-content:not(
                    .mat-calendar-body-selected
                ) {
                font-family: 'Barlow', serif;
                font-weight: var.$font-weight-bold;
                color: var(--fds-feather-grey-800);
                text-transform: lowercase;
                &::first-letter {
                    text-transform: uppercase;
                }
            }

            .mat-calendar-header {
                padding-top: 0;

                .mat-button-wrapper {
                    display: inline-block;
                }
                .mat-calendar-period-button {
                    border-radius: 50px;
                }
            }

            .mat-calendar-body-label {
                font-size: 12px;
            }

            .mat-calendar-body-today:not(.mat-calendar-body-selected) {
                border-color: var(--fds-feather-grey-800);
            }

            .mat-calendar-body-cell {
                &.mat-calendar-body-disabled {
                    .mat-calendar-body-cell-content {
                        opacity: 0.4;
                    }
                }

                &.mat-calendar-body-active {
                    .mat-calendar-body-cell-content:not(
                            .mat-calendar-body-selected
                        ) {
                        background: var(--fds-white) !important;
                    }
                }

                .mat-calendar-body-cell-content {
                    font-family: 'Barlow', serif;
                    font-size: 14px;
                    font-weight: var.$font-weight-medium;
                    color: var(--fds-feather-grey-800);
                }

                .mat-calendar-body-selected {
                    color: var(--fds-white);
                    background-color: var(--fds-info-700);
                    &.mat-calendar-body-today {
                        box-shadow: inset 0 0 0 2px var(--fds-white);
                    }
                }

                &:not(.mat-calendar-body-disabled) {
                    &:hover {
                        .mat-calendar-body-cell-content {
                            &:not(.mat-calendar-body-selected) {
                                background-color: var(
                                    --fds-feather-grey-200
                                ) !important;
                            }
                        }
                    }
                }
            }
        }
    }
}

mat-datepicker-toggle {
    &.FormGroup-field-addon {
        z-index: var.$field-z-index-state + 1;
    }
    .mat-button-wrapper {
        &::before {
            content: icons.$mi-insert_invitation;
            font-family: 'MaterialIcons', sans-serif;
            font-size: 24px;
        }
        svg {
            display: none;
        }
    }
    .mat-button-focus-overlay {
        opacity: 0 !important;
    }
}

.Input.mat-date-range-input {
    .mat-date-range-input-container {
        height: 100%;
        min-height: var(--Input-min-height);
    }
}
