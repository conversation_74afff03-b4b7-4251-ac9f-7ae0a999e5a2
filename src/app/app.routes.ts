import { Routes } from '@angular/router'
import { RoutingPath } from './routing-path.enum'
import { AuthGuard } from '@foyer/authentication'
import { DeclarationKind } from './shared/enums/declaration-kind.enum'

export const routes: Routes = [
    { path: '', redirectTo: RoutingPath.SELECTION_RISQUE, pathMatch: 'full' },

    {
        path: RoutingPath.SELECTION_RISQUE,
        canActivate: [AuthGuard],
        loadComponent: () =>
            import(
                './shared/pages/selection-risque-page/selection-risque-page.component'
            ).then((m) => m.SelectionRisquePageComponent),
    },
    {
        path: RoutingPath.LISTE_DECLARATION,
        canActivate: [AuthGuard],
        loadComponent: () =>
            import(
                './shared/pages/liste-declaration-page/liste-declaration-page.component'
            ).then((m) => m.ListeDeclarationPageComponent),
    },

    {
        path: RoutingPath.HABITATION,
        canActivate: [AuthGuard],
        children: [
            {
                path: RoutingPath.GARANTIES,
                loadComponent: () =>
                    import(
                        './habitation/pages/garanties-page/garanties-page.component'
                    ).then((m) => m.GarantiesPageComponent),
                data: { declarationKind: DeclarationKind.HABITATION },
            },
            {
                path: RoutingPath.CIRCONSTANCE,
                loadComponent: () =>
                    import(
                        './habitation/pages/circonstance-habitation-page/circonstance-habitation-page.component'
                    ).then((m) => m.CirconstanceHabitationPageComponent),
            },
            {
                path: RoutingPath.DOMMAGES,
                loadComponent: () =>
                    import(
                        './habitation/pages/dommages-habitation-page/dommages-habitation-page.component'
                    ).then((m) => m.DommagesHabitationPageComponent),
            },
            {
                path: RoutingPath.MODALITES,
                loadComponent: () =>
                    import(
                        './habitation/pages/modalites-habitation-page/modalites-habitation-page.component'
                    ).then((m) => m.ModalitesHabitationPageComponent),
            },
            {
                path: RoutingPath.PIECES_JOINTES,
                loadComponent: () =>
                    import(
                        './habitation/pages/pieces-jointes-habitation-page/pieces-jointes-habitation-page.component'
                    ).then((m) => m.PiecesJointesHabitationPageComponent),
            },
            {
                path: RoutingPath.DECLARATION_TERMINEE,
                loadComponent: () =>
                    import(
                        './shared/pages/declaration-terminee-page/declaration-terminee-page.component'
                    ).then((m) => m.DeclarationTermineePageComponent),
                data: { declarationKind: DeclarationKind.HABITATION },
            },
        ],
    },

    {
        path: RoutingPath.AUTO,
        canActivate: [AuthGuard],
        children: [
            {
                path: RoutingPath.CIRCONSTANCE,
                loadComponent: () =>
                    import(
                        './auto/pages/circonstances-auto-page/circonstances-auto-page.component'
                    ).then((m) => m.CirconstancesAutoPageComponent),
            },
            {
                path: RoutingPath.DOMMAGES,
                loadComponent: () =>
                    import(
                        './auto/pages/dommages-auto-page/dommages-auto-page.component'
                    ).then((m) => m.DommagesAutoPageComponent),
            },
            {
                path: RoutingPath.GARAGE,
                loadComponent: () =>
                    import(
                        './auto/pages/garage-page/garage-page.component'
                    ).then((m) => m.GaragePageComponent),
            },
            {
                path: RoutingPath.MODALITES,
                loadComponent: () =>
                    import(
                        './auto/pages/modalites-auto-page/modalites-auto-page.component'
                    ).then((m) => m.ModalitesAutoPageComponent),
            },
            {
                path: RoutingPath.PIECES_JOINTES,
                loadComponent: () =>
                    import(
                        './auto/pages/pieces-jointes-auto-page/pieces-jointes-auto-page.component'
                    ).then((m) => m.PiecesJointesAutoPageComponent),
            },
            {
                path: RoutingPath.DECLARATION_TERMINEE,
                loadComponent: () =>
                    import(
                        './shared/pages/declaration-terminee-page/declaration-terminee-page.component'
                    ).then((m) => m.DeclarationTermineePageComponent),
                data: { declarationKind: DeclarationKind.AUTO },
            },
        ],
    },

    {
        path: RoutingPath.ALL_PATH,
        redirectTo: RoutingPath.SELECTION_RISQUE,
        pathMatch: 'full',
    },
]
