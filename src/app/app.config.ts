import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core'
import {
    PreloadAllModules,
    provideRouter,
    withComponentInputBinding,
    withInMemoryScrolling,
    withPreloading,
} from '@angular/router'
import { routes } from './app.routes'
import { provideAnimations } from '@angular/platform-browser/animations'
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http'
import { AuthenticationConfiguration } from '@foyer/authentication'
import { provideEnvironmentNgxMask } from 'ngx-mask'
import { OAuthStorage } from 'angular-oauth2-oidc'
import { ENVIRONMENT, Environment, environmentFactory } from './app.environment'
import { provideTranslation } from './core/providers/translation.provider'
import { provideAuth } from './core/providers/auth.provider'
import { provideDate } from './core/providers/date.provider'
import { inputFileProvider } from './core/providers/input-file.provider'
import { LocationStrategy, PathLocationStrategy } from '@angular/common'

export const authenticationConfigurationFactory = (
    env: Environment,
    basePath: string
): AuthenticationConfiguration =>
    AuthenticationConfiguration.of({
        ...env.authentication,
        postLogoutRedirectUri: '/',
        redirectUri:
            (basePath === '/' ? window.location.origin : basePath) + '/',
    })

export const sessionStorageFactory = (): Storage => sessionStorage

export const appConfig: ApplicationConfig = {
    providers: [
        provideZoneChangeDetection({ eventCoalescing: true }),
        provideRouter(
            routes,
            withComponentInputBinding(),
            withPreloading(PreloadAllModules),
            withInMemoryScrolling({ scrollPositionRestoration: 'enabled' })
        ),
        provideAnimations(),
        provideHttpClient(withInterceptorsFromDi()),
        provideEnvironmentNgxMask(),
        provideTranslation(),
        provideAuth(),
        provideDate(),
        inputFileProvider,
        { provide: ENVIRONMENT, useFactory: environmentFactory },
        { provide: LocationStrategy, useClass: PathLocationStrategy },
        {
            provide: OAuthStorage,
            useFactory: sessionStorageFactory,
        },
    ],
}
