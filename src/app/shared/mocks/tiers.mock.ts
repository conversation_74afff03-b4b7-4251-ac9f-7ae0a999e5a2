import { OuiNonInconnu } from '../enums/oui-non-inconnu.enum'
import { adresseFRMock } from './refAdresseMock'
import { Tiers } from '../models/tiers'

export const singleTiersMock = (): Tiers => ({
    nom: '<PERSON><PERSON><PERSON>',
    prenom: '<PERSON><PERSON>',
    numeroPlaque: '123',
    compagnieAdverse: 'AXA',
    contratAssuranceChezCompagnieAdverse: 'Contrat',
})

export const multipleTiersMock = (): Tiers[] => [
    singleTiersMock(),
    {
        nom: 'Dupont',
        prenom: 'Jean',
        numeroPlaque: 'AB-123-CD',
        compagnieAdverse: 'Assurance XYZ',
        contratAssuranceChezCompagnieAdverse: '987654321',
        adresse: adresseFRMock(),
        tiersBlesse: OuiNonInconnu.OUI,
    },
]
