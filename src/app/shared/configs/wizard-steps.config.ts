import { WizardStepConfig } from '../models/wizard-step-config'
import { WizardSteps } from '../enums/wizard-steps.enum'
import { WizardAction } from '../enums/wizard-action.enum'
import { RoutingPath } from '../../routing-path.enum'

export const baseWizardStepConfigs: Record<WizardSteps, WizardStepConfig> = {
    [WizardSteps.SELECTION_RISQUE]: {
        step: WizardSteps.SELECTION_RISQUE,
        nextStepTitle: 'common.selectionner-garanties',
        isShowNavigation: true,
        isShowMenuTabs: true,
        actions: [
            WizardAction.HIDE_PREVIOUS,
            WizardAction.HIDE_SECONDARY,
            WizardAction.SHOW_NEXT,
            WizardAction.ENABLE_NEXT,
        ],
        routePath: RoutingPath.SELECTION_RISQUE,
    },
    [WizardSteps.GARANTIES]: {
        step: WizardSteps.GARANTIES,
        secondaryStepTitle: 'common.enregistrer-brouillon',
        secondaryStepSubTitle: 'common.reprendre-plus-tard',
        secondaryStepIcon: 'mi-file_document_outline',
        nextStepTitle: 'common.circonstance-sinistre',
        isShowNavigation: true,
        isShowMenuTabs: true,
        actions: [
            WizardAction.SHOW_PREVIOUS,
            WizardAction.SHOW_SECONDARY,
            WizardAction.SHOW_NEXT,
            WizardAction.ENABLE_NEXT,
        ],
        routePath: RoutingPath.GARANTIES,
    },
    [WizardSteps.CIRCONSTANCE]: {
        step: WizardSteps.CIRCONSTANCE,
        nextStepTitle: 'common.detail-dommage',
        secondaryStepTitle: 'common.enregistrer-brouillon',
        secondaryStepSubTitle: 'common.reprendre-plus-tard',
        secondaryStepIcon: 'mi-file_document_outline',
        isShowNavigation: true,
        isShowMenuTabs: true,
        actions: [
            WizardAction.SHOW_PREVIOUS,
            WizardAction.SHOW_SECONDARY,
            WizardAction.SHOW_NEXT,
            WizardAction.ENABLE_NEXT,
        ],
        routePath: RoutingPath.CIRCONSTANCE,
    },
    [WizardSteps.DOMMAGES]: {
        step: WizardSteps.DOMMAGES,
        secondaryStepTitle: 'common.enregistrer-brouillon',
        secondaryStepSubTitle: 'common.reprendre-plus-tard',
        secondaryStepIcon: 'mi-file_document_outline',
        isShowNavigation: true,
        isShowMenuTabs: true,
        actions: [
            WizardAction.SHOW_PREVIOUS,
            WizardAction.SHOW_SECONDARY,
            WizardAction.SHOW_NEXT,
            WizardAction.ENABLE_NEXT,
        ],
        routePath: RoutingPath.DOMMAGES,
    },
    [WizardSteps.GARAGE]: {
        step: WizardSteps.GARAGE,
        nextStepTitle: 'common.modalites.title',
        secondaryStepTitle: 'common.enregistrer-brouillon',
        secondaryStepSubTitle: 'common.reprendre-plus-tard',
        secondaryStepIcon: 'mi-file_document_outline',
        isShowNavigation: true,
        isShowMenuTabs: true,
        actions: [
            WizardAction.SHOW_PREVIOUS,
            WizardAction.SHOW_SECONDARY,
            WizardAction.SHOW_NEXT,
            WizardAction.ENABLE_NEXT,
        ],
        routePath: RoutingPath.GARAGE,
    },
    [WizardSteps.MODALITES]: {
        step: WizardSteps.MODALITES,
        nextStepTitle: 'common.pieces-jointes',
        secondaryStepTitle: 'common.enregistrer-brouillon',
        secondaryStepSubTitle: 'common.reprendre-plus-tard',
        secondaryStepIcon: 'mi-file_document_outline',
        isShowNavigation: true,
        isShowMenuTabs: true,
        actions: [
            WizardAction.SHOW_PREVIOUS,
            WizardAction.SHOW_SECONDARY,
            WizardAction.SHOW_NEXT,
            WizardAction.ENABLE_NEXT,
        ],
        routePath: RoutingPath.MODALITES,
    },
    [WizardSteps.PIECES_JOINTES]: {
        step: WizardSteps.PIECES_JOINTES,
        nextStepTitle: 'common.declarer-sinistre',
        secondaryStepTitle: 'common.enregistrer-brouillon',
        secondaryStepSubTitle: 'common.reprendre-plus-tard',
        secondaryStepIcon: 'mi-file_document_outline',
        isShowNavigation: true,
        isShowMenuTabs: true,
        actions: [
            WizardAction.SHOW_PREVIOUS,
            WizardAction.SHOW_SECONDARY,
            WizardAction.SHOW_NEXT,
            WizardAction.ENABLE_NEXT,
        ],
        routePath: RoutingPath.PIECES_JOINTES,
    },
    [WizardSteps.DECLARATION_TERMINEE]: {
        step: WizardSteps.DECLARATION_TERMINEE,
        isShowNavigation: false,
        isShowMenuTabs: false,
        actions: [WizardAction.HIDE_NAVIGATION],
        routePath: RoutingPath.DECLARATION_TERMINEE,
    },
    [WizardSteps.LISTE_DECLARATION]: {
        step: WizardSteps.LISTE_DECLARATION,
        isShowNavigation: false,
        isShowMenuTabs: true,
        actions: [WizardAction.HIDE_NAVIGATION],
        routePath: RoutingPath.LISTE_DECLARATION,
    },
}

export const habitationWizardStepConfigs: Record<
    WizardSteps,
    WizardStepConfig
> = {
    ...baseWizardStepConfigs,
    [WizardSteps.DOMMAGES]: {
        ...baseWizardStepConfigs[WizardSteps.DOMMAGES],
        nextStepTitle: 'common.modalites.title',
    },
}

export const autoWizardStepConfigs: Record<WizardSteps, WizardStepConfig> = {
    ...baseWizardStepConfigs,
    [WizardSteps.DOMMAGES]: {
        ...baseWizardStepConfigs[WizardSteps.DOMMAGES],
        nextStepTitle: 'common.garage.title',
    },
}

export const habitationStepSequence: WizardSteps[] = [
    WizardSteps.SELECTION_RISQUE,
    WizardSteps.GARANTIES,
    WizardSteps.CIRCONSTANCE,
    WizardSteps.DOMMAGES,
    WizardSteps.MODALITES,
    WizardSteps.PIECES_JOINTES,
    WizardSteps.DECLARATION_TERMINEE,
]

export const autoStepSequence: WizardSteps[] = [
    WizardSteps.SELECTION_RISQUE,
    WizardSteps.CIRCONSTANCE,
    WizardSteps.DOMMAGES,
    WizardSteps.GARAGE,
    WizardSteps.MODALITES,
    WizardSteps.PIECES_JOINTES,
    WizardSteps.DECLARATION_TERMINEE,
]
