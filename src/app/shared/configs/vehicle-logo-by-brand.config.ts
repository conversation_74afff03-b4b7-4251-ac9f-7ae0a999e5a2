const staticImages = 'https://static.foyer.lu/images/static/'
export const DEFAULT_CAR_LOGO = staticImages + 'udb-products/auto.svg'
const staticEurotaxImages = staticImages + 'eurotax/'
export const vehicleLogoByBrandMap: Map<string, string> = new Map([
    ['ABARTH', staticEurotaxImages + 'abarth.svg'],
    ['ALFA ROMEO', staticEurotaxImages + 'alfa romeo.svg'],
    ['APRILIA', staticEurotaxImages + 'aprilia.svg'],
    ['ASTON-MARTIN', staticEurotaxImages + 'aston-martin.svg'],
    ['AUDI', staticEurotaxImages + 'audi.svg'],
    ['BENTLEY', staticEurotaxImages + 'bentley.svg'],
    ['BMW', staticEurotaxImages + 'bmw.svg'],
    ['CADILLAC', staticEurotaxImages + 'cadillac.svg'],
    ['CHEVROLET', staticEurotaxImages + 'chevrolet.svg'],
    ['DACIA', staticEurotaxImages + 'dacia.svg'],
    ['CITROEN', staticEurotaxImages + 'citroen.svg'],
    ['CHRYSLER', staticEurotaxImages + 'chrysler.svg'],
    ['DAEWOO', staticEurotaxImages + 'daewoo.svg'],
    ['DUCATI', staticEurotaxImages + 'ducati.svg'],
    ['DODGE', staticEurotaxImages + 'dodge.svg'],
    ['DS', staticEurotaxImages + 'ds.svg'],
    ['FERRARI', staticEurotaxImages + 'ferrari.svg'],
    ['FIAT', staticEurotaxImages + 'fiat.svg'],
    ['FORD', staticEurotaxImages + 'ford.svg'],
    ['HARLEY DAVIDSON', staticEurotaxImages + 'harley-davidson.svg'],
    ['HONDA', staticEurotaxImages + 'honda.svg'],
    ['HYUNDAI', staticEurotaxImages + 'hyundai.svg'],
    ['HUMMER', staticEurotaxImages + 'hummer.svg'],
    ['HONDA-MOTO', staticEurotaxImages + 'honda-moto.svg'],
    ['INFINITI', staticEurotaxImages + 'infiniti.svg'],
    ['ISUZU', staticEurotaxImages + 'isuzu.svg'],
    ['IVECO', staticEurotaxImages + 'iveco.svg'],
    ['KAWASAKI', staticEurotaxImages + 'kawasaki.svg'],
    ['JEEP', staticEurotaxImages + 'jeep.svg'],
    ['JAGUAR', staticEurotaxImages + 'jaguar.svg'],
    ['KIA', staticEurotaxImages + 'kia.svg'],
    ['KTM', staticEurotaxImages + 'ktm.svg'],
    ['LADA', staticEurotaxImages + 'lada.svg'],
    ['LAMBORGHINI', staticEurotaxImages + 'lamborghini.svg'],
    ['LANCIA', staticEurotaxImages + 'lancia.svg'],
    ['LAND-ROVER', staticEurotaxImages + 'land-rover.svg'],
    ['MASERATI', staticEurotaxImages + 'maserati.svg'],
    ['LOTUS', staticEurotaxImages + 'lotus.svg'],
    ['LEXUS', staticEurotaxImages + 'lexus.svg'],
    ['MAZDA', staticEurotaxImages + 'mazda.svg'],
    ['MERCEDES-BENZ', staticEurotaxImages + 'mercedes-benz.svg'],
    ['MG', staticEurotaxImages + 'mg.svg'],
    ['NISSAN', staticEurotaxImages + 'nissan.svg'],
    ['MITSUBISHI', staticEurotaxImages + 'mitsubishi.svg'],
    ['MINI', staticEurotaxImages + 'mini.svg'],
    ['OPEL', staticEurotaxImages + 'opel.svg'],
    ['PEUGEOT', staticEurotaxImages + 'peugeot.svg'],
    ['PIAGGIO', staticEurotaxImages + 'piaggio.svg'],
    ['PORSCHE', staticEurotaxImages + 'porsche.svg'],
    ['RENAULT', staticEurotaxImages + 'renault.svg'],
    ['ROLLS-ROYCE', staticEurotaxImages + 'rolls-royce.svg'],
    ['SAAB', staticEurotaxImages + 'saab.svg'],
    ['SEAT', staticEurotaxImages + 'seat.svg'],
    ['SKODA', staticEurotaxImages + 'skoda.svg'],
    ['SMART', staticEurotaxImages + 'smart.svg'],
    ['SSANGYONG', staticEurotaxImages + 'ssangyong.svg'],
    ['SUBARU', staticEurotaxImages + 'subaru.svg'],
    ['SUZUKI', staticEurotaxImages + 'suzuki.svg'],
    ['TESLA', staticEurotaxImages + 'tesla.svg'],
    ['TOYOTA', staticEurotaxImages + 'toyota.svg'],
    ['TRIUMPH', staticEurotaxImages + 'triumph.svg'],
    ['VESPA', staticEurotaxImages + 'vespa.svg'],
    ['VOLKSWAGEN', staticEurotaxImages + 'volkswagen.svg'],
    ['VOLVO', staticEurotaxImages + 'volvo.svg'],
    ['YAMAHA', staticEurotaxImages + 'yamaha.svg'],
    ['', DEFAULT_CAR_LOGO],
])
