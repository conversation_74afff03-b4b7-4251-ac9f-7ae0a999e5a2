import { PrettifyReferentielAdressePipe } from './prettify-refentiel-adresse.pipe'
import { personnePhysiqueAdresseMock } from '../mocks/personne.mock'

describe('PrettifyReferentielAdressePipe', () => {
    let pipe: PrettifyReferentielAdressePipe
    beforeEach(() => {
        pipe = new PrettifyReferentielAdressePipe()
    })

    it('format adresse into 1 line', () => {
        expect(pipe.transform(personnePhysiqueAdresseMock())).toEqual(
            'Rue de la joie 12a, 1235 Paradis, LUXEMBOURG'
        )
    })

    it('format adresse into 2 lines', () => {
        expect(pipe.transform(personnePhysiqueAdresseMock())).toEqual(
            'Rue de la joie 12a, 1235 Paradis, LUXEMBOURG'
        )
    })
})
