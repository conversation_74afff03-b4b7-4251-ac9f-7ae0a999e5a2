import { SubPathsAreSuccessOrActivePipe } from './sub-paths-are-success-or-active.pipe'
import {
    initialGarantiesSubPathsMock,
    initialPiecesJointesSubPathsMock,
} from '../../mocks/stepper.mock'

describe('SubPathsAreSuccessOrActivePipe', () => {
    it('should return is-success when all subPaths are valid', () => {
        const pipe = new SubPathsAreSuccessOrActivePipe()
        expect(
            pipe.transform(initialPiecesJointesSubPathsMock(), false)
        ).toEqual('is-success')
    })

    it('should return is-active when all subPaths are not valid and is active', () => {
        const pipe = new SubPathsAreSuccessOrActivePipe()
        expect(pipe.transform(initialGarantiesSubPathsMock(), true)).toEqual(
            'is-active'
        )
    })

    it('should return nothing when all subPaths are not valid and is not active', () => {
        const pipe = new SubPathsAreSuccessOrActivePipe()
        expect(pipe.transform(initialGarantiesSubPathsMock(), false)).toEqual(
            ''
        )
    })
})
