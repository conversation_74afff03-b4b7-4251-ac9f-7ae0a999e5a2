import { Pipe, PipeTransform } from '@angular/core'
import { SubPath } from '../../components/stepper-parcours-declaration/models/stepper.model'

@Pipe({
    name: 'subPathsAreSuccessOrActive',
    standalone: true,
})
export class SubPathsAreSuccessOrActivePipe implements PipeTransform {
    transform(subPaths: SubPath[], isActive: boolean): string {
        if (subPaths.length > 0) {
            const validSubPaths = subPaths.every((sub) => sub.valid)
            if (validSubPaths) {
                return 'is-success'
            }
        }
        return isActive ? 'is-active' : ''
    }
}
