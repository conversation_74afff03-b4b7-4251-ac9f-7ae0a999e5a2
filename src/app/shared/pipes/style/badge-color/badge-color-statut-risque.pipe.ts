import { Pipe, PipeTransform } from '@angular/core'
import { StatutRisque } from '../../../enums/statut-risque.enum'

@Pipe({
    name: 'badgeColorStatutRisque',
    standalone: true,
})
export class BadgeColorStatutRisquePipe implements PipeTransform {
    transform(statut: StatutRisque): string {
        switch (statut) {
            case StatutRisque.EN_VIGUEUR:
                return 'is-success'
            case StatutRisque.ANNULE:
                return 'is-danger'
            case StatutRisque.EN_INSTANCE:
            case StatutRisque.SUSPENDU:
            case StatutRisque.SANS_EFFET:
                return 'is-warning'
            default:
                return ''
        }
    }
}
