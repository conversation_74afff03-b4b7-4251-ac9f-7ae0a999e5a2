import { StatutRisque } from '../../../enums/statut-risque.enum'
import { BadgeColorStatutRisquePipe } from './badge-color-statut-risque.pipe'

describe('BadgeColorStatutRisquePipe', () => {
    it('return "is-danger" if StatutRisque is ANNULE', () => {
        const pipe = new BadgeColorStatutRisquePipe()
        expect(pipe.transform(StatutRisque.ANNULE)).toEqual('is-danger')
    })

    it('return "is-warning" if StatutRisque is SUSPENDU', () => {
        const pipe = new BadgeColorStatutRisquePipe()
        expect(pipe.transform(StatutRisque.SUSPENDU)).toEqual('is-warning')
    })

    it('return "is-warning" if StatutRisque is SANS_EFFET', () => {
        const pipe = new BadgeColorStatutRisquePipe()
        expect(pipe.transform(StatutRisque.SANS_EFFET)).toEqual('is-warning')
    })

    it('return "is-success" if StatutRisque is EN_VIGUEUR', () => {
        const pipe = new BadgeColorStatutRisquePipe()
        expect(pipe.transform(StatutRisque.EN_VIGUEUR)).toEqual('is-success')
    })

    it('return default("") if DossierKind is wrong', () => {
        const pipe = new BadgeColorStatutRisquePipe()
        expect(pipe.transform({} as StatutRisque)).toEqual('')
    })
})
