import { DeclarationStateKind } from '../../../enums/declaration-state-kind.enum'
import { BadgeColorDeclarationKindPipe } from './badge-color-declaration-kind.pipe'

describe('BadgeColorDeclarationKindPipe', () => {
    it('return "" if DeclarationKind is CREE', () => {
        const pipe = new BadgeColorDeclarationKindPipe()
        expect(pipe.transform(DeclarationStateKind.CREE)).toEqual('is-primary')
    })

    it('return "is-success" if DeclarationKind is SOUMIS', () => {
        const pipe = new BadgeColorDeclarationKindPipe()
        expect(pipe.transform(DeclarationStateKind.SOUMIS)).toEqual(
            'is-success'
        )
    })
})
