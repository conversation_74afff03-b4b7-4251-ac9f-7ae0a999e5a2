import { Pipe, PipeTransform } from '@angular/core'
import { DeclarationStateKind } from '../../../enums/declaration-state-kind.enum'

@Pipe({
    name: 'badgeColorDeclarationKind',
    standalone: true,
})
export class BadgeColorDeclarationKindPipe implements PipeTransform {
    transform(statut: DeclarationStateKind): string {
        if (statut === DeclarationStateKind.SOUMIS) {
            return 'is-success'
        } else {
            return 'is-primary'
        }
    }
}
