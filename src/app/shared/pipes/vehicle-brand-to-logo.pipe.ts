import { Pipe, PipeTransform } from '@angular/core'
import {
    DEFAULT_CAR_LOGO,
    vehicleLogoByBrandMap,
} from '../configs/vehicle-logo-by-brand.config'

@Pipe({
    name: 'vehicleBrandToLogo',
    standalone: true,
})
export class VehicleBrandToLogoPipe implements PipeTransform {
    transform(marque?: string): string {
        const upperCaseBrandName = marque ? marque.trim().toUpperCase() : ''
        return vehicleLogoByBrandMap.get(upperCaseBrandName) ?? DEFAULT_CAR_LOGO
    }
}
