import { Pipe, PipeTransform } from '@angular/core'
import { FilterType } from '../../enums/filter-type.enum'

@Pipe({
    name: 'getTranslationKeyFromFilter',
    standalone: true,
})
export class GetTranslationKeyFromFilterPipe implements PipeTransform {
    transform(filter: FilterType): string {
        switch (filter) {
            case FilterType.KIND:
                return 'enums.declaration-kind.'
            case FilterType.GARANTIES:
                return 'enums.code-garantie.'
            case FilterType.DATE_DE_SURVENANCE:
                return 'enums.filter-date.'
            default:
                return ''
        }
    }
}
