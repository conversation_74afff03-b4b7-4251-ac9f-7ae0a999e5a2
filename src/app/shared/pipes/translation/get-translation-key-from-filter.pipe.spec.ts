import { GetTranslationKeyFromFilterPipe } from './get-translation-key-from-filter.pipe'
import { FilterType } from '../../enums/filter-type.enum'

describe('GetTranslationKeyFromFilterPipe', () => {
    it('return "enums.filter-date." if FilterType is DATE', () => {
        const pipe = new GetTranslationKeyFromFilterPipe()
        expect(pipe.transform(FilterType.DATE_DE_SURVENANCE)).toEqual(
            'enums.filter-date.'
        )
    })
})
