import { GetTranslationForArrayEnumPipe } from './get-translation-for-array-enum.pipe'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { TestBed } from '@angular/core/testing'
import { TranslationServiceMock } from '../../../core/services/translation/translation.service.spec.mock'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { of } from 'rxjs'
import { ZonesMaisonInterieur } from '../../../habitation/enums/zones-maison-interieur.enum'
import { ZonesMaisonExterieur } from '../../../habitation/enums/zones-maison-exterieur.enum'

describe('GetTranslationForArrayEnumPipe', () => {
    let pipe: GetTranslationForArrayEnumPipe
    let translationService: TranslationService

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                {
                    provide: TranslationService,
                    useClass: TranslationServiceMock,
                },
            ],
        })
        translationService = TestBed.inject(TranslationService)
        pipe = new GetTranslationForArrayEnumPipe(translationService)
    })

    const testTranslationTransform = <T extends string>(
        items: T[],
        expectedTranslation: string,
        prefix: string,
        done?: DoneFn,
        withSpy = true
    ) => {
        if (withSpy) {
            spyOn(translationService, 'getTranslation$').and.returnValue(
                of(expectedTranslation)
            )
        }
        pipe.transform(items, prefix).subscribe((result) => {
            items.forEach((item) => {
                expect(translationService.getTranslation$).toHaveBeenCalledWith(
                    {
                        key: item,
                        path: prefix,
                    }
                )
            })
            expect(result).toContain(expectedTranslation)
            if (done) {
                done()
            }
        })
    }

    describe('When we look for Dommages enum', () => {
        const prefix = 'enums.code-garantie.'

        it('transforms single garantie', (done) => {
            const garanties: TypeGaranties[] = [TypeGaranties.INCN]
            const expectedTranslation = 'Incendie et connexes'

            testTranslationTransform(
                garanties,
                expectedTranslation,
                prefix,
                done
            )
        })

        it('transforms multiple garanties', (done) => {
            const garanties: TypeGaranties[] = [
                TypeGaranties.INCN,
                TypeGaranties.TGN,
            ]
            const expectedTranslation =
                'Incendie et connexes, Tempête, grêle, neige'

            testTranslationTransform(
                garanties,
                expectedTranslation,
                prefix,
                done
            )
        })
    })

    describe('When we look for ZonesMaisonInterieur enum', () => {
        const prefix = 'enums.zones-maison-interieur.'

        it('transforms single zone', (done) => {
            const zones: ZonesMaisonInterieur[] = [ZonesMaisonInterieur.CHAMBRE]
            const expectedTranslation = 'Chambre'

            testTranslationTransform(zones, expectedTranslation, prefix, done)
        })

        it('transforms multiple zones', (done) => {
            const zones: ZonesMaisonInterieur[] = [
                ZonesMaisonInterieur.CHAMBRE,
                ZonesMaisonInterieur.CUISINE,
            ]
            const expectedTranslation = 'Chambre, Cuisine'

            testTranslationTransform(zones, expectedTranslation, prefix, done)
        })
    })

    describe('When we look for ZonesMaisonExterieur enum', () => {
        const prefix = 'enums.zones-maison-exterieur.'

        it('transforms single zone', (done) => {
            const zones: ZonesMaisonExterieur[] = [ZonesMaisonExterieur.TOITURE]
            const expectedTranslation = 'Toiture'

            testTranslationTransform(zones, expectedTranslation, prefix, done)
        })

        it('transforms multiple zones', (done) => {
            const zones: ZonesMaisonExterieur[] = [
                ZonesMaisonExterieur.TOITURE,
                ZonesMaisonExterieur.PISCINE,
            ]
            const expectedTranslation = 'Toiture, Piscine'

            testTranslationTransform(zones, expectedTranslation, prefix, done)
        })
    })

    describe('When we use pipe with the same array', () => {
        const prefix = 'enums.code-garantie.'

        it('should send new result when data is modified', (done) => {
            const garanties: TypeGaranties[] = [
                TypeGaranties.INCN,
                TypeGaranties.TGN,
            ]
            const expectedTranslation1 = 'faketranslation, faketranslation'

            spyOn(translationService, 'getTranslation$').and.returnValue(
                of('faketranslation')
            )

            testTranslationTransform(
                garanties,
                expectedTranslation1,
                prefix,
                undefined,
                false
            )

            garanties.push(TypeGaranties.BDGN)

            const expectedTranslation2 =
                'faketranslation, faketranslation, faketranslation'

            testTranslationTransform(
                garanties,
                expectedTranslation2,
                prefix,
                done,
                false
            )
        })
    })
})
