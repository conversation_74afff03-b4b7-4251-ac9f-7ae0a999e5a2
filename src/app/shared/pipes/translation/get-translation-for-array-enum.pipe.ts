import { Pipe, PipeTransform } from '@angular/core'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { forkJoin, map, Observable, switchMap } from 'rxjs'

@Pipe({
    name: 'getTranslationForArrayEnum',
    standalone: true,
    pure: false, //tableau donnée en paramètre a la même référence, et n'est pas considéré comme modifié lors d'un changement de valeurs
})
export class GetTranslationForArrayEnumPipe implements PipeTransform {
    constructor(private readonly translationService: TranslationService) {}

    transform(values: string[], prefix: string): Observable<string> {
        if (values.length === 1) {
            return this.translationService.getTranslation$({
                key: values[0],
                path: prefix,
            })
        } else {
            const valuesTranslated$: Observable<string>[] = values.map(
                (value) =>
                    this.translationService.getTranslation$({
                        key: value,
                        path: prefix,
                    })
            )
            return this.translationService.getCurrentLanguage$().pipe(
                switchMap(() => forkJoin(valuesTranslated$)),
                map((translations: string[]) => translations.join(', '))
            )
        }
    }
}
