import { TestBed } from '@angular/core/testing'
import { RisquesService } from '../../services/risques/risques.service'
import { NumeroRisqueToPlaquePipe } from './numero-risque-to-plaque.pipe'
import { RisquesServiceMock } from '../../services/risques/risques.service.mock'
import { risqueAutoMock } from '../../mocks/risques.mock'
import { of } from 'rxjs'

describe('NumeroRisqueToPersonnePipe', () => {
  let pipe: NumeroRisqueToPlaquePipe
  let risquesServices: RisquesService

  beforeEach(() => {
      TestBed.configureTestingModule({
          providers: [
                NumeroRisqueToPlaquePipe,
              {
                  provide: RisquesService,
                  useClass: RisquesServiceMock,
              },
          ],
      })

      risquesServices = TestBed.inject(RisquesService)
      pipe = TestBed.inject(NumeroRisqueToPlaquePipe)
  })

  it('should return the plaque linked to the risque', (done) => {
      pipe.transform('10001237-17-1').subscribe((result) => {
          expect(result).toBe('A112BC2')
          done()
      })
  })

  it('should return empty string when risque without plate', (done) => {
    spyOn(risquesServices, 'getSituationContratRisque').and.returnValue(of(risqueAutoMock()))
    pipe.transform('20527837-22-1').subscribe((result) => {
        expect(result).toBe('')
        done()
    })
})
})
