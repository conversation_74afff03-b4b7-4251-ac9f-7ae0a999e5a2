import { inject, Pipe, PipeTransform } from '@angular/core'
import { RisquesService } from '../../services/risques/risques.service'
import { map, Observable } from 'rxjs'

@Pipe({
    name: 'numeroRisqueToPlaque',
    standalone: true,
})
export class NumeroRisqueToPlaquePipe implements PipeTransform {
    private readonly risquesService: RisquesService = inject(RisquesService)

    transform(numeroRisque: string): Observable<string> {
        return this.risquesService
            .getSituationContratRisque(numeroRisque)
            .pipe(map((result) => result.plaque ?? ''))
    }
}
