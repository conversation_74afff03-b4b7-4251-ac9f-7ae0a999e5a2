import { inject, Pipe, PipeTransform } from '@angular/core'
import { RisquesService } from '../../services/risques/risques.service'
import { map, Observable } from 'rxjs'
import { getOrFail } from '../../utils/objects.utils'
import {
    formatAdress,
    formatAdresseFromReferentiel,
} from '../../utils/adresse.utils'
import { ReferentielAdresse } from '../../wrappers/personne-wrapper/models/personne/referentiel-adresse'

@Pipe({
    name: 'numeroRisqueToAdresse',
    standalone: true,
})
export class NumeroRisqueToAdressePipe implements PipeTransform {
    private readonly risquesService: RisquesService = inject(RisquesService)

    transform(
        numeroRisque: string,
        short: boolean = false
    ): Observable<string> {
        return this.risquesService.getSituationContratRisque(numeroRisque).pipe(
            map((result) => {
                const adresse = getOrFail(
                    result.adresse,
                    'adresse',
                    'situationContratRisque'
                )
                if (short) {
                    return formatAdress(
                        undefined,
                        undefined,
                        adresse.codePostal,
                        adresse.localite,
                        adresse.pays.label,
                        ', '
                    ).toUpperCase()
                } else {
                    return formatAdresseFromReferentiel(
                        adresse as ReferentielAdresse,
                        ', '
                    ).toUpperCase()
                }
            })
        )
    }
}
