import { TestBed } from '@angular/core/testing'
import { RisquesService } from '../../services/risques/risques.service'
import { NumeroRisqueToAdressePipe } from './numero-risque-to-adresse.pipe'
import { RisquesServiceMock } from '../../services/risques/risques.service.mock'

describe('NumeroRisqueToAdressePipe', () => {
  let pipe: NumeroRisqueToAdressePipe

  beforeEach(() => {
      TestBed.configureTestingModule({
          providers: [
            NumeroRisqueToAdressePipe,
              {
                  provide: RisquesService,
                  useClass: RisquesServiceMock,
              },
          ],
      })
      pipe = TestBed.inject(NumeroRisqueToAdressePipe)
  })

  it('should return the short address linked to the risque with short option', (done) => {
      pipe.transform('10001237-17-1', true).subscribe((result) => {
          expect(result).toBe('1471 LUXEMBOURG, LUXEMBOURG')
          done()
      })
  })

  it('should return the address linked to the risque without short option', (done) => {
    pipe.transform('10001237-17-1').subscribe((result) => {
        expect(result).toBe('ROUTE D\'ECH 237, 1471 LUXEMBOURG, LUXEMBOURG')
        done()
    })
})
})
