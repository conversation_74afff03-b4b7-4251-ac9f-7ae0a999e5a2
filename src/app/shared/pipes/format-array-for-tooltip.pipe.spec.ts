import { FormatArrayForTooltipPipe } from './format-array-for-tooltip.pipe'

describe('FormatArrayForTooltipPipe', () => {
    it('should return the single value if the array contains only one element', () => {
        const pipe = new FormatArrayForTooltipPipe()
        expect(pipe.transform(['Monsieur'])).toEqual('Monsieur')
    })

    it('should return a comma-separated string if the array contains multiple elements', () => {
        const pipe = new FormatArrayForTooltipPipe()
        expect(pipe.transform(['Monsieur', 'Madame'])).toEqual(
            'Monsieur, Madame'
        )
    })
})
