import { TestBed } from '@angular/core/testing'
import { ReferentielTranslationPipe } from './referentiel-translation.pipe'
import { TranslationWrapper } from '../../wrappers/translation/translation.wrapper'
import { TranslationWrapperMock } from '../../wrappers/translation/translation.wrapper.mock'
import { TranslationReference } from '../../wrappers/translation/models/translation-reference'

describe('ReferentielTranslationPipe', () => {
    let translationWrapper: TranslationWrapper
    beforeEach(async () => {
        await TestBed.configureTestingModule({
            providers: [
                {
                    provide: TranslationWrapper,
                    useClass: TranslationWrapperMock,
                },
            ],
        })
        translationWrapper = TestBed.inject(TranslationWrapper)
    })

    it('create an instance', () => {
        const pipe = new ReferentielTranslationPipe(translationWrapper)
        expect(pipe).toBeTruthy()
    })

    it('should show translation if exists', (done) => {
        const pipe = new ReferentielTranslationPipe(translationWrapper)
        pipe.transform('B08', TranslationReference.PRODUIT).subscribe(
            (result) => {
                expect(result).toBe('MOBILE REGIO 2008')
                done()
            }
        )
    })

    it('should show input if it is an unknown value', (done) => {
        const pipe = new ReferentielTranslationPipe(translationWrapper)
        pipe.transform('PB09915', TranslationReference.PRODUIT).subscribe(
            (result) => {
                expect(result).toBe('PB09915')
                done()
            }
        )
    })

    it('should show an empty text if it is an undefined value', () => {
        const pipe = new ReferentielTranslationPipe(translationWrapper)
        expect(() => {
            pipe.transform(undefined, TranslationReference.PRODUIT)
        }).toThrow(new Error('The liste code is missing'))
    })
})
