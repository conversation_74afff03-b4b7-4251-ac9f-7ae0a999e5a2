import { Pipe, PipeTransform } from '@angular/core'
import { map, Observable } from 'rxjs'
import { TranslationWrapper } from '../../wrappers/translation/translation.wrapper'
import { TranslationReference } from '../../wrappers/translation/models/translation-reference'

@Pipe({
    name: 'referentielTranslation',
    standalone: true,
})
export class ReferentielTranslationPipe implements PipeTransform {
    constructor(private readonly translationWrapper: TranslationWrapper) {}

    transform(
        code: string | undefined,
        reference: TranslationReference
    ): Observable<string> {
        if (code === undefined) {
            throw new Error('The liste code is missing')
        }

        return this.translationWrapper.getList(reference).pipe(
            map((traductions) => {
                const foundTranslation = traductions.translations.find(
                    (translation) => translation.code === code
                )
                return foundTranslation ? foundTranslation.label : code
            })
        )
    }
}
