import { DefaultOrderKeyValuePipe } from './default-oder-key-value.pipe'
import { TestBed } from '@angular/core/testing'

describe('DefaultOrderKeyValuePipe', () => {
    let defaultOrderKeyValuePipe: DefaultOrderKeyValuePipe

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [DefaultOrderKeyValuePipe],
            providers: [DefaultOrderKeyValuePipe],
        })
        defaultOrderKeyValuePipe = TestBed.inject(DefaultOrderKeyValuePipe)
    })

    it('should return object with keys sorted with order preserved', () => {
        const mockedValue = {
            C: 'foo',
            A: 'bar',
            E: 'baz',
            D: 'qux',
            B: 'quux',
        }
        const expectedResult = new Map([
            ['C', 'foo'],
            ['A', 'bar'],
            ['E', 'baz'],
            ['D', 'qux'],
            ['B', 'quux'],
        ])
        const result = defaultOrderKeyValuePipe.transform(mockedValue)
        const mapResult = new Map(
            result.map((item: { key: string; value: string }) => [
                item.key,
                item.value,
            ])
        )

        expect(mapResult).toEqual(expectedResult)
    })
})
