import { PersonneNamePipe } from './personne-name.pipe'
import {
    personneMoraleMock,
    personnePhysiqueMock,
} from '../../mocks/personne.mock'
import { PersonnePhysique } from '../../wrappers/personne-wrapper/models/personne/personne-physique'
import { TestBed } from '@angular/core/testing'
import { BrowserModule } from '@angular/platform-browser'
import { TitleCasePipe } from '@angular/common'

describe('PersonneNamePipe', () => {
    let pipe: PersonneNamePipe

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [BrowserModule],
            providers: [PersonneNamePipe, TitleCasePipe],
        })

        pipe = TestBed.inject(PersonneNamePipe)
    })

    it('format nom for personne physique', () => {
        const expectedResult = 'Rocky BALBOA'
        expect(pipe.transform(personnePhysiqueMock())).toBe(expectedResult)
    })

    it('format nom for personne morale', () => {
        const expectedResult = 'AXA'
        expect(pipe.transform(personneMoraleMock())).toBe(expectedResult)
    })

    it('format only nom if prenom is missing', () => {
        const expectedResult = 'Alba'
        expect(
            pipe.transform({
                nomUsuel: 'Alba',
                typePersonne: { code: '17', label: 'test personne physique' },
            } as PersonnePhysique)
        ).toBe(expectedResult)
    })
})
