import { Pipe, PipeTransform } from '@angular/core'
import { differenceInYears } from 'date-fns'
import { PersonnePhysique } from '../../wrappers/personne-wrapper/models/personne/personne-physique'
import { Observable, of } from 'rxjs'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { parseDashedDDMMYYYYtoDate } from '../../utils/date.utils'
import { isPersonnePhysique } from '../../utils/personne.utils'
import { TranslationWithPath } from '../../../core/services/translation/models/translation.model'

@Pipe({
    name: 'personneToAge',
    standalone: true,
})
export class PersonneToAgePipe implements PipeTransform {
    constructor(private readonly translationService: TranslationService) {}

    transform(personne: Personne): Observable<string> {
        if (isPersonnePhysique(personne.typePersonne.code)) {
            const personnePhysique = personne as PersonnePhysique

            if (!personnePhysique.dateDeNaissance) {
                return of('')
            }

            const birthdate = parseDashedDDMMYYYYtoDate(
                personnePhysique.dateDeNaissance || ''
            )

            const age = differenceInYears(new Date(), birthdate).toString()
            const translation: TranslationWithPath = {
                key: 'years-old',
                path: 'common.',
            }

            return this.translationService.getTranslation$(translation, {
                nombre: age,
            })
        } else {
            return of('')
        }
    }
}
