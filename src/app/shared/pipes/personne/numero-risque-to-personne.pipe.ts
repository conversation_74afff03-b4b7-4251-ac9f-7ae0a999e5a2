import { Pipe, PipeTransform } from '@angular/core'
import { RisquesService } from '../../services/risques/risques.service'
import { map, Observable } from 'rxjs'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'
import { getOrFail } from '../../utils/objects.utils'

@Pipe({
    name: 'numeroRisqueToPersonne',
    standalone: true,
})
export class NumeroRisqueToPersonnePipe implements PipeTransform {
    constructor(private readonly risquesService: RisquesService) {}

    transform(numeroRisque: string): Observable<Personne> {
        return this.risquesService
            .getSituationContratRisque(numeroRisque)
            .pipe(map((result) => getOrFail(result.preneur, 'risque')))
    }
}
