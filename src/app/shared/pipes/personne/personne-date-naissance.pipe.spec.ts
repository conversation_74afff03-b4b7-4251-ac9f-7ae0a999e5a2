import { of } from 'rxjs'
import { PersonneDateNaissancePipe } from './personne-date-naissance.pipe'
import {
    personneMoraleMock,
    personnePhysiqueMock,
    personnePhysiqueTitreDateNaissanceAndContentieuxMock,
} from '../../mocks/personne.mock'
import { PersonneTitre } from '../../enums/personne-titre.enum'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { TestBed } from '@angular/core/testing'
import { TranslationServiceMock } from '../../../core/services/translation/translation.service.spec.mock'

describe('PersonneDateNaissancePipe', () => {
    let pipe: PersonneDateNaissancePipe
    let translationService: TranslationService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            providers: [
                {
                    provide: TranslationService,
                    useClass: TranslationServiceMock,
                },
            ],
        }).compileComponents()

        translationService = TestBed.inject(TranslationService)
        pipe = new PersonneDateNaissancePipe(translationService)
    })

    it('format and return birthdate for personne Physique of women gender', (done) => {
        spyOn(translationService, 'getTranslation$').and.returnValue(
            of('Née le')
        )
        pipe.transform(personnePhysiqueMock()).subscribe((result) => {
            expect(result).toBe(' - Née le 17.03.1984')
            expect(translationService.getTranslation$).toHaveBeenCalledWith({
                key: 'born.WOMEN',
                path: 'common.',
            })
            done()
        })
    })

    it('format and return birthdate for personne Physique of men gender', (done) => {
        spyOn(translationService, 'getTranslation$').and.returnValue(
            of('Né le')
        )
        const monsieur = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: PersonneTitre.MONSIEUR, label: 'Mr' },
            '1984-03-18'
        )
        pipe.transform(monsieur).subscribe((result) => {
            expect(result).toBe(' - Né le 18.03.1984')
            expect(translationService.getTranslation$).toHaveBeenCalledWith({
                key: 'born.OTHER',
                path: 'common.',
            })
            done()
        })
    })

    it('format and return birthdate for personne Physique of curious gender', (done) => {
        spyOn(translationService, 'getTranslation$').and.returnValue(
            of('Né le')
        )
        const monsieur = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: PersonneTitre.MONSIEUR_ET_MADAME, label: 'Mr & Mrs' },
            '1984-03-01'
        )
        pipe.transform(monsieur).subscribe((result) => {
            expect(result).toBe(' - Né le 01.03.1984')
            expect(translationService.getTranslation$).toHaveBeenCalledWith({
                key: 'born.OTHER',
                path: 'common.',
            })
            done()
        })
    })

    it('should return empty string if applied on personne Morale', (done) => {
        pipe.transform(personneMoraleMock()).subscribe((result) => {
            expect(result).toBe('')
            done()
        })
    })
})
