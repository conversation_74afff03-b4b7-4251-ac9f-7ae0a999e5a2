import { Pipe, PipeTransform } from '@angular/core'

import { map, Observable, of } from 'rxjs'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'
import { PersonnePhysique } from '../../wrappers/personne-wrapper/models/personne/personne-physique'
import { PersonneTitre } from '../../enums/personne-titre.enum'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { TranslationWithPath } from '../../../core/services/translation/models/translation.model'
import {
    formatDateToDottedDDMMYYYY,
    parseDashedDDMMYYYYtoDate,
} from '../../utils/date.utils'
import { isPersonnePhysique } from '../../utils/personne.utils'

@Pipe({
    name: 'personneDateNaissance',
    standalone: true,
})
export class PersonneDateNaissancePipe implements PipeTransform {
    constructor(private readonly translationService: TranslationService) {}

    transform(personne: Personne): Observable<string> {
        if (isPersonnePhysique(personne.typePersonne.code)) {
            const personnePhysique = personne as PersonnePhysique

            if (!personnePhysique.dateDeNaissance) {
                return of('')
            }

            const birthdate = parseDashedDDMMYYYYtoDate(
                personnePhysique.dateDeNaissance
            )

            const translation: TranslationWithPath = {
                key: this.defineBornStringByGender(personnePhysique),
                path: 'common.',
            }

            return this.translationService
                .getTranslation$(translation)
                .pipe(
                    map(
                        (translatedBornString) =>
                            ` - ${translatedBornString} ${formatDateToDottedDDMMYYYY(
                                birthdate
                            )}`
                    )
                )
        } else {
            return of('')
        }
    }

    private defineBornStringByGender(personne: PersonnePhysique): string {
        return personne.titre.code === PersonneTitre.MADAME
            ? 'born.WOMEN'
            : 'born.OTHER'
    }
}
