import { SortAlphabeticallyGarantiePipe } from './sort-alphabetically-garantie.pipe'
import { Garantie } from '../../models/garantie'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { EtatGarantie } from '../../enums/etat-garantie.enum'
import {
    DATE_ENTREE_EN_VIGUEUR,
    garantiesMock,
} from '../../mocks/garanties.mock'

describe('SortAlphabeticallyGarantiePipe', () => {
    let pipe: SortAlphabeticallyGarantiePipe

    beforeEach(() => {
        pipe = new SortAlphabeticallyGarantiePipe()
    })

    it('should sort array of objects alphabetically by given property', () => {
        const garanties = garantiesMock()

        const sortedGaranties = pipe.transform(garanties)
        expect(sortedGaranties).toEqual([
            {
                nom: 'Attentats et conflits du travail',
                code: TypeGaranties.ATTN,
                dateEntreeEnVigueur: DATE_ENTREE_EN_VIGUEUR,
                franchise: '0',
                etat: EtatGarantie.EN_VIGUEUR,
                franchises: [],
            },
            {
                nom: 'Catastrophes naturelles',
                code: TypeGaranties.CNN,
                dateEntreeEnVigueur: DATE_ENTREE_EN_VIGUEUR,
                franchise: '0',
                etat: EtatGarantie.EN_VIGUEUR,
                franchises: [],
            },
            {
                nom: 'Dégâts des eaux',
                code: TypeGaranties.DGEN,
                dateEntreeEnVigueur: DATE_ENTREE_EN_VIGUEUR,
                franchise: '0',
                etat: EtatGarantie.EN_VIGUEUR,
                franchises: [],
            },
            {
                nom: 'Incendie et risques connexes',
                code: TypeGaranties.INCN,
                dateEntreeEnVigueur: DATE_ENTREE_EN_VIGUEUR,
                franchise: '0',
                etat: EtatGarantie.EN_VIGUEUR,
                franchises: [],
            },
            {
                nom: 'Tempête, grêle, neige',
                code: TypeGaranties.TGN,
                dateEntreeEnVigueur: DATE_ENTREE_EN_VIGUEUR,
                franchise: '0',
                etat: EtatGarantie.EN_VIGUEUR,
                franchises: [],
            },
        ])
    })

    it('should handle an empty array', () => {
        const garanties: Garantie[] = []

        const sortedGaranties = pipe.transform(garanties)

        expect(sortedGaranties).toEqual([])
    })

    it('should handle an array with one element', () => {
        const garanties: Garantie[] = [
            {
                nom: 'Assurance Vie',
                code: TypeGaranties.TGN,
                dateEntreeEnVigueur: new Date('2020-01-01'),
                franchise: '1000',
                etat: EtatGarantie.EN_VIGUEUR,
                franchises: [],
            },
        ]

        const sortedGaranties = pipe.transform(garanties)

        expect(sortedGaranties).toEqual([
            {
                nom: 'Assurance Vie',
                code: TypeGaranties.TGN,
                dateEntreeEnVigueur: new Date('2020-01-01'),
                franchise: '1000',
                etat: EtatGarantie.EN_VIGUEUR,
                franchises: [],
            },
        ])
    })
})
