import { PersonneLanguePipe } from './personne-langue.pipe'
import { personnePhysiqueMock } from '../../mocks/personne.mock'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'

describe('PersonneLanguePipe', () => {
    let pipe: PersonneLanguePipe

    beforeEach(() => {
        pipe = new PersonneLanguePipe()
    })

    it('format and return langue for personne Physique', () => {
        const expectedResult = ' Allemand'
        expect(pipe.transform(personnePhysiqueMock())).toBe(expectedResult)
    })

    it('format and return langue for personne Morale', () => {
        const expectedResult = ''
        expect(pipe.transform({} as Personne)).toBe(expectedResult)
    })
})
