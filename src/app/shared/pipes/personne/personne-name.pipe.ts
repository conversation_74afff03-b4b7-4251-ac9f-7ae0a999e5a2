import { inject, Pipe, PipeTransform } from '@angular/core'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'
import { PersonneMorale } from '../../wrappers/personne-wrapper/models/personne/personne-morale'
import { PersonnePhysique } from '../../wrappers/personne-wrapper/models/personne/personne-physique'
import { isPersonneMorale } from '../../utils/personne.utils'
import { TitleCasePipe } from '@angular/common'

@Pipe({
    name: 'personneName',
    standalone: true,
})
export class PersonneNamePipe implements PipeTransform {
    private readonly titleCasePipe: TitleCasePipe = inject(TitleCasePipe)

    transform(personne: Personne): string {
        if (isPersonneMorale(personne.typePersonne.code)) {
            const personneMorale = personne as PersonneMorale
            return personneMorale.nom
        }
        const personnePhysique = personne as PersonnePhysique

        return personnePhysique.prenom && personnePhysique.nomUsuel
            ? `${this.titleCasePipe.transform(personnePhysique.prenom)} ${
                  personnePhysique.nomUsuel
              }`
            : `${personnePhysique.nomUsuel}`
    }
}
