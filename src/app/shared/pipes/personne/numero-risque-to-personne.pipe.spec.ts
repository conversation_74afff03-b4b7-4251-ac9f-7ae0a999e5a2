import { NumeroRisqueToPersonnePipe } from './numero-risque-to-personne.pipe'
import { TestBed } from '@angular/core/testing'
import { RisquesService } from '../../services/risques/risques.service'
import { RisquesServiceMock } from '../../services/risques/risques.service.mock'

describe('NumeroRisqueToPersonnePipe', () => {
    let pipe: NumeroRisqueToPersonnePipe
    let risquesService: RisquesService

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
            ],
        })
        risquesService = TestBed.inject(RisquesService)
        pipe = new NumeroRisqueToPersonnePipe(risquesService)
    })

    it('should return the person linked to the risque', (done) => {
        pipe.transform('123456').subscribe((result) => {
            expect(result.numeroPersonne).toBe('123')
            done()
        })
    })
})
