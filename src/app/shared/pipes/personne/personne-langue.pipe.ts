import { Pipe, PipeTransform } from '@angular/core'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'

@Pipe({
    name: 'personneLangue',
    standalone: true,
})
export class PersonneLanguePipe implements PipeTransform {
    transform(personne: Personne): string {
        return personne.langueCorrespondance
            ? ' ' + personne.langueCorrespondance.label
            : ''
    }
}
