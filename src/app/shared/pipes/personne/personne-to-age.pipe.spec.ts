import { PersonneToAgePipe } from './personne-to-age.pipe'
import { personnePhysiqueTitreDateNaissanceAndContentieuxMock } from '../../mocks/personne.mock'
import { PersonneTitre } from '../../enums/personne-titre.enum'
import { format, subYears } from 'date-fns'
import { PersonnePhysique } from '../../wrappers/personne-wrapper/models/personne/personne-physique'
import { TestBed } from '@angular/core/testing'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { TranslationServiceMock } from '../../../core/services/translation/translation.service.spec.mock'

describe('PersonneToAgePipe', () => {
    let pipe: PersonneToAgePipe
    let translationService: TranslationService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            providers: [
                {
                    provide: TranslationService,
                    useClass: TranslationServiceMock,
                },
            ],
        }).compileComponents()

        translationService = TestBed.inject(TranslationService)
        pipe = new PersonneToAgePipe(translationService)
    })

    it('should calculate age and format accordingly', () => {
        spyOn(translationService, 'getTranslation$')
        const now = new Date()
        const expectedAge = 39
        const olderNow = subYears(now, expectedAge)
        const olderNowString = format(olderNow, 'yyyy-MM-dd')
        const oldPerson = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: PersonneTitre.MADAME, label: 'Ms' },
            olderNowString
        )
        pipe.transform(oldPerson)
        expect(translationService.getTranslation$).toHaveBeenCalledOnceWith(
            {
                key: 'years-old',
                path: 'common.',
            },
            { nombre: expectedAge.toString() }
        )
    })

    it('should not show string if no date de naissance is defined', () => {
        const personWithoutBirthdate: PersonnePhysique =
            personnePhysiqueTitreDateNaissanceAndContentieuxMock(
                { code: PersonneTitre.MADAME, label: 'Ms' },
                '2000-01-01'
            )
        personWithoutBirthdate.dateDeNaissance = undefined
        pipe.transform(personWithoutBirthdate).subscribe((result) => {
            expect(result).toBe('')
        })
    })
})
