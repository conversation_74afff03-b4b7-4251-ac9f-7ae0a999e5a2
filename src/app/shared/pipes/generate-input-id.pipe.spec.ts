import { GenerateInputIdPipe } from './generate-input-id.pipe'
import { FilterType } from '../enums/filter-type.enum'

describe('GenerateInputIdPipe', () => {
    it('return expected id whith instance destinataire and index', () => {
        const pipe = new GenerateInputIdPipe()
        const expectedId = 'col-declaration.garanties-filter-2'
        expect(pipe.transform(FilterType.GARANTIES, 2)).toEqual(expectedId)
    })
})
