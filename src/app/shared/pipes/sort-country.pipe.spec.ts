import { SortCountryPipe } from './sort-country.pipe'
import { paysMock } from '../mocks/refAdresseMock'

describe('SortCountryPipe', () => {
    let pipe: SortCountryPipe

    beforeEach(() => {
        pipe = new SortCountryPipe()
    })

    it('should sort countries according to priority order', () => {
        const expectedOrder = [
            { code: 'BE', label: 'Belgique' },
            { code: 'LU', label: 'Luxembourg' },
            { code: 'DE', label: 'Allemagne' },
            { code: 'FR', label: 'France' },
            { code: 'NL', label: 'Pays-Bas' },
            { code: 'PT', label: 'Portugal' },
            { code: 'AD', label: 'Andorre' },
            { code: 'IQ', label: 'Iraq' },
            { code: 'ZW', label: 'Zimbabwe' },
        ]
        const result = pipe.transform(
            paysMock().map((apiModel) => apiModel.pays)
        )
        expect(result).toEqual(expectedOrder)
    })
})
