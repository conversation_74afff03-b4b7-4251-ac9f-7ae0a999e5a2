import { Pipe, PipeTransform } from '@angular/core'

@Pipe({
    name: 'formatArrayForTooltip',
    standalone: true,
})
export class FormatArrayForTooltipPipe implements PipeTransform {
    transform(values: string[]): string {
        const filteredArray = values.filter(
            (value) => value !== null && value.length > 0
        )
        if (filteredArray.length === 1) {
            return filteredArray[0]
        } else {
            return filteredArray.join(', ')
        }
    }
}
