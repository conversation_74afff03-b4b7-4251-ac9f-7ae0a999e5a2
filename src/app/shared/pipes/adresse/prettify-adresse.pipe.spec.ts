import { PrettifyAdressePipe } from './prettify-adresse.pipe'
import { adresseBEMock } from '../../mocks/refAdresseMock'

describe('PrettifyAdressePipe', () => {
    let pipe: PrettifyAdressePipe
    beforeEach(() => {
        pipe = new PrettifyAdressePipe()
    })

    it('format adresse as expected', () => {
        expect(pipe.transform(adresseBEMock())).toEqual(
            'Rue arasaka 12, 2077 Night city, BE'
        )
    })
})
