import { Pipe, PipeTransform } from '@angular/core'
import { ReferentielAdresse } from '../../wrappers/personne-wrapper/models/personne/referentiel-adresse'
import { formatAdresseFromReferentiel } from '../../utils/adresse.utils'

@Pipe({
    name: 'prettifyReferentielAdresse',
    standalone: true,
})
export class PrettifyReferentielAdressePipe implements PipeTransform {
    transform(adresse: ReferentielAdresse): string {
        return formatAdresseFromReferentiel(adresse)
    }
}
