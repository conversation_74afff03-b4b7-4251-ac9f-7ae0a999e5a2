import { VehicleBrandToLogoPipe } from './vehicle-brand-to-logo.pipe'

describe('VehicleBrandToLogoPipe', () => {
    const defaultLogo =
        'https://static.foyer.lu/images/static/udb-products/auto.svg'
    it('return a default logo for an undefined input', () => {
        const pipe = new VehicleBrandToLogoPipe()
        expect(pipe.transform(undefined)).toEqual(defaultLogo)
    })

    it('return the right logo for the given input by upercasing and triming input', () => {
        const pipe = new VehicleBrandToLogoPipe()

        const alfaRomeoLogo =
            'https://static.foyer.lu/images/static/eurotax/alfa romeo.svg'
        const vehicleLogoExpectationMap: Map<string, string> = new Map([
            ['alfa romeo', alfaRomeoLogo],
            ['ALFA ROMEO', alfaRomeoLogo],
            ['Alfa Romeo', alfaRomeoLogo],
            [' Alfa Romeo ', alfaRomeoLogo],
            ['ASTONE-MARTINE', defaultLogo],
        ])

        for (const [brandInput, expectedLogo] of vehicleLogoExpectationMap) {
            expect(pipe.transform(brandInput)).toEqual(expectedLogo)
        }
    })
})
