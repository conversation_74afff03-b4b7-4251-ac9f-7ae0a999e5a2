import { Pipe, PipeTransform } from '@angular/core'
import { CodeLabel } from '../models/code-label'

@Pipe({
    name: 'sortCountry',
    standalone: true,
})
export class SortCountryPipe implements PipeTransform {
    private static readonly priorityOrder: string[] = [
        'BE',
        'LU',
        'DE',
        'FR',
        'NL',
        'PT',
    ]
    private static compareCountryNames(a: CodeLabel, b: CodeLabel): number {
        const nameA = a.label
        const nameB = b.label
        return nameA.localeCompare(nameB)
    }

    // eslint-disable-next-line @typescript-eslint/member-ordering
    transform(countries: CodeLabel[]): CodeLabel[] {
        if (!countries) {
            return []
        }

        return countries.sort((a, b) => {
            const indexA = SortCountryPipe.priorityOrder.indexOf(a.code)
            const indexB = SortCountryPipe.priorityOrder.indexOf(b.code)

            if (indexA !== -1 && indexB !== -1) {
                return indexA - indexB
            } else if (indexA !== -1) {
                return -1
            } else if (indexB !== -1) {
                return 1
            } else {
                return SortCountryPipe.compareCountryNames(a, b)
            }
        })
    }
}
