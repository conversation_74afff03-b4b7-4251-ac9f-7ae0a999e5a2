import { FoyerDatePipe } from './foyer-date.pipe'
import { registerLocaleData } from '@angular/common'
import localeFr from '@angular/common/locales/fr'

registerLocaleData(localeFr)

describe('FoyerDatePipe', () => {
    let pipe: FoyerDatePipe

    beforeEach(() => {
        pipe = new FoyerDatePipe('fr-FR')
    })

    it('format the value with dots dd.MM.YYYY', () => {
        expect(pipe.transform(new Date(2022, 8, 15))).toBe('15.09.2022')
    })

    it('return an empty value for an undefined input', () => {
        expect(pipe.transform(undefined)).toBe('')
    })
})
