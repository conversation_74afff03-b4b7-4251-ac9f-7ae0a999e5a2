import { Pipe, PipeTransform } from '@angular/core'
import { DatePipe } from '@angular/common'
import { DateFormat } from 'src/app/shared/utils/date.utils'

@Pipe({
    name: 'foyerDate',
    standalone: true,
})
export class FoyerDatePipe extends DatePipe implements PipeTransform {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    override transform(value: any): any {
        return super.transform(value, DateFormat.DOTTED_DD_MM_YYYY) ?? ''
    }
}
