import { Pipe, PipeTransform } from '@angular/core'
import { KeyValuePipe } from '@angular/common'

// eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unused-vars
const keepOrder = (a: any, b: any) => a

@Pipe({
    name: 'defaultOrderKeyValue',
    standalone: true,
})
export class DefaultOrderKeyValuePipe
    extends KeyValuePipe
    implements PipeTransform
{
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    override transform(value: any): any {
        return super.transform(value, keepOrder)
    }
}
