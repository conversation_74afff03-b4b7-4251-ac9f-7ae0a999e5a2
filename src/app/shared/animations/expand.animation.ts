import { animate, style, transition, trigger } from '@angular/animations'

const DURATION = '400ms cubic-bezier(.48, 0, 0, 1)'

export const expandAnimation = trigger('expand', [
    transition(':enter', [
        style({
            height: 0,
            'margin-top': 0,
            opacity: 0,
            overflow: 'hidden',
            padding: 0,
        }),
        animate(
            DURATION,
            style({ height: '*', 'margin-top': '*', opacity: 1, padding: '*' })
        ),
    ]),
    transition(':leave', [
        style({
            height: '*',
            'margin-top': '*',
            opacity: 1,
            overflow: 'hidden',
            padding: '*',
        }),
        animate(
            DURATION,
            style({ height: 0, 'margin-top': 0, opacity: 0, padding: 0 })
        ),
    ]),
])
