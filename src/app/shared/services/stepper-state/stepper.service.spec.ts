import { TestBed } from '@angular/core/testing'

import { StepperService } from './stepper.service'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import {
    initialStepperAuto,
    initialStepperHabitation,
} from '../../components/stepper-parcours-declaration/models/stepper.model'
import {
    circonstanceHabitationStepperMock,
    dommagesStepperMock,
    garantiesStepperMock,
    modalitesStepperMock,
    piecesJointesStepperMock,
    steppersMock,
} from '../../mocks/stepper.mock'

describe('StepperService', () => {
    let service: StepperService

    beforeEach(() => {
        TestBed.configureTestingModule({})
        service = TestBed.inject(StepperService)
    })

    it('should return empty steppers when no stepper assigned', () => {
        const result = service.getSteppers()
        expect(result()).toEqual([])
    })

    it('should return initial steppers for habitation', () => {
        service.setInitialStepper(DeclarationKind.HABITATION)
        const result = service.getSteppers()
        expect(result()).toEqual(initialStepperHabitation)
    })

    it('should return initial steppers for auto', () => {
        service.setInitialStepper(DeclarationKind.AUTO)
        const result = service.getSteppers()
        expect(result()).toEqual(initialStepperAuto)
    })

    it('should return complete steppers when we assign the different stepper from the page', () => {
        service.setInitialStepper(DeclarationKind.HABITATION)
        service.setStepper(garantiesStepperMock())
        service.setStepper(circonstanceHabitationStepperMock())
        service.setStepper(dommagesStepperMock())
        service.setStepper(modalitesStepperMock())
        service.setStepper(piecesJointesStepperMock())
        const result = service.getSteppers()
        expect(result()).toEqual(steppersMock())
    })
})
