import {
    initialStepperHabitation,
    Stepper,
} from '../../components/stepper-parcours-declaration/models/stepper.model'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { Signal, signal } from '@angular/core'

export class StepperServiceMock {
    getSteppers(): Signal<Stepper[]> {
        return signal(initialStepperHabitation)
    }

    setStepper(stepper: Stepper) {}

    setInitialStepper(type: DeclarationKind): void {}
}
