import { Injectable, Signal, signal } from '@angular/core'
import {
    initialStepperAuto,
    initialStepperHabitation,
    Stepper,
} from '../../components/stepper-parcours-declaration/models/stepper.model'
import { DeclarationKind } from '../../enums/declaration-kind.enum'

@Injectable({
    providedIn: 'root',
})
export class StepperService {
    private readonly steppers = signal<Stepper[]>([])

    getSteppers(): Signal<Stepper[]> {
        return this.steppers.asReadonly()
    }

    setInitialStepper(type: DeclarationKind): void {
        if (type === DeclarationKind.HABITATION) {
            this.steppers.set(initialStepperHabitation)
        } else {
            this.steppers.set(initialStepperAuto)
        }
    }

    setStepper(stepper?: Stepper): void {
        if (stepper) {
            this.steppers.update((currentSteppers) =>
                currentSteppers.map((item) =>
                    item.routingPath === stepper.routingPath ? stepper : item
                )
            )
        }
    }
}
