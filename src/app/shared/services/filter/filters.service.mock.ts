import { BehaviorSubject, Observable, of } from 'rxjs'
import { Filter } from '../../builders/filter'
import { defaultFilters } from '../../mocks/filter.mock'
import { filterSearchPrestatairesMock } from '../../mocks/filters.mock'

export class FiltersServiceMock {
    filtersSubject: BehaviorSubject<Filter[]> = new BehaviorSubject<Filter[]>(
        defaultFilters()
    )

    getFilters$(): Observable<Filter[]> {
        return of(defaultFilters())
    }

    applyNewFilters(newFilters: Filter[]): void {}

    applyDefaultFiltersForDeclaration(): void {}

    removeAllFilters(): void {}

    removeFilter(filter: Filter): void {}

    buildSearchPrestatairesByNomPrenomOrNumeroPersonneFilters(
        criteria: string
    ): Filter[] {
        return filterSearchPrestatairesMock(criteria)
    }
}
