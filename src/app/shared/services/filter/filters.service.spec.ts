import { TestBed } from '@angular/core/testing'

import { FiltersService } from './filters.service'
import { FiltersBuilder } from '../../builders/filters-builder'
import { FilterType } from '../../enums/filter-type.enum'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { FilterDate } from '../../enums/filter-date.enum'
import { defaultFilters, filterContrat } from '../../mocks/filter.mock'
import { TypePrestataire } from '../../enums/type-prestataire.enum'
import { PrestataireKind } from '../../enums/prestataire-kind.enum'

describe('FiltersService', () => {
    let service: FiltersService

    beforeEach(() => {
        TestBed.configureTestingModule({})
        service = TestBed.inject(FiltersService)
    })

    describe('applyNewFilters', () => {
        describe('when the filters are not  GARANTIES or DATE_DE_SURVENANCE', () => {
            it('should add new filter to the list', (done) => {
                const expectedFilter = filterContrat()
                service.applyNewFilters(expectedFilter)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(1)
                    expect(filters).toContain(expectedFilter[0])
                    done()
                })
            })

            it('should replace original filter with replacing filter on the list', (done) => {
                service.applyNewFilters(filterContrat())
                const replacingFilter = new FiltersBuilder()
                    .addFilter(FilterType.CONTRAT, '87654321')
                    .build()
                service.applyNewFilters(replacingFilter)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(1)
                    expect(filters).toContain(replacingFilter[0])
                    done()
                })
            })
        })

        describe('when the filters are GARANTIES', () => {
            it('should add multiple filters when it s GARANTIES filter', (done) => {
                const firstGarantiesFilter = new FiltersBuilder()
                    .addFilter(FilterType.GARANTIES, TypeGaranties.TGN)
                    .build()
                service.applyNewFilters(firstGarantiesFilter)
                const newGarantiesFilter = new FiltersBuilder()
                    .addFilter(FilterType.GARANTIES, TypeGaranties.CNN)
                    .build()
                service.applyNewFilters(newGarantiesFilter)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(2)
                    expect(filters).toContain(firstGarantiesFilter[0])
                    expect(filters).toContain(newGarantiesFilter[0])
                    done()
                })
            })
        })

        describe('when the filters are DATE_DE_SURVENANCE', () => {
            const applyNewDateFilter = (filterDate: FilterDate) => {
                switch (filterDate) {
                    case FilterDate.AUJOURDHUI:
                        const aujourdhui = new FiltersBuilder()
                            .addFilter(FilterType.DATE_DE_SURVENANCE, 'matin')
                            .addFilter(FilterType.DATE_DE_SURVENANCE, 'soir')
                            .build()
                        return service.applyNewFilters(aujourdhui)
                    case FilterDate.SEMAINE:
                        const semaine = new FiltersBuilder()
                            .addFilter(FilterType.DATE_DE_SURVENANCE, 'lundi')
                            .addFilter(
                                FilterType.DATE_DE_SURVENANCE,
                                'dimanche'
                            )
                            .build()
                        return service.applyNewFilters(semaine)
                    case FilterDate.MOIS:
                        const mois = new FiltersBuilder()
                            .addFilter(
                                FilterType.DATE_DE_SURVENANCE,
                                '1er jour'
                            )
                            .addFilter(
                                FilterType.DATE_DE_SURVENANCE,
                                'dernier jour'
                            )
                            .build()
                        return service.applyNewFilters(mois)
                }
            }

            it('should add new filter (DATE) with 2 filters (SEMAINE)', (done) => {
                applyNewDateFilter(FilterDate.SEMAINE)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(2)
                    expect(filters[0].value).toContain('lundi')
                    expect(filters[1].value).toContain('dimanche')
                    done()
                })
            })

            it('should replace filter (DATE -> SEMAINE) with (DATE -> AUJOURDHUI)', (done) => {
                applyNewDateFilter(FilterDate.SEMAINE)
                applyNewDateFilter(FilterDate.AUJOURDHUI)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(2)
                    expect(filters[0].value).toContain('matin')
                    expect(filters[1].value).toContain('soir')
                    done()
                })
            })

            it('should replace filter (DATE -> SEMAINE) with (DATE -> MOIS)', (done) => {
                applyNewDateFilter(FilterDate.SEMAINE)
                applyNewDateFilter(FilterDate.MOIS)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(2)
                    expect(filters[0].value).toContain('1er jour')
                    expect(filters[1].value).toContain('dernier jour')
                    done()
                })
            })

            it('should add new filter (DATE) with 2 filters (MOIS)', (done) => {
                applyNewDateFilter(FilterDate.MOIS)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(2)
                    expect(filters[0].value).toContain('1er jour')
                    expect(filters[1].value).toContain('dernier jour')
                    done()
                })
            })

            it('should replace filter (DATE -> MOIS) with (DATE -> SEMAINE)', (done) => {
                applyNewDateFilter(FilterDate.MOIS)
                applyNewDateFilter(FilterDate.SEMAINE)
                service.getFilters$().subscribe((filters) => {
                    expect(filters.length).toEqual(2)
                    expect(filters[0].value).toContain('lundi')
                    expect(filters[1].value).toContain('dimanche')
                    done()
                })
            })
        })
    })

    describe('when we want to remove the filters', () => {
        beforeEach(() => {
            const originalFilter = new FiltersBuilder()
                .addFilter(FilterType.KIND, 'kind')
                .build()
            service.applyNewFilters(originalFilter)
        })

        it('should remove all the filters', (done) => {
            service.removeAllFilters()
            service.getFilters$().subscribe((filters) => {
                expect(filters.length).toEqual(0)
                done()
            })
        })

        it('should remove expected filter', (done) => {
            const expectedFilter = filterContrat()
            service.applyNewFilters(expectedFilter)
            service.removeFilter(expectedFilter[0])
            service.getFilters$().subscribe((filters) => {
                expect(filters.length).toEqual(1)
                expect(filters).not.toContain(expectedFilter[0])
                done()
            })
        })
    })

    it('should set the default filters', (done) => {
        service.applyDefaultFiltersForDeclaration()
        service.getFilters$().subscribe((result) => {
            expect(result).toEqual(defaultFilters())
            done()
        })
    })

    describe('buildSearchPrestatairesByNomPrenomFilters', () => {
        it('should build filters with RSQL query for searching prestataires by nomPrenom or numeroPersonne', () => {
            const nomPrenom = 'Garage Test'
            const result =
                service.buildSearchPrestatairesByNomPrenomOrNumeroPersonneFilters(
                    nomPrenom
                )

            expect(Array.isArray(result)).toBeTrue()
            expect(result.length).toBeGreaterThan(0)

            const rsqlFilter = result.find(
                (filter) => filter.type === FilterType.CUSTOM_RSQL_QUERY_TYPE
            )
            expect(rsqlFilter).toBeDefined()
            expect(rsqlFilter?.value).toContain(
                'prestataire.nomPrenom=~"Garage Test" OR prestataire.numeroPersonne=~"Garage Test"'
            )
            expect(rsqlFilter?.value).toContain(TypePrestataire.GARAGE)
            expect(rsqlFilter?.value).toContain(PrestataireKind.ACTIF)
        })

        it('should handle empty criteria parameter', () => {
            const nomPrenomOrNumeroPersonne = ''
            const result =
                service.buildSearchPrestatairesByNomPrenomOrNumeroPersonneFilters(
                    nomPrenomOrNumeroPersonne
                )

            expect(Array.isArray(result)).toBeTrue()
            expect(result.length).toBeGreaterThan(0)

            const rsqlFilter = result.find(
                (filter) => filter.type === FilterType.CUSTOM_RSQL_QUERY_TYPE
            )
            expect(rsqlFilter).toBeDefined()

            expect(rsqlFilter?.value).toContain(TypePrestataire.GARAGE)
            expect(rsqlFilter?.value).toContain(PrestataireKind.ACTIF)
        })
    })
})
