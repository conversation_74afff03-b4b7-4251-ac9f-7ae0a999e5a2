import { Injectable } from '@angular/core'
import { BehaviorSubject, Observable } from 'rxjs'
import { Filter } from '../../builders/filter'
import { FilterType } from '../../enums/filter-type.enum'
import { FiltersBuilder } from '../../builders/filters-builder'
import { RSQLFilterList } from '../../builders/expressions/rsql-filter-list'
import { LikeRSQLFilterExpression } from '../../builders/expressions/like-rsql-filter-expression'
import { TypePrestataire } from '../../enums/type-prestataire.enum'
import { PrestataireKind } from '../../enums/prestataire-kind.enum'
import { EqualToRSQLFilterExpression } from '../../builders/expressions/equalto-rsql-filter-expression'
import { GroupRSQLFilterExpression } from '../../builders/expressions/group-rsql-filter-expression'

@Injectable({
    providedIn: 'root',
})
export class FiltersService {
    private readonly filtersSubject: BehaviorSubject<Filter[]> =
        new BehaviorSubject<Filter[]>([])

    getFilters$(): Observable<Filter[]> {
        return this.filtersSubject.asObservable()
    }

    applyNewFilters(newFilters: Filter[]): void {
        const hasDateLimiteTraitement = newFilters.some(
            (filter) => filter.type === FilterType.DATE_DE_SURVENANCE
        )
        const hasGaranties = newFilters.some(
            (filter) => filter.type === FilterType.GARANTIES
        )
        if (hasDateLimiteTraitement) {
            this.replaceNewDateFilters(newFilters)
        } else if (hasGaranties) {
            this.replaceNewGarantiesFilter(newFilters[0])
        } else if (newFilters.length === 1) {
            this.replaceNewFilters(newFilters[0])
        }
    }

    applyDefaultFiltersForDeclaration(): void {
        const defaultFilters = new FiltersBuilder()
            .addFilter(
                FilterType.WITHOUT_KIND,
                'urn:api:sinistres:declaration:states:abandonne'
            )
            .build()
        this.filtersSubject.next(defaultFilters)
    }

    removeAllFilters(): void {
        this.filtersSubject.next([])
    }

    removeFilter(filter: Filter): void {
        const newFilters = this.filtersSubject.value.filter((f) =>
            filter.type === FilterType.DATE_DE_SURVENANCE
                ? f.type !== filter.type
                : f !== filter
        )
        this.filtersSubject.next(newFilters)
    }

    buildSearchPrestatairesByNomPrenomOrNumeroPersonneFilters(
        criteria: string
    ): Filter[] {
        const filters = new FiltersBuilder()
        const searchGroup = new RSQLFilterList()
            .or(
                new LikeRSQLFilterExpression(
                    FilterType.PRESTATAIRE_NOM_PRENOM,
                    criteria
                )
            )
            .or(
                new LikeRSQLFilterExpression(
                    FilterType.PRESTATAIRE_NUMERO_PERSONNE,
                    criteria
                )
            )

        const searchFilter = new GroupRSQLFilterExpression(searchGroup)

        const query = new RSQLFilterList()
            .and(searchFilter)
            .and(
                new EqualToRSQLFilterExpression(
                    FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                    TypePrestataire.GARAGE
                )
            )
            .and(
                new EqualToRSQLFilterExpression(
                    FilterType.KIND,
                    PrestataireKind.ACTIF
                )
            )
        return filters.withRSQLCustomQuery(query).build()
    }

    private replaceNewFilters(newFilter: Filter): void {
        const sameTypeIndex = this.filtersSubject.value.findIndex(
            (filter) => filter.type === newFilter.type
        )
        if (sameTypeIndex === -1) {
            const newFiltersArray = [...this.filtersSubject.value, newFilter]
            this.filtersSubject.next(newFiltersArray)
        } else {
            const newFiltersArray = [
                ...this.filtersSubject.value.slice(0, sameTypeIndex),
                newFilter,
                ...this.filtersSubject.value.slice(sameTypeIndex + 1),
            ]
            this.filtersSubject.next(newFiltersArray)
        }
    }

    private replaceNewGarantiesFilter(newFilters: Filter) {
        const newFiltersArray = [...this.filtersSubject.value, ...[newFilters]]
        this.filtersSubject.next(newFiltersArray)
    }

    private replaceNewDateFilters(newFilters: Filter[]) {
        const dateLimiteTraitements = this.filtersSubject.value.filter(
            (filter) => filter.type === FilterType.DATE_DE_SURVENANCE
        )
        if (dateLimiteTraitements.length >= 1) {
            const firstIndex = this.filtersSubject.value.indexOf(
                dateLimiteTraitements[0]
            )
            const lastIndex = firstIndex + dateLimiteTraitements.length
            const newFiltersArray = [
                ...this.filtersSubject.value.slice(0, firstIndex),
                ...newFilters,
                ...this.filtersSubject.value.slice(lastIndex),
            ]
            this.filtersSubject.next(newFiltersArray)
        } else {
            const newFiltersArray = [
                ...this.filtersSubject.value,
                ...newFilters,
            ]
            this.filtersSubject.next(newFiltersArray)
        }
    }
}
