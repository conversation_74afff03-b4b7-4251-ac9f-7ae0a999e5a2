import { inject, Injectable } from '@angular/core'
import { ParamsBuilder } from '../../builders/params-builder'
import { PageSize } from '../../enums/page-size.enum'
import { CodeLabel } from '../../models/code-label'
import { map, Observable } from 'rxjs'
import { AdresseWrapper } from '../../wrappers/adresse/adresse-wrapper.service'

@Injectable({
    providedIn: 'root',
})
export class AdresseService {
    private readonly adresseWrapper: AdresseWrapper = inject(AdresseWrapper)

    getPays(): Observable<CodeLabel[]> {
        const params = new ParamsBuilder()
            .addParam('page[size]', PageSize.TWO_HUNDRED_FIFTY_ELEMENTS)
            .build()
        return this.adresseWrapper
            .getPays(params)
            .pipe(
                map((response) =>
                    response.data.map((data) => data.attributes.pays)
                )
            )
    }
}
