import { TestBed } from '@angular/core/testing'
import { AdresseService } from './adresse.service'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { AdresseWrapper } from '../../wrappers/adresse/adresse-wrapper.service'
import { paysMock } from '../../mocks/refAdresseMock'
import { AdresseWrapperMock } from '../../wrappers/adresse/adresse-wrapper.service.spec.mock'

describe('AdresseService', () => {
    let service: AdresseService
    let adresseWrapper: AdresseWrapper

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideHttpClientTesting(),
                {
                    provide: AdresseWrapper,
                    useClass: AdresseWrapperMock,
                },
            ],
        })

        service = TestBed.inject(AdresseService)
        adresseWrapper = TestBed.inject(AdresseWrapper)
    })

    describe('getPays', () => {
        it('should execute getPays from wrapper with right params return a list of pays', (done) => {
            spyOn(adresseWrapper, 'getPays').and.callThrough()
            service.getPays().subscribe((result) => {
                expect(result).toEqual(
                    paysMock().map((apiModel) => apiModel.pays)
                )
                done()
            })
        })
    })
})
