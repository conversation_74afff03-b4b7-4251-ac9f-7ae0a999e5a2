import { TestBed } from '@angular/core/testing'

import { RisquesService } from './risques.service'
import { SituationsContratsWrapper } from '../../wrappers/situations-contrats-wrapper/situations-contrats-wrapper.service'
import { SituationsContratsWrapperServiceSpecMock } from '../../wrappers/situations-contrats-wrapper/situations-contrats-wrapper.service.spec.mock'
import { DATE_ENTREE_EN_VIGUEUR } from '../../mocks/garanties.mock'
import { risquesMock } from '../../mocks/risques.mock'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { provideTranslation } from '../../../core/providers/translation.provider'

describe('RisquesService', () => {
    let service: RisquesService
    let translationService: TranslationService

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideTranslation(),
                {
                    provide: SituationsContratsWrapper,
                    useClass: SituationsContratsWrapperServiceSpecMock,
                },
            ],
        })
        service = TestBed.inject(RisquesService)
        translationService = TestBed.inject(TranslationService)
    })

    it('should be created', () => {
        expect(service).toBeTruthy()
    })

    describe('findRisques', () => {
        it('should return mapped risque when results are found', (done) => {
            service
                .findRisques('123456', DATE_ENTREE_EN_VIGUEUR)
                .subscribe((result) => {
                    const expected = risquesMock()[0]
                    expect(result[0].preneur?.numeroPersonne).toEqual(
                        expected.preneur?.numeroPersonne
                    )
                    expect(result[0].garanties).toEqual(expected.garanties)
                    done()
                })
        })
    })

    describe('getSituationContratRisque', () => {
        it('should return mapped risque based on the current language', (done) => {
            spyOn(translationService, 'getCurrentLanguage$').and.callThrough()
            service.getSituationContratRisque('123456').subscribe((result) => {
                const expected = risquesMock()[0]
                expect(
                    translationService.getCurrentLanguage$
                ).toHaveBeenCalled()
                expect(result.garanties).toEqual(expected.garanties)
                done()
            })
        })
    })
})
