import { Injectable } from '@angular/core'
import { map, Observable, switchMap } from 'rxjs'
import { SituationsContratsWrapper } from '../../wrappers/situations-contrats-wrapper/situations-contrats-wrapper.service'
import { Risque } from '../../models/risque'
import {
    mapAllSituationsRisquesToRisques,
    mapSituationContratRisqueToRisque,
} from 'src/app/shared/utils/risque.utils'
import { TranslationService } from '../../../core/services/translation/translation.service'

@Injectable({
    providedIn: 'root',
})
export class RisquesService {
    constructor(
        private readonly situationsContratsWrapper: SituationsContratsWrapper,
        private readonly translationService: TranslationService
    ) {}

    findRisques(
        numeroPolice: string,
        dateSurvenance: Date
    ): Observable<Risque[]> {
        return this.situationsContratsWrapper
            .findSituationsContratsRisques(numeroPolice, dateSurvenance)
            .pipe(map(mapAllSituationsRisquesToRisques))
    }

    getSituationContratRisque(
        idSituationContratRisque: string
    ): Observable<Risque> {
        return this.translationService.getCurrentLanguage$().pipe(
            switchMap((language) =>
                this.situationsContratsWrapper.getSituationContratRisque(
                    idSituationContratRisque,
                    language
                )
            ),
            map(mapSituationContratRisqueToRisque)
        )
    }
}
