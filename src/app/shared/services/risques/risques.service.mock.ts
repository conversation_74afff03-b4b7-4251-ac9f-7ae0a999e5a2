import { Observable, of } from 'rxjs'
import { SituationContratRisque } from '../../wrappers/situations-contrats-wrapper/models/situation-contrat.risque'
import { risquesMock, situationContratMock } from '../../mocks/risques.mock'
import { Risque } from '../../models/risque'

export class RisquesServiceMock {
    findRisques(
        numeroPolice: string,
        dateSurvenance: Date
    ): Observable<Risque[]> {
        return of(risquesMock())
    }

    getSituationContratRisque(
        idSituationContratRisque: string
    ): Observable<Risque> {
        return of(risquesMock()[0])
    }

    getSituationContrat(
        idSituationContratRisque: string
    ): Observable<SituationContratRisque> {
        return of(situationContratMock())
    }
}
