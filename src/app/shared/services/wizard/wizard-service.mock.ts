import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { WizardSteps } from '../../enums/wizard-steps.enum'
import { baseWizardStepConfigs } from '../../configs/wizard-steps.config'
import { WizardAction } from '../../enums/wizard-action.enum'
import { WizardStepConfig } from '../../models/wizard-step-config'
import { Router } from '@angular/router'
import { signal, WritableSignal } from '@angular/core'

export class WizardServiceMock {
    declarationKind: WritableSignal<DeclarationKind> = signal(
        DeclarationKind.AUTO
    )
    currentStep: WritableSignal<WizardStepConfig> = signal(
        baseWizardStepConfigs[WizardSteps.CIRCONSTANCE]
    )
    wizardState: WritableSignal<WizardAction | null> = signal(
        WizardAction.ON_NEXT
    )

    setDeclarationKind(kind: DeclarationKind): void {
        this.declarationKind.set(kind)
    }

    setCurrentStep(newStep: WizardStepConfig): void {
        this.currentStep.set(newStep)
    }

    updateState(action: WizardAction): void {
        this.wizardState.set(action)
    }

    getStepConfig(step: WizardSteps): WizardStepConfig {
        return (
            baseWizardStepConfigs[step] ??
            baseWizardStepConfigs[WizardSteps.CIRCONSTANCE]
        )
    }

    getNextStep(currentStep: WizardSteps): WizardSteps {
        return currentStep
    }

    navigateToStep(step: WizardSteps, router: Router): Promise<boolean> {
        return Promise.resolve(true)
    }
}
