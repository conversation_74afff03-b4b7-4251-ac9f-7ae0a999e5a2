import { fakeAsync, TestBed, tick } from '@angular/core/testing'
import { WizardService } from './wizard.service'
import { WizardAction } from '../../enums/wizard-action.enum'
import { WizardSteps } from '../../enums/wizard-steps.enum'
import {
    autoStepSequence,
    autoWizardStepConfigs,
    baseWizardStepConfigs,
    habitationStepSequence,
    habitationWizardStepConfigs,
} from '../../configs/wizard-steps.config'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { provideRouter, Route, Router } from '@angular/router'
import { RoutingPath } from '../../../routing-path.enum'
import { Component } from '@angular/core'

@Component({
    standalone: true,
    template: '<p>Dummy</p>',
})
class DummyComponent {}

const routes: Route[] = [
    {
        path: RoutingPath.AUTO,
        children: [
            {
                path: autoWizardStepConfigs[WizardSteps.CIRCONSTANCE].routePath,
                loadComponent: () => Promise.resolve(DummyComponent),
            },
        ],
    },
    {
        path: RoutingPath.HABITATION,
        children: [
            {
                path: habitationWizardStepConfigs[WizardSteps.CIRCONSTANCE]
                    .routePath,
                loadComponent: () => Promise.resolve(DummyComponent),
            },
        ],
    },
]

describe('WizardService', () => {
    let service: WizardService
    let router: Router

    beforeEach(async () => {
        TestBed.configureTestingModule({
            providers: [provideRouter(routes)],
        })
        service = TestBed.inject(WizardService)
        router = TestBed.inject(Router)
        spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
    })

    describe('setCurrentStep', () => {
        it('should update the current step', () => {
            const initialStep = service.currentStep$()
            expect(initialStep).toBeDefined()

            const newStep = WizardSteps.CIRCONSTANCE
            service.setCurrentStep(newStep)

            const updatedStep = service.currentStep$()
            expect(updatedStep.step).toEqual(newStep)
        })
    })

    describe('currentStep', () => {
        it('should return the initial wizard step', () => {
            const step = service.currentStep$()
            expect(step).toEqual(
                baseWizardStepConfigs[WizardSteps.SELECTION_RISQUE]
            )
        })
    })

    describe('wizardState', () => {
        it('should return null initially', () => {
            expect(service.wizardState$()).toBeNull()
        })

        it('should update wizardState when updateState is called', () => {
            const action = WizardAction.ON_NEXT
            service.updateState(action)
            expect(service.wizardState$()).toEqual(action)
        })
    })

    describe('updateState', () => {
        it('should update wizardState with the last action', () => {
            const actions = [WizardAction.ON_NEXT, WizardAction.ON_PREVIOUS]
            actions.forEach((action) => service.updateState(action))
            expect(service.wizardState$()).toEqual(actions[actions.length - 1])
        })
    })

    describe('getStepConfig', () => {
        it('should return the correct step config', () => {
            const step =
                baseWizardStepConfigs[WizardSteps.SELECTION_RISQUE].step
            const config = service.getStepConfig(step)
            expect(config).toEqual(
                baseWizardStepConfigs[WizardSteps.SELECTION_RISQUE]
            )
        })

        it('should throw an error if the step configuration is not found', () => {
            expect(() =>
                service.getStepConfig('INVALID_STEP' as WizardSteps)
            ).toThrowError('No configuration found for step INVALID_STEP')
        })
    })

    describe('getNextStep', () => {
        it('should return the next step for AUTO', () => {
            service.setDeclarationKind(DeclarationKind.AUTO)
            const currentStep = WizardSteps.SELECTION_RISQUE
            const nextStep = service.getNextStep(currentStep)
            expect(nextStep).toEqual(
                autoStepSequence[autoStepSequence.indexOf(currentStep) + 1]
            )
        })

        it('should return the next step for HABITATION', () => {
            service.setDeclarationKind(DeclarationKind.HABITATION)
            const currentStep = WizardSteps.SELECTION_RISQUE
            const nextStep = service.getNextStep(currentStep)
            expect(nextStep).toEqual(
                habitationStepSequence[
                    habitationStepSequence.indexOf(currentStep) + 1
                ]
            )
        })

        it('should return the same step if it is the last step', () => {
            service.setDeclarationKind(DeclarationKind.HABITATION)
            const lastStep =
                habitationStepSequence[habitationStepSequence.length - 1]
            const nextStep = service.getNextStep(lastStep)
            expect(nextStep).toEqual(lastStep)
        })
    })

    describe('navigateToStep', () => {
        it('should navigate to AUTO route with correct path', fakeAsync(() => {
            service.setDeclarationKind(DeclarationKind.AUTO)
            const step = WizardSteps.CIRCONSTANCE

            service.navigateToStep(step, router)

            tick()

            expect(router.navigate).toHaveBeenCalledWith([
                RoutingPath.AUTO,
                autoWizardStepConfigs[step].routePath,
            ])
        }))

        it('should navigate to HABITATION route with correct path', fakeAsync(() => {
            service.setDeclarationKind(DeclarationKind.HABITATION)
            const step = WizardSteps.CIRCONSTANCE

            service.navigateToStep(step, router)

            tick()

            expect(router.navigate).toHaveBeenCalledWith([
                RoutingPath.HABITATION,
                habitationWizardStepConfigs[step].routePath,
            ])
        }))
    })
})
