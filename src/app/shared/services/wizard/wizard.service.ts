import { computed, Injectable, signal, WritableSignal } from '@angular/core'
import { Router } from '@angular/router'
import { WizardAction } from '../../enums/wizard-action.enum'
import {
    autoStepSequence,
    autoWizardStepConfigs,
    baseWizardStepConfigs,
    habitationStepSequence,
    habitationWizardStepConfigs,
} from '../../configs/wizard-steps.config'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { WizardSteps } from '../../enums/wizard-steps.enum'
import { WizardStepConfig } from '../../models/wizard-step-config'
import { RoutingPath } from '../../../routing-path.enum'

@Injectable({
    providedIn: 'root',
})
export class WizardService {
    private readonly declarationKind: WritableSignal<DeclarationKind> = signal(
        DeclarationKind.HABITATION
    )
    private readonly wizardStepConfigs = computed(() =>
        this.declarationKind() === DeclarationKind.AUTO
            ? autoWizardStepConfigs
            : habitationWizardStepConfigs
    )
    private readonly stepSequence = computed(() =>
        this.declarationKind() === DeclarationKind.AUTO
            ? autoStepSequence
            : habitationStepSequence
    )
    private readonly currentStep: WritableSignal<WizardStepConfig> = signal(
        baseWizardStepConfigs[WizardSteps.SELECTION_RISQUE]
    )
    private readonly wizardState = signal<WizardAction | null>(null)

    get currentStep$() {
        return this.currentStep.asReadonly()
    }

    get wizardState$() {
        return this.wizardState.asReadonly()
    }

    setDeclarationKind(kind: DeclarationKind) {
        this.declarationKind.set(kind)
    }

    getStepConfig(step: WizardSteps): WizardStepConfig {
        const config = this.wizardStepConfigs()[step]
        if (!config) {
            throw new Error(`No configuration found for step ${step}`)
        }
        return config
    }

    setCurrentStep(step: WizardSteps) {
        const config = this.getStepConfig(step)
        this.currentStep.set(config)
    }

    updateState(action: WizardAction) {
        this.wizardState.set(action)
    }

    getNextStep(currentStep: WizardSteps): WizardSteps {
        const sequence = this.stepSequence()
        if (currentStep === WizardSteps.LISTE_DECLARATION) {
            return sequence[1]
        }
        const currentIndex = sequence.indexOf(currentStep)
        if (currentIndex >= 0 && currentIndex < sequence.length - 1) {
            return sequence[currentIndex + 1]
        }
        return currentStep
    }

    navigateToStep(step: WizardSteps, router: Router): Promise<boolean> {
        const config = this.getStepConfig(step)
        const routeSegment =
            this.declarationKind() === DeclarationKind.AUTO
                ? RoutingPath.AUTO
                : RoutingPath.HABITATION
        return router.navigate([routeSegment, config.routePath])
    }
}
