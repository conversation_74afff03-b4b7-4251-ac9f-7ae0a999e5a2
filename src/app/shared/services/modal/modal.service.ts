import { Injectable, Type, ViewContainerRef } from '@angular/core'
import { delay, race } from 'rxjs'
import { HideableModalComponent } from '../../components/hideable-modal.component'
import { NotificationMessage } from '../../models/notification.model'
import { NotificationSnackbarComponent } from '../../components/notification-snackbar/notification-snackbar.component'
import { ConfirmModalConfig } from '../../models/confirm-modal-config'
import { ConfirmModalComponent } from '../../components/confirm-modal/confirm-modal.component'

@Injectable({
    providedIn: 'root',
})
export class ModalService {
    showAsComponent<R, S, T extends HideableModalComponent<R, S>>(
        modalComponentType: Type<T>,
        viewContainerRef: ViewContainerRef,
        inputs?: S,
        animationTimeout: number = 300
    ): T {
        // Create the component and wire it up with the element
        const modalComponentRef =
            viewContainerRef.createComponent<T>(modalComponentType)
        const instance = modalComponentRef.instance
        if (inputs && instance.onInjectInputs) {
            instance.onInjectInputs(inputs)
        }
        instance.show()

        // Listen to the close event
        const closeSubscription = race(
            instance.closed,
            instance.closedWithResult
        ).subscribe(() => {
            instance.hide()
        })

        const afterCloseSubscription = instance.modalHidden
            .pipe(delay(animationTimeout)) // add some delay to preserve FDS closing animation
            .subscribe(() => {
                modalComponentRef.destroy()
            })
        modalComponentRef.onDestroy(() => {
            closeSubscription.unsubscribe()
            afterCloseSubscription.unsubscribe()
        })

        return instance
    }

    showNotification(
        inputs: NotificationMessage,
        viewContainerRef: ViewContainerRef
    ): NotificationSnackbarComponent {
        return this.showAsComponent<
            void,
            NotificationMessage,
            NotificationSnackbarComponent
        >(NotificationSnackbarComponent, viewContainerRef, inputs)
    }

    showConfirmModal(
        viewContainerRef: ViewContainerRef,
        config: ConfirmModalConfig
    ): ConfirmModalComponent {
        return this.showAsComponent<
            void,
            ConfirmModalConfig,
            ConfirmModalComponent
        >(ConfirmModalComponent, viewContainerRef, config)
    }
}
