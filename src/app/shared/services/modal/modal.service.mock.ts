import { EventEmitter, Type, ViewContainerRef } from '@angular/core'
import { HideableModalComponent } from '../../components/hideable-modal.component'
import { NotificationSnackbarComponent } from '../../components/notification-snackbar/notification-snackbar.component'
import { ViewContainerHostDirective } from '../../directives/view-container-host.directive'
import { ConfirmModalComponent } from '../../components/confirm-modal/confirm-modal.component'
import { ConfirmModalConfig } from '../../models/confirm-modal-config'
import { NotificationMessage } from '../../models/notification.model'

export class ModalServiceMock {
    showAsComponent<R, S, T extends HideableModalComponent<R, S>>(
        modalComponentType: Type<T>,
        viewContainerRef: ViewContainerRef,
        inputs?: unknown
    ): T {
        return {
            closedWithResult: new EventEmitter<R>(),
        } as T
    }

    showNotification(
        inputs: NotificationMessage,
        viewContainerRef: ViewContainerRef
    ): NotificationSnackbarComponent {
        return {} as NotificationSnackbarComponent
    }

    logAndShowErrorNotification(
        err: any,
        errorMessage: string,
        notificationHost: ViewContainerHostDirective
    ): void {}

    showConfirmModal(
        viewContainerRef: ViewContainerRef,
        config: ConfirmModalConfig
    ): ConfirmModalComponent {
        return {
            closedWithResult: new EventEmitter<void>(),
        } as ConfirmModalComponent
    }
}
