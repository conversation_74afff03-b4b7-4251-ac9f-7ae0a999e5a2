import { TestBed } from '@angular/core/testing'

import { StorageService } from './storage.service'

interface TestObject {
    isNiceValue: boolean
}

describe('StorageService', () => {
    let service: StorageService

    beforeEach(() => {
        TestBed.configureTestingModule({})
        service = TestBed.inject(StorageService)
        service.clear()
    })

    it('should return null if key is not set', () => {
        expect(service.retrieve<string>('cubic')).toBe(null)
    })

    it('should return the a string value if set', () => {
        service.store('toto', 'testValue')
        expect(service.retrieve<string>('toto')).toBe('testValue')
    })

    it('should return a boolean value if set', () => {
        service.store('booboo', true)
        expect(service.retrieve<boolean>('booboo')).toBe(true)
    })

    it('should return an object value if set', () => {
        const expectedObject: TestObject = { isNiceValue: true }
        service.store('obj', expectedObject)
        expect(service.retrieve<TestObject>('obj')).toEqual(expectedObject)
    })

    describe('When isTokenExpired method is called', () => {
        it('should return false if token is not expired', () => {
            service.store('expires_at', 9999999999999)
            expect(service.isTokenExpired()).toBeFalsy()
        })

        it('should return true if token is expired', () => {
            service.store('expires_at', 1000)
            expect(service.isTokenExpired()).toBeTruthy()
        })
    })
})
