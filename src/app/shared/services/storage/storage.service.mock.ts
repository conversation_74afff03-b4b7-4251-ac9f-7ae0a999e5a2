export class StorageServiceMock {
    fakeStore = new Map<string, any>()

    retrieve<T>(key: string): T | null {
        const newVar = this.fakeStore.get(key)
        if (newVar === undefined) {
            return null
        } else {
            return newVar as T
        }
    }

    store<T>(key: string, value: T) {
        this.fakeStore.set(key, value)
    }

    clear() {
        this.fakeStore.clear()
    }

    isTokenExpired(): boolean {
        return false
    }
}
