import { Injectable } from '@angular/core'

@Injectable({
    providedIn: 'root',
})
export class StorageService {
    private readonly EXPIRES_AT_KEY = 'expires_at'

    retrieve<T>(key: string): T | null {
        const result = sessionStorage.getItem(key)
        if (result) {
            try {
                return JSON.parse(result)
            } catch (e) {
                throw new Error(
                    `The result for retrieving ${key} is not a valid json ${result}`
                )
            }
        } else {
            return null
        }
    }

    store<T>(key: string, value: T) {
        sessionStorage.setItem(key, JSON.stringify(value))
    }

    clear() {
        sessionStorage.clear()
    }

    isTokenExpired(): boolean {
        const tokenExpiresValue = sessionStorage.getItem(this.EXPIRES_AT_KEY)
        if (tokenExpiresValue) {
            return +tokenExpiresValue - new Date().getTime() <= 0
        }
        return true
    }
}
