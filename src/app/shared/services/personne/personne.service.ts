import { inject, Injectable } from '@angular/core'
import { Observable } from 'rxjs'
import { PersonneWrapper } from '../../wrappers/personne-wrapper/personne-wrapper.service'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'

@Injectable({
    providedIn: 'root',
})
export class PersonneService {
    private readonly personneWrapper: PersonneWrapper = inject(PersonneWrapper)

    load(numeroPersonne: string): Observable<Personne> {
        return this.personneWrapper.getPersonne(numeroPersonne)
    }
}
