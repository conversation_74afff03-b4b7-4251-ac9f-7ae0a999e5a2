import { TestBed } from '@angular/core/testing'
import { PersonneService } from './personne.service'
import { of } from 'rxjs'
import { PersonneWrapperMock } from '../../wrappers/personne-wrapper/personne-wrapper.service.spec.mock'
import { PersonneWrapper } from '../../wrappers/personne-wrapper/personne-wrapper.service'
import {
    personneMoraleMock,
    personnePhysiqueMock,
} from '../../mocks/personne.mock'
import Spy = jasmine.Spy

describe('PersonneService', () => {
    let service: PersonneService
    let personneWrapper: PersonneWrapper

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                {
                    provide: PersonneWrapper,
                    useClass: PersonneWrapperMock,
                },
            ],
        })
        service = TestBed.inject(PersonneService)
        personneWrapper = TestBed.inject(PersonneWrapper)
    })

    describe('When the load method is called ', () => {
        describe('When it is a personne phyisique', () => {
            let intervenantWrapperFindOne: Spy

            beforeEach(() => {
                intervenantWrapperFindOne = spyOn(
                    personneWrapper,
                    'getPersonne'
                ).and.returnValue(of(personnePhysiqueMock()))
                service.load('4444')
            })

            it('should call findOne method', () => {
                expect(intervenantWrapperFindOne).toHaveBeenCalledOnceWith(
                    '4444'
                )
            })

            it('should return mapped personne when results are found', (done) => {
                service.load('123456789').subscribe((result) => {
                    expect(result).toEqual(personnePhysiqueMock())
                    done()
                })
            })
        })

        describe('When it is a personne morale', () => {
            let intervenantWrapperFindOne: Spy

            beforeEach(() => {
                intervenantWrapperFindOne = spyOn(
                    personneWrapper,
                    'getPersonne'
                ).and.returnValue(of(personneMoraleMock()))
                service.load('5555')
            })

            it('should call findOne method', () => {
                expect(intervenantWrapperFindOne).toHaveBeenCalledOnceWith(
                    '5555'
                )
            })

            it('should return mapped personne when results are found', (done) => {
                service.load('123456789').subscribe((result) => {
                    expect(result).toEqual(personneMoraleMock())
                    done()
                })
            })
        })
    })
})
