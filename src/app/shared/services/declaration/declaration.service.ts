import { Injectable } from '@angular/core'
import {
    BehaviorSubject,
    filter,
    map,
    Observable,
    Subscription,
    switchMap,
    take,
    tap,
} from 'rxjs'

import { DeclarationWrapper } from '../../wrappers/declaration/declaration-wrapper.service'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { Router } from '@angular/router'
import { PageSize } from '../../enums/page-size.enum'
import { ParamsBuilder } from '../../builders/params-builder'
import { FiltersService } from '../filter/filters.service'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { DeclarationAutoForm } from '../../../auto/models/forms/declaration-auto.form'
import { DeclarationHabitationForm } from 'src/app/habitation/models/forms/declaration-habitation.form'
import { Declaration } from '../../models/declaration'
import { CirconstanceSinistre } from '../../models/circonstance-sinistre'
import { DeclarationResult } from '../../models/declaration-result'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { DeclarationSoumiseResult } from '../../models/declaration-soumise-result'
import { ListeDeclarationPageItem } from '../../models/liste-declaration-page-item'
import { Pageable } from '../../models/pageable.model'
import { RoutingPath } from '../../../routing-path.enum'
import { jsonAPIToPageable } from '../../utils/pagination-utils'
import { mapToListeDeclarationPageItem } from '../../utils/declaration.utils'
import { Survenance } from '../../models/survenance'
import { ModalitesHabitation } from '../../../habitation/models/modalites-habitation'
import { Garage } from '../../../auto/models/garage'
import { DeclarationAuto } from '../../../auto/models/declaration-auto'
import { DeclarationHabitation } from '../../../habitation/models/declaration-habitation'
import { DetailsSinistreHabitation } from '../../../habitation/models/details-sinistre-habitation'
import { DommagesHabitation } from '../../../habitation/models/dommages-habitation'
import { DommagesAuto } from '../../../auto/models/dommages-auto'
import { ModalitesAuto } from '../../../auto/models/modalites-auto'
import { CauseSinistre } from '../../../auto/enums/cause-sinistre.enum'
import { DetailsSinistreAuto } from '../../../auto/models/details-sinistre-auto'
import { SurvenanceAuto } from '../../../auto/models/survenance-auto'
import { ConducteurSinistre } from '../../../auto/models/conducteur-sinistre'

@Injectable({
    providedIn: 'root',
})
export class DeclarationService {
    private form?: DeclarationHabitationForm | DeclarationAutoForm

    private readonly declarationSubject$: BehaviorSubject<
        Declaration | undefined
    > = new BehaviorSubject<Declaration | undefined>(undefined)
    private readonly declarationSubjectObservable$: Observable<
        Declaration | undefined
    > = this.declarationSubject$.asObservable()

    constructor(
        private readonly declarationWrapper: DeclarationWrapper,
        private readonly router: Router,
        private readonly filtersService: FiltersService
    ) {}

    getDeclarationAutoForm(): DeclarationAutoForm {
        return this.form as DeclarationAutoForm
    }

    getDeclarationHabitationForm(): DeclarationHabitationForm {
        return this.form as DeclarationHabitationForm
    }

    initializeForm(declarationKind: DeclarationKind): void {
        if (declarationKind === DeclarationKind.HABITATION) {
            this.form = new DeclarationHabitationForm()
        } else if (declarationKind === DeclarationKind.AUTO) {
            this.form = new DeclarationAutoForm()
        } else {
            throw new Error(`Unsupported declaration kind: ${declarationKind}`)
        }
    }

    getDeclaration$(): Observable<Declaration> {
        return this.declarationSubjectObservable$.pipe(
            filter((element) => element !== undefined),
            map((element) => element as unknown as Declaration)
        )
    }

    getDeclarationHabitation$(): Observable<DeclarationHabitation> {
        return this.declarationSubjectObservable$.pipe(
            filter(
                (element) =>
                    element !== undefined &&
                    element._kind === DeclarationKind.HABITATION
            ),
            map((element) => element as unknown as DeclarationHabitation)
        )
    }

    getDeclarationAuto$(): Observable<DeclarationAuto> {
        return this.declarationSubjectObservable$.pipe(
            filter(
                (element) =>
                    element !== undefined &&
                    element._kind === DeclarationKind.AUTO
            ),
            map((element) => element as unknown as DeclarationAuto)
        )
    }

    clearDeclaration(): void {
        this.form?.reset()
        this.declarationSubject$.next(undefined)
    }

    updateDeclaration(declaration: Declaration): void {
        this.declarationSubject$.next(declaration)
    }

    updateGarantiesAndReferenceProducteurForHabitation(
        garanties?: TypeGaranties[],
        referenceProducteur?: string
    ): void {
        this.getDeclarationHabitation$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    garanties,
                    referenceProducteur,
                })
            })
    }

    updateCauseSinistreAndReferenceProducteurForAuto(
        causeSinistre: CauseSinistre,
        referenceProducteur?: string
    ): void {
        this.getDeclarationAuto$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    causeSinistre,
                    referenceProducteur,
                })
            })
    }

    updateGarage(garage: Garage): void {
        this.getDeclarationAuto$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    garage,
                })
            })
    }

    updateCirconstances(circonstance?: CirconstanceSinistre): void {
        this.getDeclaration$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    circonstance,
                })
            })
    }

    updateCirconstancesAuto(circonstance?: CirconstanceSinistre): void {
        this.getDeclarationHabitation$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    circonstance,
                })
            })
    }

    updateDetailsSinistreHabitation(
        detailsSinistre: DetailsSinistreHabitation
    ): void {
        this.getDeclarationHabitation$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    detailsSinistre,
                })
            })
    }

    updateDetailsSinistreAuto(detailsSinistre: DetailsSinistreAuto): void {
        this.getDeclarationAuto$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    detailsSinistre,
                })
            })
    }

    updateConducteur(conducteur: ConducteurSinistre): void {
        this.getDeclarationAuto$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    conducteur,
                })
            })
    }

    updateDommagesHabitation(dommages: DommagesHabitation): void {
        this.getDeclarationHabitation$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    dommages,
                })
            })
    }

    updateDommagesAuto(dommages: DommagesAuto): void {
        this.getDeclarationAuto$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    dommages,
                })
            })
    }

    updateSurvenance(survenance: Survenance | SurvenanceAuto): void {
        this.getDeclaration$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    survenance,
                })
            })
    }

    updateModalites(modalites: ModalitesHabitation | ModalitesAuto): void {
        this.getDeclaration$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    modalites,
                })
            })
    }

    updatePiecesJointes(piecesJointes: string[]): void {
        this.getDeclaration$()
            .pipe(take(1))
            .subscribe((currentValue) => {
                this.updateDeclaration({
                    ...currentValue,
                    piecesJointes,
                })
            })
    }

    upsertDeclaration(): Observable<
        JsonApiResponseInterface<DeclarationResult>
    > {
        return this.getDeclaration$()
            .pipe(take(1))
            .pipe(
                switchMap((declaration) => {
                    if (declaration.id) {
                        return this.declarationWrapper.modifier(declaration)
                    } else {
                        return this.creerDeclarationAndUpdateCurrentOneWithId(
                            declaration
                        )
                    }
                })
            )
    }

    abandonnerDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        return this.declarationWrapper.abandonnerDeclaration(id)
    }

    soumettreDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationSoumiseResult>> {
        return this.declarationWrapper.soumettreDeclaration(id).pipe(
            tap((declarationResult) => {
                const updatedDeclaration: Declaration = {
                    ...declarationResult.data.attributes.declaration,
                    numeroDossierSinistre:
                        declarationResult.data.attributes.numeroDossierSinistre,
                }
                this.updateDeclaration(updatedDeclaration)
            })
        )
    }

    hasDeclarationOrRedirectToSelectionRisque(): Subscription {
        return this.hasDeclaration$().subscribe((hasDeclaration) => {
            if (!hasDeclaration) {
                this.router.navigate(['../', RoutingPath.SELECTION_RISQUE])
            }
        })
    }

    findWithFilters(
        pageSize: PageSize,
        pageNumber: number,
        sort?: string
    ): Observable<Pageable<ListeDeclarationPageItem>> {
        return this.filtersService.getFilters$().pipe(
            switchMap((filters) => {
                const params = new ParamsBuilder()
                    .addParam('page[number]', pageNumber)
                    .addParam('page[size]', pageSize)
                    .addParamIfDefined('sort', sort)
                    .addParamFilters(filters)
                    .build()
                return this.declarationWrapper
                    .find(params)
                    .pipe(
                        map((json) =>
                            jsonAPIToPageable<
                                Declaration,
                                ListeDeclarationPageItem
                            >(json, mapToListeDeclarationPageItem)
                        )
                    )
            })
        )
    }

    isUpdateInProgress(): boolean {
        return this.form?.dirty ?? false
    }

    hasDeclaration$(): Observable<boolean> {
        return this.declarationSubjectObservable$.pipe(
            map((elem) => elem !== undefined)
        )
    }

    private creerDeclarationAndUpdateCurrentOneWithId(
        declaration: Declaration
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        return this.declarationWrapper.creer(declaration).pipe(
            tap((result) => {
                const idDeclaration =
                    result.data.relationships['_entity'].data.id
                this.updateDeclaration({
                    ...declaration,
                    id: idDeclaration,
                })
            })
        )
    }
}
