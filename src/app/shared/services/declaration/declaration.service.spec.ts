import { TestBed } from '@angular/core/testing'

import { DeclarationService } from './declaration.service'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { DeclarationWrapper } from '../../wrappers/declaration/declaration-wrapper.service'
import {
    declarationHabitationMock,
    DeclarationWrapperMock,
} from '../../wrappers/declaration/declaration-wrapper.service.mock'
import { PageSize } from '../../enums/page-size.enum'
import { FiltersService } from '../filter/filters.service'
import { ParamsBuilder } from '../../builders/params-builder'
import { FiltersServiceMock } from '../filter/filters.service.mock'
import { defaultFilters } from '../../mocks/filter.mock'
import { piecesJointesMock } from '../../mocks/pieces-jointes.mock'
import { circonstancesMock } from '../../mocks/circonstances.mock'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { OrigineDegats } from '../../../habitation/enums/origine-degats.enum'
import { ZonesMaisonInterieur } from '../../../habitation/enums/zones-maison-interieur.enum'
import { Survenance } from '../../models/survenance'
import { Garage } from '../../../auto/models/garage'
import { DeclarationHabitationForm } from '../../../habitation/models/forms/declaration-habitation.form'
import { DeclarationAutoForm } from '../../../auto/models/forms/declaration-auto.form'
import { DetailsSinistreHabitation } from '../../../habitation/models/details-sinistre-habitation'
import { DommagesHabitation } from '../../../habitation/models/dommages-habitation'
import { DeclarationAuto } from '../../../auto/models/declaration-auto'
import { DommagesAuto } from '../../../auto/models/dommages-auto'
import { ModalitesHabitation } from '../../../habitation/models/modalites-habitation'
import { ModalitesAuto } from '../../../auto/models/modalites-auto'
import { CauseSinistre } from '../../../auto/enums/cause-sinistre.enum'
import { DetailsSinistreAuto } from '../../../auto/models/details-sinistre-auto'
import { OuiNonInconnu } from '../../enums/oui-non-inconnu.enum'
import { ResultatTest } from '../../enums/resultat-test.enum'
import {
    garageManuelMock,
    prestataireIdMock,
} from '../../mocks/prestataires.mock'

describe('DeclarationService', () => {
    let service: DeclarationService
    let declarationWrapper: DeclarationWrapper
    const numeroRisque = '213546'
    const dateDeSurvenance = new Date()

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                {
                    provide: DeclarationWrapper,
                    useClass: DeclarationWrapperMock,
                },
                {
                    provide: FiltersService,
                    useClass: FiltersServiceMock,
                },
            ],
        })
        service = TestBed.inject(DeclarationService)
        declarationWrapper = TestBed.inject(DeclarationWrapper)
    })

    describe('initializeForm', () => {
        it('should initialize habitation form when DeclarationKind is HABITATION', () => {
            service.initializeForm(DeclarationKind.HABITATION)
            const form = Reflect.get(service, 'form')
            expect(form instanceof DeclarationHabitationForm).toBeTruthy()
        })

        it('should initialize auto form when DeclarationKind is AUTO', () => {
            service.initializeForm(DeclarationKind.AUTO)
            const form = Reflect.get(service, 'form')
            expect(form instanceof DeclarationAutoForm).toBeTruthy()
        })

        it('should throw error when DeclarationKind is unsupported', () => {
            const invalidKind = 'INVALID' as DeclarationKind
            expect(() => service.initializeForm(invalidKind)).toThrow(
                new Error(`Unsupported declaration kind: ${invalidKind}`)
            )
        })
    })

    describe('When declaration is HABITATION', () => {
        beforeEach(() => {
            service.updateDeclaration({
                _kind: DeclarationKind.HABITATION,
                survenance: { dateDeSurvenance },
                numeroRisque,
            })
        })

        it('init should initiate a delaration in service', (done) => {
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.numeroRisque).toEqual(numeroRisque)
                expect(declaration.survenance.dateDeSurvenance).toEqual(
                    dateDeSurvenance
                )
                done()
            })
        })

        it('updateCirconstances should update circonstance and complement de circonstance in service', (done) => {
            const expectedCirconstance = {
                circonstance: circonstancesMock()[0].key,
                complementDeCirconstance:
                    'Ceci est un complément de circonstance',
            }

            service.updateCirconstances(expectedCirconstance)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.circonstance).toEqual(expectedCirconstance)
                expect(
                    declaration.circonstance?.complementDeCirconstance
                ).toEqual('Ceci est un complément de circonstance')
                done()
            })
        })

        it('updateDetailsSinistre should update details sinistre in service', (done) => {
            const detailsHabitation: DetailsSinistreHabitation = {
                presentAuMomentDesFaits: true,
                origine: OrigineDegats.AUTRE,
            }

            service.updateDetailsSinistreHabitation(detailsHabitation)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.detailsSinistre).toEqual(detailsHabitation)
                done()
            })
        })

        it('updateDommagesHabitation should update dommages in service', (done) => {
            const dommagesHabitation: DommagesHabitation = {
                degatsPieces: [
                    ZonesMaisonInterieur.CHAMBRE,
                    ZonesMaisonInterieur.SALON,
                ],
            }

            service.updateDommagesHabitation(dommagesHabitation)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.dommages).toEqual(dommagesHabitation)
                done()
            })
        })

        it('updateSurvenance should update survenance in service', (done) => {
            const survenance: Survenance = {
                dateDeSurvenance,
                heureDeSurvenance: '15:05',
            }

            service.updateSurvenance(survenance)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.survenance).toEqual(survenance)
                done()
            })
        })

        it('updateGarantiesAndReferenceProducteurForHabitation should update garanties and referenceProducteur in service', (done) => {
            const expectedGaranties = [TypeGaranties.CNN]
            const referenceProducteur = 'mister'
            service.updateGarantiesAndReferenceProducteurForHabitation(
                expectedGaranties,
                referenceProducteur
            )
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.garanties).toEqual(expectedGaranties)
                expect(declaration.referenceProducteur).toEqual(
                    referenceProducteur
                )
                done()
            })
        })

        it('updateModalites should update modalites in service', (done) => {
            const modalites: ModalitesHabitation = {
                tva6Pourcent: true,
                preneurSoumisTva: false,
                compteBancaire: 'IBAN',
            }

            service.updateModalites(modalites)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.modalites).toEqual(modalites)
                done()
            })
        })

        it('updatePiecesJointes should update pieces jointes in service', (done) => {
            service.updatePiecesJointes(piecesJointesMock())
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.piecesJointes?.length).toEqual(2)
                expect(declaration.piecesJointes).toEqual(piecesJointesMock())
                done()
            })
        })

        it('upsertDeclaration should trigger creer when no id exists in declaration, and add id to active Declaration', (done) => {
            spyOn(declarationWrapper, 'creer').and.callThrough()
            service.upsertDeclaration().subscribe(() => {
                expect(declarationWrapper.creer).toHaveBeenCalled()
                service.getDeclaration$().subscribe((declaration) => {
                    expect(declaration.id).not.toBeUndefined()
                    done()
                })
            })
        })

        it('upsertDeclaration should trigger modifier when id exists in declaration', (done) => {
            service.updateDeclaration({
                _kind: DeclarationKind.HABITATION,
                id: 'coconut',
                survenance: { dateDeSurvenance },
                numeroRisque,
            })

            spyOn(declarationWrapper, 'modifier').and.callThrough()
            service.upsertDeclaration().subscribe(() => {
                expect(declarationWrapper.modifier).toHaveBeenCalled()
                done()
            })
        })
    })

    describe('When declaration is AUTO', () => {
        beforeEach(() => {
            service.updateDeclaration({
                _kind: DeclarationKind.AUTO,
                survenance: { dateDeSurvenance },
                numeroRisque,
                causeSinistre: CauseSinistre.VANDALISME,
            })
        })

        it('updateDetailsSinistre should update details sinistre in service', (done) => {
            const detailsAuto: DetailsSinistreAuto = {
                interventionPoliceOuPompiers: false,
                conducteurTestAlcoolemie: OuiNonInconnu.NON,
                conducteurTestStup: OuiNonInconnu.OUI,
                resultatStup: ResultatTest.POSITIF,
                tiersImplique: false,
            }

            service.updateDetailsSinistreAuto(detailsAuto)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.detailsSinistre).toEqual(detailsAuto)
                done()
            })
        })

        it('updateGarage should update garage in service when not manualy filled', (done) => {
            const garage: Garage = {
                idPrestataire: prestataireIdMock,
                isSaisiManuellement: false,
            }

            service.updateGarage(garage)
            service.getDeclaration$().subscribe((declaration) => {
                const declarationAuto = declaration as DeclarationAuto
                expect(declarationAuto.garage).toEqual(garage)
                done()
            })
        })

        it('updateGarage should update garage in service when manualy filled', (done) => {
            service.updateGarage(garageManuelMock())
            service.getDeclaration$().subscribe((declaration) => {
                const declarationAuto = declaration as DeclarationAuto
                expect(declarationAuto.garage).toEqual(garageManuelMock())
                done()
            })
        })

        it('updateDommagesAuto should update dommages in service', (done) => {
            const dommagesAuto: DommagesAuto = {
                etatDuVehicule: 'mauvais',
                degatsAnterieurs: OuiNonInconnu.OUI,
                zonesDommagesAuto: [],
            }

            service.updateDommagesAuto(dommagesAuto)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.dommages).toEqual(dommagesAuto)
                done()
            })
        })

        it('updateModalites should update modalites in service', (done) => {
            const modalites: ModalitesAuto = {
                tva6Pourcent: true,
                preneurSoumisTva: false,
                compteBancaire: 'IBAN',
            }

            service.updateModalites(modalites)
            service.getDeclaration$().subscribe((declaration) => {
                expect(declaration.modalites).toEqual(modalites)
                done()
            })
        })
    })

    it('find should trigger find with withoutKind filter to hide ABANDONNE', (done) => {
        const params = new ParamsBuilder()
            .addParam('page[number]', 1)
            .addParam('page[size]', PageSize.FIVE_ELEMENTS)
            .addParamIfDefined('sort', '-updatedAt')
            .addParamFilters(defaultFilters())
            .build()

        spyOn(declarationWrapper, 'find').and.callThrough()
        service
            .findWithFilters(PageSize.FIVE_ELEMENTS, 1, '-updatedAt')
            .subscribe(() => {
                expect(declarationWrapper.find).toHaveBeenCalledWith(params)
                done()
            })
    })

    it('abandonnerDeclaration should trigger abandonnerDeclaration', () => {
        spyOn(declarationWrapper, 'abandonnerDeclaration').and.callThrough()
        service.abandonnerDeclaration('coconut')

        expect(declarationWrapper.abandonnerDeclaration).toHaveBeenCalledWith(
            'coconut'
        )
    })

    it('soumettreDeclaration should trigger soumettreDeclaration', () => {
        spyOn(declarationWrapper, 'soumettreDeclaration').and.callThrough()
        service.soumettreDeclaration('idDeclaration')

        expect(declarationWrapper.soumettreDeclaration).toHaveBeenCalledWith(
            'idDeclaration'
        )
    })

    it('clear declaration when trigger', (done) => {
        service.initializeForm(DeclarationKind.HABITATION)
        service
            .getDeclarationHabitationForm()
            .controls.survenance.controls.heureDeSurvenance.setValue('12h13')

        service.updateDeclaration(declarationHabitationMock)

        service.clearDeclaration()

        service.hasDeclaration$().subscribe((hasDeclaration) => {
            expect(service.isUpdateInProgress()).toBeFalsy()
            expect(hasDeclaration).toBeFalsy()
            expect(
                service.getDeclarationHabitationForm().controls.survenance
                    .controls.heureDeSurvenance.value
            ).toBeNull()
            done()
        })
    })

    describe('isUpdateInProgress', () => {
        it('should return true if a value has changed in form', () => {
            service.initializeForm(DeclarationKind.HABITATION)
            service
                .getDeclarationHabitationForm()
                .controls.circonstance.controls.complementDeCirconstance.setValue(
                    'coconut'
                )
            service
                .getDeclarationHabitationForm()
                .controls.circonstance.controls.complementDeCirconstance.markAsDirty()

            expect(service.isUpdateInProgress()).toBeTruthy()
        })

        it('should return false if no value has changed in form', () => {
            expect(service.isUpdateInProgress()).toBeFalsy()
        })
    })
})
