import { Observable, of, Subscription } from 'rxjs'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { DeclarationResult } from '../../models/declaration-result'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { PageSize } from '../../enums/page-size.enum'
import { ListeDeclarationPageItem } from '../../models/liste-declaration-page-item'
import { Pageable } from '../../models/pageable.model'
import {
    declarationAutoMock,
    declarationHabitationMock,
    declarationWithId,
    DeclarationWrapperMock,
} from '../../wrappers/declaration/declaration-wrapper.service.mock'
import { DeclarationStateKind } from '../../enums/declaration-state-kind.enum'
import { DeclarationHabitationForm } from '../../../habitation/models/forms/declaration-habitation.form'
import { CirconstanceSinistre } from '../../models/circonstance-sinistre'
import { DeclarationSoumiseResult } from '../../models/declaration-soumise-result'
import { Survenance } from '../../models/survenance'
import { ModalitesHabitation } from '../../../habitation/models/modalites-habitation'
import { Garage } from '../../../auto/models/garage'
import { DeclarationAutoForm } from '../../../auto/models/forms/declaration-auto.form'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { DetailsSinistreHabitation } from '../../../habitation/models/details-sinistre-habitation'
import { Dommages } from '../../models/dommages'
import { DommagesAuto } from '../../../auto/models/dommages-auto'
import { DeclarationHabitation } from '../../../habitation/models/declaration-habitation'
import { Declaration } from '../../models/declaration'
import { DeclarationAuto } from '../../../auto/models/declaration-auto'
import { CauseSinistre } from '../../../auto/enums/cause-sinistre.enum'
import { DetailsSinistreAuto } from '../../../auto/models/details-sinistre-auto'

export const declarationPageItemMock: ListeDeclarationPageItem = {
    ...declarationHabitationMock,
    stateKind: DeclarationStateKind.CREE,
    updatedAt: new Date('2023-10-26T08:00:00Z'),
    id: '1265',
}

export class DeclarationServiceMock {
    getDeclarationAutoForm(): DeclarationAutoForm {
        return new DeclarationAutoForm()
    }

    getDeclarationHabitationForm(): DeclarationHabitationForm {
        return new DeclarationHabitationForm()
    }

    initializeForm(declarationKind: DeclarationKind): void {}

    getDeclaration$(): Observable<Declaration> {
        return of(declarationHabitationMock)
    }

    getDeclarationHabitation$(): Observable<DeclarationHabitation> {
        return of(declarationHabitationMock)
    }

    getDeclarationAuto$(): Observable<DeclarationAuto> {
        return of(declarationAutoMock)
    }

    clearDeclaration(): void {}

    updateDeclaration(declaration: Declaration) {}

    updateGarantiesAndReferenceProducteurForHabitation(
        garanties?: TypeGaranties[],
        referenceProducteur?: string
    ): void {}

    updateCirconstances(circonstance?: CirconstanceSinistre): void {}

    updateCirconstancesAuto(
        circonstance?: CirconstanceSinistre,
        causeSinistre?: CauseSinistre
    ): void {}

    updateDetailsSinistreHabitation(
        detailsSinistre: DetailsSinistreHabitation
    ): void {}

    updateDetailsSinistreAuto(detailsSinistre: DetailsSinistreAuto): void {}

    updateDommagesHabitation(dommages: Dommages): void {}

    updateDommagesAuto(dommages: DommagesAuto): void {}

    updateSurvenance(survenance: Survenance): void {}

    updateModalites(modalites: ModalitesHabitation): void {}

    updateGarage(garage: Garage): void {}

    updatePiecesJointes(piecesJointes: string[]): void {}

    upsertDeclaration(): Observable<
        JsonApiResponseInterface<DeclarationResult>
    > {
        return of(
            DeclarationWrapperMock.toDeclarationApiResult(
                declarationHabitationMock
            )
        )
    }

    abandonnerDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        return of(
            DeclarationWrapperMock.toDeclarationApiResult(
                declarationHabitationMock
            )
        )
    }

    soumettreDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationSoumiseResult>> {
        return of(
            DeclarationWrapperMock.toDeclarationSoumiseApiResult(
                declarationWithId
            )
        )
    }

    hasDeclarationOrRedirectToSelectionRisque(): Subscription {
        return new Subscription()
    }

    findWithFilters(
        pageSize: PageSize,
        pageNumber: number,
        sort?: string
    ): Observable<Pageable<ListeDeclarationPageItem>> {
        return of({
            content: [declarationPageItemMock],
            totalItems: 1,
            currentPage: 0,
            totalPages: 1,
            pageSize: PageSize.ONE_ELEMENT,
        })
    }

    isUpdateInProgress(): boolean {
        return true
    }

    hasDeclaration$(): Observable<boolean> {
        return of(true)
    }
}
