import { inject, Injectable } from '@angular/core'
import { map, Observable, take } from 'rxjs'
import { PrestataireWrapper } from '../../wrappers/prestataire/prestataire-wrapper.service'
import { PageSize } from '../../enums/page-size.enum'
import { Filter } from '../../builders/filter'
import { Pageable } from '../../models/pageable.model'
import { PrestatairePageItem } from '../../models/prestataire-page-item.model'
import { ParamsBuilder } from '../../builders/params-builder'
import { jsonAPIToPageable } from '../../utils/pagination-utils'
import { Prestataire } from '../../models/prestataire.model'
import {
    mapJsonApiResponseToPrestataire,
    mapToListePrestatairePageItem,
} from '../../utils/prestataire.utils'
import { FiltersBuilder } from '../../builders/filters-builder'
import { PrestataireKind } from '../../enums/prestataire-kind.enum'
import { FilterType } from '../../enums/filter-type.enum'
import { DistanceKm } from '../../enums/distance-km.enum'
import { TypePrestataire } from '../../enums/type-prestataire.enum'
import { FiltersService } from '../filter/filters.service'

@Injectable({
    providedIn: 'root',
})
export class PrestataireService {
    private readonly filtersService: FiltersService = inject(FiltersService)
    private readonly prestataireWrapper: PrestataireWrapper =
        inject(PrestataireWrapper)

    findOne(idPrestataire: string): Observable<Prestataire> {
        return this.prestataireWrapper
            .findOne(idPrestataire)
            .pipe(map(mapJsonApiResponseToPrestataire))
    }

    findManyPaginated(
        pageSize: PageSize,
        pageIndex: number,
        filters: Filter[] = [],
        sort?: string
    ): Observable<Pageable<PrestatairePageItem>> {
        const params = new ParamsBuilder()
            .addParam('page[number]', pageIndex)
            .addParam('page[size]', pageSize)
            .addParamIfDefined('sort', sort)
            .addParamFilters(filters)
            .build()

        return this.prestataireWrapper
            .find(params)
            .pipe(
                map((json) =>
                    jsonAPIToPageable<Prestataire, PrestatairePageItem>(
                        json,
                        mapToListePrestatairePageItem
                    )
                )
            )
    }

    findAllGaragesByNomPrenomOrNumeroPersonne(
        criteria: string
    ): Observable<Array<PrestatairePageItem>> {
        const filters: Filter[] =
            this.filtersService.buildSearchPrestatairesByNomPrenomOrNumeroPersonneFilters(
                criteria
            )

        return this.findManyPaginated(
            PageSize.TWO_HUNDRED_FIFTY_ELEMENTS,
            0,
            filters
        ).pipe(
            take(1),
            map((item) => item.content)
        )
    }

    findAllGaragesByNumeroPersonne(
        numeroPersonne: string,
        pageSize: PageSize = PageSize.TEN_ELEMENTS,
        pageIndex: number = 0,
        sort?: string
    ): Observable<Pageable<PrestatairePageItem>> {
        const filters = new FiltersBuilder()
            .addFilter(
                FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                TypePrestataire.GARAGE
            )
            .addFilter(FilterType.KIND, PrestataireKind.ACTIF)
            .addFilter(FilterType.PRESTATAIRE_NUMERO_PERSONNE, numeroPersonne)
            .build()

        return this.findManyPaginated(pageSize, pageIndex, filters, sort)
    }

    findAllGaragesByLocation(
        coordinates?: number[],
        distance?: DistanceKm | null,
        pageSize: PageSize = PageSize.TEN_ELEMENTS,
        pageIndex: number = 0,
        sort?: string
    ): Observable<Pageable<PrestatairePageItem>> {
        const filters = new FiltersBuilder()
            .addFilter(
                FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                TypePrestataire.GARAGE
            )
            .addFilter(FilterType.KIND, PrestataireKind.ACTIF)

        if (distance) {
            filters.addFilter(FilterType.MAX_DISTANCE_IN_KM, distance)
        }

        if (coordinates && coordinates.length > 0) {
            filters.addFilter(FilterType.COORDINATES, coordinates.join(','))
        }

        return this.findManyPaginated(
            pageSize,
            pageIndex,
            filters.build(),
            sort
        )
    }
}
