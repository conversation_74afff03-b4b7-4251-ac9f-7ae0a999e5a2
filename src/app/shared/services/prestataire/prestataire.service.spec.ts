import { TestBed } from '@angular/core/testing'

import { PrestataireService } from './prestataire.service'
import { PrestataireWrapper } from '../../wrappers/prestataire/prestataire-wrapper.service'
import { PrestataireWrapperMock } from '../../wrappers/prestataire/prestataire-wrapper.service.spec.mock'
import { ParamsBuilder } from '../../builders/params-builder'
import { PageSize } from '../../enums/page-size.enum'
import { FiltersBuilder } from '../../builders/filters-builder'
import { FilterType } from '../../enums/filter-type.enum'
import { of } from 'rxjs'
import { prestataireExpertHabitationMock } from '../../mocks/prestataires.mock'
import { mapJsonApiResponseToPrestataire } from '../../utils/prestataire.utils'
import { PrestataireKind } from '../../enums/prestataire-kind.enum'
import { DistanceKm } from '../../enums/distance-km.enum'
import { TypePrestataire } from '../../enums/type-prestataire.enum'
import { FiltersService } from '../filter/filters.service'
import { FiltersServiceMock } from '../filter/filters.service.mock'

describe('PrestataireService', () => {
    let service: PrestataireService
    let prestataireWrapper: PrestataireWrapper

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                {
                    provide: PrestataireWrapper,
                    useClass: PrestataireWrapperMock,
                },
                {
                    provide: FiltersService,
                    useClass: FiltersServiceMock,
                },
            ],
        })
        service = TestBed.inject(PrestataireService)
        prestataireWrapper = TestBed.inject(PrestataireWrapper)
    })

    describe('findOne', () => {
        it('should call prestataireWrapper.findOne with the correct id', (done) => {
            const idPrestataire = '123456'
            const mockResponse = PrestataireWrapperMock.toPrestataireApiResult(
                prestataireExpertHabitationMock()
            )

            spyOn(prestataireWrapper, 'findOne').and.returnValue(
                of(mockResponse)
            )

            service.findOne(idPrestataire).subscribe((result) => {
                expect(prestataireWrapper.findOne).toHaveBeenCalledWith(
                    idPrestataire
                )
                expect(result).toEqual(
                    mapJsonApiResponseToPrestataire(mockResponse)
                )
                done()
            })
        })
    })

    describe('findManyPaginated', () => {
        it('should trigger find with right params', (done) => {
            const expectedParams = new ParamsBuilder()
                .addParam('page[number]', 1)
                .addParam('page[size]', PageSize.FIVE_ELEMENTS)
                .addParamIfDefined('sort', '-numeroPersonne')
                .build()

            spyOn(prestataireWrapper, 'find').and.callThrough()

            service
                .findManyPaginated(
                    PageSize.FIVE_ELEMENTS,
                    1,
                    [],
                    '-numeroPersonne'
                )
                .subscribe(() => {
                    expect(prestataireWrapper.find).toHaveBeenCalledWith(
                        expectedParams
                    )
                    done()
                })
        })

        it('should trigger find with right params for findAllGaragesByNomPrenomOrNumeroPersonne', (done) => {
            const mockFilters = new FiltersBuilder()
                .addFilter(
                    FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                    TypePrestataire.GARAGE
                )
                .addFilter(FilterType.KIND, PrestataireKind.ACTIF)
                .addFilter(FilterType.PRESTATAIRE_NOM_PRENOM, 'nomPrenom')
                .build()

            const filtersService = TestBed.inject(FiltersService)
            spyOn(
                filtersService,
                'buildSearchPrestatairesByNomPrenomOrNumeroPersonneFilters'
            ).and.returnValue(mockFilters)

            const expectedParams = new ParamsBuilder()
                .addParam('page[number]', 0)
                .addParam('page[size]', PageSize.TWO_HUNDRED_FIFTY_ELEMENTS)
                .addParamFilters(mockFilters)
                .build()

            spyOn(prestataireWrapper, 'find').and.callThrough()

            service
                .findAllGaragesByNomPrenomOrNumeroPersonne('nomPrenom')
                .subscribe(() => {
                    expect(
                        filtersService.buildSearchPrestatairesByNomPrenomOrNumeroPersonneFilters
                    ).toHaveBeenCalledWith('nomPrenom')
                    expect(prestataireWrapper.find).toHaveBeenCalledWith(
                        expectedParams
                    )
                    done()
                })
        })

        it('should trigger find with right params for findAllGaragesByNumeroPersonne', (done) => {
            const filters = new FiltersBuilder()
                .addFilter(
                    FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                    TypePrestataire.GARAGE
                )
                .addFilter(FilterType.KIND, PrestataireKind.ACTIF)
                .addFilter(FilterType.PRESTATAIRE_NUMERO_PERSONNE, '12345')
                .build()

            const expectedParams = new ParamsBuilder()
                .addParam('page[number]', 0)
                .addParam('page[size]', PageSize.TEN_ELEMENTS)
                .addParamFilters(filters)
                .build()

            spyOn(prestataireWrapper, 'find').and.callThrough()

            service.findAllGaragesByNumeroPersonne('12345').subscribe(() => {
                expect(prestataireWrapper.find).toHaveBeenCalledWith(
                    expectedParams
                )
                done()
            })
        })

        it('should trigger find with expected filters', (done) => {
            const filters = new FiltersBuilder()
                .addFilter(
                    FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                    TypePrestataire.GARAGE
                )
                .build()
            const expectedParams = new ParamsBuilder()
                .addParam('page[number]', 1)
                .addParam('page[size]', PageSize.FIVE_ELEMENTS)
                .addParamFilters(filters)
                .build()

            spyOn(prestataireWrapper, 'find').and.callThrough()

            service
                .findManyPaginated(PageSize.FIVE_ELEMENTS, 1, filters)
                .subscribe(() => {
                    expect(prestataireWrapper.find).toHaveBeenCalledWith(
                        expectedParams
                    )
                    done()
                })
        })
    })

    describe('findAllGaragesByLocation', () => {
        const coordonates = [48.8566, 2.3522]

        it('should call prestataireWrapper.find with correct params for location search', (done) => {
            const filters = new FiltersBuilder()
                .addFilter(
                    FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                    TypePrestataire.GARAGE
                )
                .addFilter(FilterType.KIND, PrestataireKind.ACTIF)
                .addFilter(FilterType.MAX_DISTANCE_IN_KM, DistanceKm.FIVE_KM)
                .addFilter(FilterType.COORDINATES, coordonates.join(','))
                .build()

            const expectedParams = new ParamsBuilder()
                .addParam('page[number]', 0)
                .addParam('page[size]', PageSize.TEN_ELEMENTS)
                .addParamFilters(filters)
                .build()

            spyOn(prestataireWrapper, 'find').and.callThrough()

            service
                .findAllGaragesByLocation(
                    coordonates,
                    DistanceKm.FIVE_KM,
                    PageSize.TEN_ELEMENTS,
                    0
                )
                .subscribe(() => {
                    expect(prestataireWrapper.find).toHaveBeenCalledWith(
                        expectedParams
                    )
                    done()
                })
        })

        it('should call prestataireWrapper.find with default distance when not provided', (done) => {
            const filters = new FiltersBuilder()
                .addFilter(
                    FilterType.PRESTATAIRE_TYPE_PRESTATAIRE,
                    TypePrestataire.GARAGE
                )
                .addFilter(FilterType.KIND, PrestataireKind.ACTIF)
                .addFilter(FilterType.COORDINATES, coordonates.join(','))
                .build()

            const expectedParams = new ParamsBuilder()
                .addParam('page[number]', 1)
                .addParam('page[size]', PageSize.FIVE_ELEMENTS)
                .addParamFilters(filters)
                .build()

            spyOn(prestataireWrapper, 'find').and.callThrough()

            service
                .findAllGaragesByLocation(
                    coordonates,
                    null,
                    PageSize.FIVE_ELEMENTS,
                    1
                )
                .subscribe(() => {
                    expect(prestataireWrapper.find).toHaveBeenCalledWith(
                        expectedParams
                    )
                    done()
                })
        })
    })
})
