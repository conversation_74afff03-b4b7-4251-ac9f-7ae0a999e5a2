import { Observable, of } from 'rxjs'
import { Prestataire } from '../../models/prestataire.model'
import { garagePrestataireMock } from '../../mocks/prestataires.mock'
import { PageSize } from '../../enums/page-size.enum'
import { Pageable } from '../../models/pageable.model'
import { PrestatairePageItem } from '../../models/prestataire-page-item.model'

export class PrestataireServiceMock {
    findOne(idPrestataire: string): Observable<Prestataire> {
        return of(garagePrestataireMock())
    }

    findAllGaragesByNomPrenomOrNumeroPersonne(
        criteria: string
    ): Observable<Prestataire[]> {
        return of([
            garagePrestataireMock(),
            {
                ...garagePrestataireMock(),
                numeroPersonne: '654321',
                nomPrenom: 'Garage Premium',
            },
        ])
    }

    findAllGaragesByNumeroPersonne(
        numeroPersonne: string,
        pageSize: PageSize = PageSize.TEN_ELEMENTS,
        pageIndex: number = 0,
        sort?: string
    ): Observable<Pageable<PrestatairePageItem>> {
        return of()
    }
}
