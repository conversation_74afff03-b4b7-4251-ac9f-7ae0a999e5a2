import { Observable, of } from 'rxjs'
import {
    circonstancesDataSourceMock,
    circonstancesMock,
} from '../../mocks/circonstances.mock'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { CirconstanceParametre } from '../../models/circonstance-parametre'
import { DataSourceItem } from '../../data-sources/data-source.item'
import { featureFlagsParametresMock } from '../../wrappers/parametres-wrapper/parametres-wrapper.service.spec.mock'

export class ParametresServiceMock {
    getCirconstances$(): Observable<CirconstanceParametre[]> {
        return of(circonstancesMock())
    }

    getCirconstancesDataSource(): Observable<DataSourceItem[]> {
        return of(circonstancesDataSourceMock())
    }

    setCirconstances(garanties?: TypeGaranties[]): void {}

    getParametresFeatureFlags$() {
        return of(featureFlagsParametresMock())
    }
}
