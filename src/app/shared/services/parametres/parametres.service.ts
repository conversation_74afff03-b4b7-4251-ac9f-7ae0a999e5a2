import { Injectable } from '@angular/core'
import {
    BehaviorSubject,
    forkJoin,
    map,
    Observable,
    shareReplay,
    switchMap,
    take,
} from 'rxjs'
import { CirconstanceParametre } from '../../models/circonstance-parametre'
import { ParametresWrapper } from '../../wrappers/parametres-wrapper/parametres-wrapper.service'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { FiltersBuilder } from '../../builders/filters-builder'
import { FilterType } from '../../enums/filter-type.enum'
import { ParamsBuilder } from '../../builders/params-builder'
import { Circonstance } from '../../enums/circonstance.enum'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { TypeCirconstance } from '../../enums/type-circonstance.enum'
import { DataSourceItem } from '../../data-sources/data-source.item'
import { ClefParametres } from '../../enums/clef-parametres.enum'
import { Parametres } from '../../models/parametres'
import { FeatureFlagsParametres } from '../../models/feature-flags-parametres'

@Injectable({
    providedIn: 'root',
})
export class ParametresService {
    private readonly circonstancesSubject: BehaviorSubject<
        CirconstanceParametre[]
    > = new BehaviorSubject<CirconstanceParametre[]>([])
    private readonly circonstances$: Observable<CirconstanceParametre[]> =
        this.circonstancesSubject.asObservable()
    private readonly parametresFeatureFlags$

    constructor(
        private readonly parametresWrapper: ParametresWrapper,
        private readonly translationService: TranslationService
    ) {
        this.parametresFeatureFlags$ =
            this.findParametresByClef<FeatureFlagsParametres>(
                ClefParametres.FEATURE_FLAGS
            ).pipe(shareReplay(1))
    }

    findParametresByClef<T>(clef: ClefParametres): Observable<Parametres<T>> {
        return this.parametresWrapper.findOneByClef(clef)
    }

    getParametresFeatureFlags$() {
        return this.parametresFeatureFlags$
    }

    getCirconstances$(): Observable<CirconstanceParametre[]> {
        return this.circonstancesSubject.asObservable()
    }

    setCirconstances(
        typeGaranties?: TypeGaranties[],
        typeCirconstance: TypeCirconstance = TypeCirconstance.DAB
    ): void {
        this.circonstancesSubject.next([])
        const filters = new FiltersBuilder()
            .addFilter(FilterType.CIRCONSTANCES_PRINCIPALES, true)
            .addFilter(FilterType.CIRCONSTANCES_VALEURS, typeCirconstance)

        if (typeGaranties && typeGaranties.length > 0) {
            filters.addFilter(FilterType.CIRCONSTANCES_GARANTIES, typeGaranties)
        } else {
            filters.addFilter(FilterType.CIRCONSTANCES_KEY, Circonstance.AUTRE)
        }

        const params = new ParamsBuilder()
            .addParamFilters(filters.build())
            .build()

        this.parametresWrapper
            .findCirconstancesWithParams(params)
            .pipe(take(1))
            .subscribe((circonstances) =>
                this.circonstancesSubject.next(circonstances.circonstances)
            )
    }

    getCirconstancesDataSource(): Observable<DataSourceItem[]> {
        return this.circonstances$.pipe(
            switchMap((circonstances) => {
                const circonstancesLabel = circonstances.map((circonstance) =>
                    this.translationService.getTranslation$({
                        key: circonstance.key,
                        path: 'enums.circonstance-key.',
                    })
                )
                return forkJoin(circonstancesLabel).pipe(
                    map((labels) =>
                        circonstances.map(
                            (circonstance, index) =>
                                ({
                                    key: circonstance.key,
                                    displayValue: labels[index],
                                }) as DataSourceItem
                        )
                    )
                )
            })
        )
    }
}
