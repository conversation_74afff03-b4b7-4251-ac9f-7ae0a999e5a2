import { TestBed } from '@angular/core/testing'

import { ParametresService } from './parametres.service'
import { of } from 'rxjs'
import { ParametresWrapper } from '../../wrappers/parametres-wrapper/parametres-wrapper.service'
import {
    featureFlagsParametresMock,
    ParametresWrapperMock,
} from '../../wrappers/parametres-wrapper/parametres-wrapper.service.spec.mock'
import {
    circonstanceAutreDataSourceMock,
    circonstanceAutreMock,
    circonstancesDataSourceMock,
    circonstancesMock,
} from '../../mocks/circonstances.mock'
import { FiltersBuilder } from '../../builders/filters-builder'
import { FilterType } from '../../enums/filter-type.enum'
import { ParamsBuilder } from '../../builders/params-builder'
import { Circonstance } from '../../enums/circonstance.enum'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { Language } from '../../enums/language.enum'
import { TypeCirconstance } from '../../enums/type-circonstance.enum'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { ClefParametres } from '../../enums/clef-parametres.enum'
import Spy = jasmine.Spy

describe('ParametresService', () => {
    let service: ParametresService
    let parametresWrapper: ParametresWrapper
    let translationService: TranslationService

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                provideTranslation(),
                {
                    provide: ParametresWrapper,
                    useClass: ParametresWrapperMock,
                },
            ],
        })
        service = TestBed.inject(ParametresService)
        translationService = TestBed.inject(TranslationService)
        parametresWrapper = TestBed.inject(ParametresWrapper)
    })

    describe('When the findParametresByClef method is called', () => {
        let parametresWrapperFindParametresByClef: Spy

        it('should call findOneByClef with good parameters for feature flags', () => {
            parametresWrapperFindParametresByClef = spyOn(
                parametresWrapper,
                'findOneByClef'
            ).and.returnValue(of(featureFlagsParametresMock()))
            service.findParametresByClef(ClefParametres.FEATURE_FLAGS)
            expect(
                parametresWrapperFindParametresByClef
            ).toHaveBeenCalledOnceWith(ClefParametres.FEATURE_FLAGS)
        })
    })

    describe('Circonstances', () => {
        let findCirconstancesWithParamsSpy: Spy

        beforeEach(() => {
            translationService.changeLanguage(Language.FRENCH)
            findCirconstancesWithParamsSpy = spyOn(
                parametresWrapper,
                'findCirconstancesWithParams'
            ).and.returnValue(
                of({
                    circonstances: circonstancesMock(),
                })
            )
        })

        describe('When setCirconstances method is called', () => {
            it('should call findCirconstancesWithParams from wrapper with expected params and filters when no garanties', () => {
                const expectedFilters = new FiltersBuilder()
                    .addFilter(FilterType.CIRCONSTANCES_PRINCIPALES, true)
                    .addFilter(
                        FilterType.CIRCONSTANCES_VALEURS,
                        TypeCirconstance.DAB
                    )
                    .addFilter(FilterType.CIRCONSTANCES_KEY, Circonstance.AUTRE)
                const expectedParams = new ParamsBuilder()
                    .addParamFilters(expectedFilters.build())
                    .build()

                service.setCirconstances()

                expect(
                    findCirconstancesWithParamsSpy.calls.mostRecent().args[0]
                ).toEqual(expectedParams)
            })

            it('should call findCirconstancesWithParams from wrapper with expected params and filters when garanties', () => {
                const expectedFilters = new FiltersBuilder()
                    .addFilter(FilterType.CIRCONSTANCES_PRINCIPALES, true)
                    .addFilter(
                        FilterType.CIRCONSTANCES_VALEURS,
                        TypeCirconstance.DAB
                    )
                    .addFilter(FilterType.CIRCONSTANCES_GARANTIES, [
                        TypeGaranties.TGN,
                    ])
                const expectedParams = new ParamsBuilder()
                    .addParamFilters(expectedFilters.build())
                    .build()

                service.setCirconstances([TypeGaranties.TGN])

                expect(
                    findCirconstancesWithParamsSpy.calls.mostRecent().args[0]
                ).toEqual(expectedParams)
            })
        })

        describe('When getCirconstancesDataSource method is called', () => {
            it('should return expected circonstances when we pass garanties CNN', (done) => {
                const expectedValues = circonstancesDataSourceMock()
                service.setCirconstances([TypeGaranties.CNN])
                service.getCirconstancesDataSource().subscribe((values) => {
                    expect(values).toEqual(expectedValues)
                    done()
                })
            })

            it('should return default values when we pass no garanties', (done) => {
                findCirconstancesWithParamsSpy.and.returnValue(
                    of({
                        circonstances: circonstanceAutreMock(),
                    })
                )
                const expectedValues = circonstanceAutreDataSourceMock()
                service.setCirconstances()
                service.getCirconstancesDataSource().subscribe((values) => {
                    expect(values).toEqual(expectedValues)
                    done()
                })
            })
        })
    })
})
