import { inject, Injectable } from '@angular/core'
import { Observable, of } from 'rxjs'
import { AddressGeocodingApiWrapper } from '../../wrappers/address-geocoding-api/address-geocoding-api-wrapper.service'
import { AdressGeocodingLocation } from '../../models/adress-geocoding-location'
import { HttpParams } from '@angular/common/http'

@Injectable({
    providedIn: 'root',
})
export class AddressGeocodingApiService {
    private readonly addressGeocodingApiWrapper: AddressGeocodingApiWrapper =
        inject(AddressGeocodingApiWrapper)

    searchPlaces(
        localiteOrCodePostal: string,
        pays: string = 'BE'
    ): Observable<AdressGeocodingLocation[]> {
        if (!localiteOrCodePostal || localiteOrCodePostal.trim().length === 0) {
            return of([])
        }
        const params = new HttpParams()
            .set('filter[localiteOrCodePostal]', localiteOrCodePostal)
            .set('filter[pays]', pays)

        return this.addressGeocodingApiWrapper.geocoder(params)
    }
}
