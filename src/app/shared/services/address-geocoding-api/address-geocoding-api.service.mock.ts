import { Observable, of } from 'rxjs'
import { addressGeocodingResultMock } from '../../mocks/address-geocoding-api.mock'
import { AdressGeocodingLocation } from '../../models/adress-geocoding-location'

export class AddressGeocodingApiServiceMock {
    searchPlaces(
        localiteOrCodePostal: string,
        pays: string = 'BE'
    ): Observable<AdressGeocodingLocation[]> {
        return of([
            {
                ...addressGeocodingResultMock()[0],
                address_components: [
                    {
                        long_name: 'Localité',
                        short_name: 'Localité',
                        types: ['locality', 'political'],
                    },
                    {
                        long_name: '1234',
                        short_name: '1234',
                        types: ['postal_code'],
                    },
                ],
            },
        ])
    }
}
