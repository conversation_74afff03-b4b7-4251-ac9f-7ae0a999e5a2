import { TestBed } from '@angular/core/testing'
import { AddressGeocodingApiService } from './address-geocoding-api.service'
import { of } from 'rxjs'
import { AddressGeocodingApiWrapper } from '../../wrappers/address-geocoding-api/address-geocoding-api-wrapper.service'
import { AddressGeocodingApiWrapperMock } from '../../wrappers/address-geocoding-api/address-geocoding-api-wrapper.service.spec.mock'
import { addressGeocodingResultMock } from '../../mocks/address-geocoding-api.mock'
import Spy = jasmine.Spy

describe('AddressGeocodingApiService', () => {
    let service: AddressGeocodingApiService
    let addressGeocodingApiWrapper: AddressGeocodingApiWrapper
    let searchSpy: Spy

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                {
                    provide: AddressGeocodingApiWrapper,
                    useClass: AddressGeocodingApiWrapperMock,
                },
            ],
        })

        service = TestBed.inject(AddressGeocodingApiService)
        addressGeocodingApiWrapper = TestBed.inject(AddressGeocodingApiWrapper)
    })

    it('should return empty array for empty query', (done) => {
        searchSpy = spyOn(
            addressGeocodingApiWrapper,
            'geocoder'
        ).and.returnValue(of([]))

        service.searchPlaces('').subscribe((results) => {
            expect(results).toEqual([])
            expect(searchSpy).not.toHaveBeenCalled()
            done()
        })
    })

    it('should call wrapper search method with correct parameters', (done) => {
        const mockResult = addressGeocodingResultMock()
        searchSpy = spyOn(
            addressGeocodingApiWrapper,
            'geocoder'
        ).and.returnValue(of(mockResult))

        const query = 'Bruxelles'
        const region = 'BE'

        service.searchPlaces(query, region).subscribe((results) => {
            expect(results).toEqual(mockResult)
            done()
        })
    })
})
