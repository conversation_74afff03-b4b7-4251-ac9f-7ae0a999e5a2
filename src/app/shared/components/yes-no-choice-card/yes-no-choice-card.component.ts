import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DoChe<PERSON>,
    forwardRef,
    Injector,
    Input,
} from '@angular/core'
import {
    ControlValueAccessor,
    FormControl,
    NG_VALUE_ACCESSOR,
    NgControl,
} from '@angular/forms'
import { OuiNonInconnu } from '../../enums/oui-non-inconnu.enum'
import { ResultatTest } from '../../enums/resultat-test.enum'

import { I18nPipe } from '@foyer/ng-i18n'

export type YesNoChoiceValue = boolean | null | OuiNonInconnu | ResultatTest

@Component({
    selector: 'yes-no-choice-card',
    templateUrl: './yes-no-choice-card.component.html',
    styleUrls: ['./yes-no-choice-card.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => YesNoChoiceCardComponent),
            multi: true,
        },
    ],
    standalone: true,
    imports: [I18nPipe],
})
export class YesNoChoiceCardComponent
    implements ControlValueAccessor, AfterViewInit, DoCheck
{
    @Input()
    withUnknown = false
    @Input()
    useEnum: typeof OuiNonInconnu | typeof ResultatTest | null = null

    id: string
    value?: YesNoChoiceValue
    formControl?: FormControl<unknown>

    protected readonly ResultatTest = ResultatTest
    protected readonly OuiNonInconnu = OuiNonInconnu

    constructor(
        private readonly changeDetectorRef: ChangeDetectorRef,
        private readonly injector: Injector
    ) {
        this.id = Math.random().toString().substring(2)
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onChange = (value: YesNoChoiceValue) => {}

    onTouched = () => {}

    registerOnChange(onChange: (value: YesNoChoiceValue) => void): void {
        this.onChange = onChange
    }

    registerOnTouched(onTouched: () => void): void {
        this.onTouched = onTouched
    }

    writeValue(obj?: YesNoChoiceValue): void {
        this.value = obj
        this.changeDetectorRef.markForCheck()
    }

    ngDoCheck(): void {
        this.changeDetectorRef.markForCheck()
    }

    ngAfterViewInit(): void {
        this.formControl = this.injector.get(NgControl)
            .control as FormControl<YesNoChoiceValue>
        this.changeDetectorRef.markForCheck()
    }

    setValue($event: boolean | null) {
        this.value = this.convertValue($event)
        this.onTouched()
        this.onChange(this.value)
        this.changeDetectorRef.markForCheck()
    }

    private convertValue(value: boolean | null): YesNoChoiceValue {
        if (this.useEnum === OuiNonInconnu) {
            switch (value) {
                case true:
                    return OuiNonInconnu.OUI
                case false:
                    return OuiNonInconnu.NON
                default:
                    return OuiNonInconnu.INCONNU
            }
        } else if (this.useEnum === ResultatTest) {
            switch (value) {
                case true:
                    return ResultatTest.POSITIF
                case false:
                    return ResultatTest.NEGATIF
                default:
                    return null
            }
        } else {
            return value
        }
    }
}
