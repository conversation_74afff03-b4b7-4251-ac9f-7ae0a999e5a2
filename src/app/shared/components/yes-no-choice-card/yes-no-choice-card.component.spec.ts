import { ComponentFixture, TestBed } from '@angular/core/testing'

import { YesNoChoiceCardComponent } from './yes-no-choice-card.component'
import { By } from '@angular/platform-browser'
import { FormControl, NgControl, Validators } from '@angular/forms'
import { OuiNonInconnu } from '../../enums/oui-non-inconnu.enum'
import { ResultatTest } from '../../enums/resultat-test.enum'

describe('YesNoChoiceCardComponent', () => {
    let component: YesNoChoiceCardComponent
    let fixture: ComponentFixture<YesNoChoiceCardComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [YesNoChoiceCardComponent],
            providers: [NgControl],
        }).compileComponents()

        fixture = TestBed.createComponent(YesNoChoiceCardComponent)
        component = fixture.componentInstance
    })

    it('should set value to true when click yes', () => {
        fixture.detectChanges()
        const yesButton = fixture.debugElement.query(By.css('.yes'))

        yesButton.nativeElement.click()
        expect(component.value).toBeTruthy()
    })

    it('should set value to false when click no', () => {
        fixture.detectChanges()
        const yesButton = fixture.debugElement.query(By.css('.no'))

        yesButton.nativeElement.click()
        expect(component.value).toBeFalsy()
    })

    it('should set value to undefined when not clicking on a button', () => {
        fixture.detectChanges()
        expect(component.value).toBeUndefined()
    })

    it('should show red style when touched or dirty or invalid', () => {
        const formControl = new FormControl<boolean | null>(null, {
            nonNullable: true,
            validators: [Validators.required],
        })
        component.formControl = formControl
        fixture.detectChanges()
        component.formControl = formControl
        component.formControl.markAsTouched()
        fixture.detectChanges()
        const dangerElement = fixture.debugElement.query(By.css('.is-danger'))

        expect(dangerElement).not.toBeUndefined()
    })

    it('should not show red style when not touched nor dirty nor invalid', () => {
        component.formControl = new FormControl<boolean | null>(null, {
            nonNullable: true,
            validators: [Validators.required],
        })
        fixture.detectChanges()
        const dangerElement = fixture.debugElement.query(By.css('.is-danger'))

        expect(dangerElement).toBeNull()
    })

    describe('setValue', () => {
        it('should convert value to OuiNonInconnu when useEnum is OuiNonInconnu', () => {
            component.useEnum = OuiNonInconnu
            component.setValue(true)

            expect(component.value).toBe(OuiNonInconnu.OUI)
            component.setValue(false)

            expect(component.value).toBe(OuiNonInconnu.NON)
            component.setValue(null)

            expect(component.value).toBe(OuiNonInconnu.INCONNU)
        })

        it('should convert value to ResultatTest when useEnum is ResultatTest', () => {
            component.useEnum = ResultatTest

            component.setValue(true)
            expect(component.value).toBe(ResultatTest.POSITIF)

            component.setValue(false)
            expect(component.value).toBe(ResultatTest.NEGATIF)

            component.setValue(null)
            expect(component.value).toBe(null)
        })

        it('should keep original value when useEnum is null', () => {
            component.useEnum = null

            component.setValue(true)
            expect(component.value).toBe(true)

            component.setValue(false)
            expect(component.value).toBe(false)

            component.setValue(null)
            expect(component.value).toBe(null)
        })
    })
})
