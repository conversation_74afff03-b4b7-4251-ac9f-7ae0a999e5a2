<div class="ButtonGroup is-medium is-full-width" role="radiogroup" aria-label="choixOuiNonInconnu">
    <button
        class="ChoiceCard is-poll yes is-light"
        type="button"
        role="radio"
        [attr.aria-checked]="value === true || value === OuiNonInconnu.OUI || value === ResultatTest.POSITIF"
        (click)="setValue(true)">
        @if (useEnum === ResultatTest) {
            {{ 'enums.resultat-test.POSITIF' | translate }}
        } @else {
            {{ 'common.oui' | translate }}
        }
    </button>

    <button
        class="ChoiceCard is-poll no is-light"
        type="button"
        role="radio"
        [attr.aria-checked]="value === false || value === OuiNonInconnu.NON || value === ResultatTest.NEGATIF"
        (click)="setValue(false)">
        @if (useEnum === ResultatTest) {
            {{ 'enums.resultat-test.NEGATIF' | translate }}
        } @else {
            {{ 'common.non' | translate }}
        }
    </button>

    @if (withUnknown) {
        <button
            class="ChoiceCard is-poll unknown is-light"
            type="button"
            role="radio"
            [attr.aria-checked]="value === OuiNonInconnu.INCONNU"
            (click)="setValue(null)">
            {{ 'enums.oui-non-inconnu.INCONNU' | translate }}
        </button>
    }
</div>
