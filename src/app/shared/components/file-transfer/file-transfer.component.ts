import { Component, inject, Input } from '@angular/core'
import { FormControl, ReactiveFormsModule } from '@angular/forms'
import { InputFileComponent } from '@foyer/ng-input-file'
import { WizardAction } from '../../enums/wizard-action.enum'
import { WizardService } from '../../services/wizard/wizard.service'

@Component({
    selector: 'file-transfer',
    templateUrl: './file-transfer.component.html',
    styleUrls: ['./file-transfer.component.scss'],
    standalone: true,
    imports: [InputFileComponent, ReactiveFormsModule],
})
export class FileTransferComponent {
    private readonly wizardService: WizardService = inject(WizardService)

    @Input({ required: true })
    piecesJointesFormControl!: FormControl<string[]>

    readonly TRANSLATION_PREFIX: string = 'common.file-transfer.'
    readonly ACCEPTED_FILE_TYPES: string[] = [
        'image/png',
        'image/jpeg',
        'image/jpg',
        'image/pcx',
        'image/jp2',
        'image/gif',
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'text/html',
    ]
    readonly MAX_FILE_SIZE = 54525952 // 52MB
    readonly MAX_FILE_NUMBER = 10

    private filesUploadingCounter = 1 // Car à l'init du component isUploading = false donc on retire 1

    isFileUploading(isUploading: Event | boolean): void {
        if (isUploading) {
            this.filesUploadingCounter++
            this.wizardService.updateState(WizardAction.SHOW_LOADING)
        } else {
            this.filesUploadingCounter--
        }

        if (this.filesUploadingCounter === 0) {
            this.wizardService.updateState(WizardAction.HIDE_LOADING)
        }
    }
}
