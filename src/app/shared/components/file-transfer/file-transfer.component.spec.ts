import { ComponentFixture, TestBed } from '@angular/core/testing'
import { FileTransferComponent } from './file-transfer.component'
import { provideHttpClient } from '@angular/common/http'
import { inputFileProvider } from '../../../core/providers/input-file.provider'
import { FormControl } from '@angular/forms'
import { By } from '@angular/platform-browser'
import { InputFileComponent } from '@foyer/ng-input-file'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { WizardService } from '../../services/wizard/wizard.service'
import { WizardServiceMock } from '../../services/wizard/wizard-service.mock'
import { WizardAction } from '../../enums/wizard-action.enum'

describe('FileTransferComponent', () => {
    let component: FileTransferComponent
    let fixture: ComponentFixture<FileTransferComponent>
    let inputFileComponent: InputFileComponent
    let wizardService: WizardService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [FileTransferComponent],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                inputFileProvider,
                { provide: WizardService, useClass: WizardServiceMock },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(FileTransferComponent)
        component = fixture.componentInstance
        component.piecesJointesFormControl = new FormControl<string[]>([], {
            nonNullable: true,
        })
        wizardService = TestBed.inject(WizardService)
        fixture.detectChanges()

        inputFileComponent = fixture.debugElement.query(
            By.directive(InputFileComponent)
        ).componentInstance
    })

    describe('Form control integration', () => {
        const mockFiles = ['file1.pdf', 'file2.jpg']

        it('should handle file changes through ControlValueAccessor', () => {
            inputFileComponent.writeValue(mockFiles)
            inputFileComponent.onChange?.(mockFiles)
            fixture.detectChanges()

            expect(component.piecesJointesFormControl.value).toEqual(mockFiles)
        })

        it('should handle file changes through FormControl', () => {
            component.piecesJointesFormControl.setValue(mockFiles)
            fixture.detectChanges()

            expect(component.piecesJointesFormControl.value).toEqual(mockFiles)
        })
    })

    it('should have correct input file configuration', () => {
        const inputElement = fixture.debugElement.query(
            By.directive(InputFileComponent)
        )

        expect(inputElement.componentInstance.maxFileSize).toBe(
            component.MAX_FILE_SIZE
        )
        expect(inputElement.componentInstance.maxFilesNb).toBe(
            component.MAX_FILE_NUMBER
        )
        expect(inputElement.componentInstance.acceptedTypes).toEqual(
            component.ACCEPTED_FILE_TYPES
        )
    })

    describe('Loading state management', () => {
        beforeEach(() => {
            spyOn(wizardService, 'updateState')
        })

        it('should show loading on file selection', () => {
            component.isFileUploading(true)

            expect(wizardService.updateState).toHaveBeenCalledWith(
                WizardAction.SHOW_LOADING
            )
        })

        it('should hide loading after upload', () => {
            component.isFileUploading(true)
            component.isFileUploading(false)

            expect(wizardService.updateState).toHaveBeenCalledWith(
                WizardAction.HIDE_LOADING
            )
        })

        it('should not doing anything when error', () => {
            component.isFileUploading(false)

            expect(wizardService.updateState).not.toHaveBeenCalled()
        })

        it('should handle loading on multiple files correctly', () => {
            component.isFileUploading(true)
            component.isFileUploading(true)
            component.isFileUploading(true)
            component.isFileUploading(false)
            component.isFileUploading(false)

            expect(wizardService.updateState).toHaveBeenCalledWith(
                WizardAction.SHOW_LOADING
            )
            expect(wizardService.updateState).not.toHaveBeenCalledWith(
                WizardAction.HIDE_LOADING
            )
        })
    })
})
