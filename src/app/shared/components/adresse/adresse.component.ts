import {
    ChangeDetectionStrategy,
    Component,
    computed,
    input,
} from '@angular/core'
import { CountrySelectComponent } from '../country-select/country-select.component'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { I18nPipe } from '@foyer/ng-i18n'
import { MessageBannerComponent } from '../message-banner/message-banner.component'
import { AdresseForm } from '../../models/forms/adresse.form'
import { AdresseFormStructure } from '../../models/forms/adresse-form-structure'

@Component({
    selector: 'adresse',
    standalone: true,
    imports: [
        CountrySelectComponent,
        FormsModule,
        ReactiveFormsModule,
        I18nPipe,
        MessageBannerComponent,
    ],
    templateUrl: './adresse.component.html',
    styleUrl: './adresse.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class AdresseComponent {
    form = input.required<AdresseForm>()

    readonly TRANSLATION_PREFIX: string = 'common.adresse.'

    formGroup = computed(
        () => this.form() as unknown as FormGroup<AdresseFormStructure>
    )
}
