@if (form(); as form) {
    <ng-container [formGroup]="formGroup()">
        <div class="GridFlex-row Form-row u-has-margin-top-16">
            <div class="GridFlex-col-4">
                <div class="Form-field is-medium" [class.is-danger]="form.controls.pays.invalid && (form.controls.pays.touched || form.controls.pays.dirty)">
                    <label class="Form-field-label">
                        {{ TRANSLATION_PREFIX + 'pays' | translate }}
                    </label>
                    <div class="Form-field-input">
                        <country-select [formControl]="form.controls.pays"></country-select>
                    </div>
                </div>
            </div>

            <div class="GridFlex-col-2">
                <div class="Form-field is-medium">
                    <label class="Form-field-label">
                        {{ TRANSLATION_PREFIX + 'code-postal' | translate }}
                    </label>
                    <div class="Form-field-input">
                        <input class="Input" type="text" formControlName="codePostal" />
                    </div>
                </div>
            </div>

            <div class="GridFlex-col-6">
                <div
                    class="Form-field is-medium"
                    [class.is-danger]="form.controls.localite.invalid && (form.controls.localite.touched || form.controls.localite.dirty)">
                    <label class="Form-field-label">
                        {{ TRANSLATION_PREFIX + 'localite' | translate }}
                    </label>
                    <div class="Form-field-input">
                        <input class="Input" type="text" formControlName="localite" />
                    </div>
                </div>
            </div>

            <div class="GridFlex-col-12">
                @if (form.controls.pays.invalid && (form.controls.pays.touched || form.controls.pays.dirty)) {
                    <message-banner>
                        @if (form.controls.pays.errors?.['required']) {
                            <span>
                                {{ 'common.is-required' | translate: { field: TRANSLATION_PREFIX + 'pays' | translate } }}
                            </span>
                        }
                    </message-banner>
                }

                @if (form.controls.localite.invalid && (form.controls.localite.touched || form.controls.localite.dirty)) {
                    <message-banner>
                        @if (form.controls.localite.errors?.['required']) {
                            <span>
                                {{ 'common.is-required' | translate: { field: TRANSLATION_PREFIX + 'localite' | translate } }}
                            </span>
                        }
                    </message-banner>
                }
            </div>
        </div>

        <div class="GridFlex-row Form-row">
            <div class="GridFlex-col-2">
                <div class="Form-field is-medium">
                    <label class="Form-field-label">
                        {{ TRANSLATION_PREFIX + 'numero-rue' | translate }}
                    </label>
                    <div class="Form-field-input">
                        <input class="Input" type="text" formControlName="numeroRue" />
                    </div>
                </div>
            </div>

            <div class="GridFlex-col-10">
                <div class="Form-field is-medium">
                    <label class="Form-field-label">
                        {{ TRANSLATION_PREFIX + 'nom-rue' | translate }}
                    </label>
                    <div class="Form-field-input">
                        <input class="Input" type="text" formControlName="nomRue" />
                    </div>
                </div>
            </div>
        </div>
    </ng-container>
}
