import { ComponentFixture, TestBed } from '@angular/core/testing'

import { AdresseComponent } from './adresse.component'
import { AdresseService } from '../../services/adresse/adresse.service'
import { AdresseServiceMock } from '../../services/adresse/adresse.service.spec.mock'
import { AdresseForm } from '../../models/forms/adresse.form'
import { Validators } from '@angular/forms'
import { adresseBEMock } from '../../mocks/refAdresseMock'

describe('AdresseComponent', () => {
    let component: AdresseComponent
    let fixture: ComponentFixture<AdresseComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [AdresseComponent],
            providers: [
                { provide: AdresseService, useClass: AdresseServiceMock },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(AdresseComponent)
        component = fixture.componentInstance
    })

    it('should have required validators on pays and localite when isRequired is true in form', () => {
        fixture.componentRef.setInput('form', new AdresseForm(undefined, true))
        fixture.detectChanges()

        const paysControl = component.form().controls.pays
        const localiteControl = component.form().controls.localite

        expect(paysControl.hasValidator(Validators.required)).toBeTruthy()
        expect(localiteControl.hasValidator(Validators.required)).toBeTruthy()
    })

    it('should have no required fields when isRequired is false in form', () => {
        fixture.componentRef.setInput('form', new AdresseForm(undefined, false))
        fixture.detectChanges()

        const paysControl = component.form().controls.pays
        const localiteControl = component.form().controls.localite

        expect(paysControl.hasValidator(Validators.required)).toBeFalsy()
        expect(localiteControl.hasValidator(Validators.required)).toBeFalsy()
    })

    it('should have default fields when provided in form', () => {
        fixture.componentRef.setInput(
            'form',
            new AdresseForm(adresseBEMock(), false)
        )
        fixture.detectChanges()

        expect(component.form().getValue()).toEqual(adresseBEMock())
    })
})
