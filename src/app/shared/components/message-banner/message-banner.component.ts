import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { NgClass } from '@angular/common'

@Component({
    selector: 'message-banner',
    templateUrl: './message-banner.component.html',
    styleUrls: ['./message-banner.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgClass],
})
export class MessageBannerComponent {
    @Input()
    fdsColorClass = 'is-danger'
    @Input()
    fdsIconClass?: string
}
