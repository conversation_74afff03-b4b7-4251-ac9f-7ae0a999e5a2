import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    forwardRef,
    inject,
    Input,
} from '@angular/core'
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms'
import { TypeGaranties } from '../../enums/type-garanties.enum'

import { GarantiePanelComponent } from '../garantie-panel/garantie-panel.component'

import { I18nPipe } from '@foyer/ng-i18n'
import { trackByGarantieCode } from '../../utils/garantie.utils'
import { Garantie } from '../../models/garantie'
import { SortAlphabeticallyGarantiePipe } from '../../pipes/personne/sort-alphabetically-garantie.pipe'
import {
    ARAG_BELGIUM_GARANTIES,
    EUROP_ASSISTANCE_GARANTIES,
    FOYER_ARAG_SA_GARANTIES,
} from '../../constants/garanties.constants'

@Component({
    selector: 'garanties-concernees',
    templateUrl: './garanties-concernees.component.html',
    styleUrls: ['./garanties-concernees.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            useExisting: forwardRef(() => GarantiesConcerneesComponent),
            multi: true,
        },
    ],
    standalone: true,
    imports: [GarantiePanelComponent, I18nPipe, SortAlphabeticallyGarantiePipe],
})
export class GarantiesConcerneesComponent implements ControlValueAccessor {
    private readonly changeDetectorRef = inject(ChangeDetectorRef)

    @Input()
    garanties!: Garantie[]

    typeGaranties?: TypeGaranties[]

    readonly TRANSLATION_PREFIX = 'common.garanties-concernees.'
    readonly trackByGarantieCode = trackByGarantieCode

    onChange: (typeGaranties?: TypeGaranties[]) => void = () => {}
    onTouched: () => void = () => {}

    writeValue(typeGaranties: TypeGaranties[]): void {
        this.typeGaranties = typeGaranties || undefined
        this.changeDetectorRef.markForCheck()
    }

    registerOnChange(
        onChange: (typeGaranties?: TypeGaranties[]) => void
    ): void {
        this.onChange = onChange
    }

    registerOnTouched(onTouched: () => void): void {
        this.onTouched = onTouched
    }

    isGarantieSelectable(garantie: Garantie): boolean {
        return [
            ARAG_BELGIUM_GARANTIES,
            FOYER_ARAG_SA_GARANTIES,
            EUROP_ASSISTANCE_GARANTIES,
        ].every((array) => !array.includes(garantie.code))
    }

    selectedGarantiesContainsGarantie(garantie: Garantie): boolean {
        if (this.typeGaranties) {
            return this.typeGaranties.includes(garantie.code)
        }
        return false
    }

    toggleGarantie(garantie: Garantie): void {
        if (this.typeGaranties === undefined) {
            this.typeGaranties = []
        }

        const index = this.typeGaranties.indexOf(garantie.code)
        if (index > -1) {
            this.typeGaranties.splice(index, 1)
        } else {
            this.typeGaranties.push(garantie.code)
        }

        if (this.typeGaranties.length === 0) {
            this.typeGaranties = undefined
        }
        this.updateGaranties()
    }

    toggleAutreGarantie(): void {
        if (this.typeGaranties?.length === 0) {
            this.typeGaranties = undefined
        } else {
            this.typeGaranties = []
        }
        this.updateGaranties()
    }

    private updateGaranties(): void {
        this.onChange(this.typeGaranties)
        this.onTouched()
        this.changeDetectorRef.markForCheck()
    }
}
