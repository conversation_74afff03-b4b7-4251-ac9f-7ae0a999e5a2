import { ComponentFixture, TestBed } from '@angular/core/testing'

import { GarantiesConcerneesComponent } from './garanties-concernees.component'
import { By } from '@angular/platform-browser'
import { risquesMock } from '../../mocks/risques.mock'
import { garantiesMock } from '../../mocks/garanties.mock'

describe('GarantiesConcerneesComponent', () => {
    let component: GarantiesConcerneesComponent
    let fixture: ComponentFixture<GarantiesConcerneesComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [GarantiesConcerneesComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(GarantiesConcerneesComponent)
        component = fixture.componentInstance
        component.garanties = garantiesMock()
        fixture.detectChanges()
    })

    describe('garanties', () => {
        it('should show the list of available garanties', () => {
            const garantiesElementList = fixture.debugElement.queryAll(
                By.css('garantie-panel')
            )
            expect(garantiesElementList.length > 0).toBeTruthy()
        })

        it('should show the "autre garantie" datatile in order to be able to define no matching garantie', () => {
            const autreGarantieDataTile = fixture.debugElement.query(
                By.css('.autreGarantieDataTile')
            )
            expect(autreGarantieDataTile).not.toBeUndefined()
        })

        it('should fill the form with the code when choosing garantie ', () => {
            component.toggleGarantie(risquesMock()[0].garanties[2])
            expect(component.typeGaranties).toContain(garantiesMock()[2].code)
        })

        it('should select multiple garanties', () => {
            component.toggleGarantie(garantiesMock()[0])
            component.toggleGarantie(garantiesMock()[1])
            component.toggleGarantie(garantiesMock()[2])

            expect(component.typeGaranties?.length).toBe(3)
            expect(component.typeGaranties).toEqual([
                garantiesMock()[0].code,
                garantiesMock()[1].code,
                garantiesMock()[2].code,
            ])
        })

        it('should remove the garantie from the form when it is chosen again', () => {
            const garantie = garantiesMock()[0]
            component.toggleGarantie(garantie)
            component.toggleGarantie(garantie)
            expect(component.typeGaranties).toEqual(undefined)
        })

        it('should toggleAutreGarantie when choosing autre garantie', () => {
            component.toggleGarantie(risquesMock()[0].garanties[2])
            component.toggleAutreGarantie()
            expect(component.typeGaranties).toEqual([])
            component.toggleAutreGarantie()
            expect(component.typeGaranties).toEqual(undefined)
        })
    })
})
