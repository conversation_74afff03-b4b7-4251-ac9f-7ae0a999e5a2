<div class="Form-field is-medium">
    <label class="Form-field-label">
        {{ TRANSLATION_PREFIX + 'garantie-concernee' | translate }}
    </label>
    <div class="Form-field-input">
        <div class="Panel u-has-margin-0 u-is-full-width u-has-margin-bottom-16 is-clickable">
            @for (garantie of garanties | sortAlphabetically; track trackByGarantieCode(i, garantie); let last = $last; let i = $index) {
                @if (isGarantieSelectable(garantie)) {
                    <garantie-panel [garantie]="garantie" (selectGarantie)="toggleGarantie(garantie)">
                        @if (isGarantieSelectable(garantie)) {
                            <div class="Radio is-check is-large u-has-margin-0 u-has-padding-left-16 u-has-padding-right-8">
                                <input
                                    id="radio-garantie-{{ i }}-big"
                                    name="radio-garantie-{{ i }}"
                                    type="radio"
                                    [checked]="selectedGarantiesContainsGarantie(garantie)"
                                    (click)="toggleGarantie(garantie)" />
                                <label for="radio-garantie-{{ i }}-big"></label>
                            </div>
                        }
                    </garantie-panel>
                }
                @if (isGarantieSelectable(garantie) && !last) {
                    <hr class="Panel-separator u-has-margin-0" />
                }
            }
            <hr class="Panel-separator u-has-margin-0" />
            <div class="DataTile u-has-padding-16" (click)="toggleAutreGarantie()">
                <div class="DataTile-content u-has-padding-0 autreGarantieDataTile">
                    <div class="DataTile-label">
                        {{ TRANSLATION_PREFIX + 'autre-garantie' | translate }}
                    </div>
                </div>
                <div class="Radio is-check is-large u-has-margin-0 u-has-padding-left-16 u-has-padding-right-8">
                    <input
                        id="radio-no-garantie-big"
                        name="radio-garantie-no-garantie"
                        type="radio"
                        [checked]="typeGaranties && typeGaranties.length === 0"
                        (click)="toggleAutreGarantie()" />
                    <label for="radio-no-garantie-big"></label>
                </div>
            </div>
        </div>
    </div>
</div>
