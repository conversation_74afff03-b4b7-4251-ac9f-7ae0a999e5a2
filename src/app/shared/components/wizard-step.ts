import { WizardAction } from '../enums/wizard-action.enum'
import { WizardSteps } from '../enums/wizard-steps.enum'
import { WizardService } from '../services/wizard/wizard.service'
import { effect } from '@angular/core'

export abstract class WizardStep {
    protected currentWizardStep?: WizardSteps

    protected constructor(protected wizardService: WizardService) {
        this.wizardService = wizardService

        effect(
            () => {
                const action = this.wizardService.wizardState$()

                if (!action) {
                    return
                }

                switch (action) {
                    case WizardAction.ON_NEXT:
                        this.wizardService.updateState(
                            WizardAction.SHOW_LOADING
                        )
                        this.onNext().finally(() =>
                            this.wizardService.updateState(
                                WizardAction.HIDE_LOADING
                            )
                        )
                        break

                    case WizardAction.ON_PREVIOUS:
                        this.onBack().finally(() =>
                            this.wizardService.updateState(
                                WizardAction.HIDE_LOADING
                            )
                        )
                        break

                    case WizardAction.ON_SECONDARY:
                        this.onSecondary()
                        break
                }
            },
            { allowSignalWrites: true }
        )
    }

    init(): void {
        if (this.currentWizardStep) {
            const config = this.wizardService.getStepConfig(
                this.currentWizardStep
            )
            this.wizardService.setCurrentStep(config.step)
        }
    }

    /**
     * Fonction appelée lors du passage à l'étape suivante
     */
    protected abstract onNext(): Promise<boolean>

    /**
     * Fonction appelée lors du passage à l'étape pécédente
     */
    protected abstract onBack(): Promise<boolean>

    /**
     * Fonction appelée lors du clic sur le bouton secondaire
     */
    protected onSecondary(): void {}
}
