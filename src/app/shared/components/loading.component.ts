import { ChangeDetectorRef } from '@angular/core'
import { finalize, Observable } from 'rxjs'

export abstract class LoadingComponent {
    private internalIsLoading = false

    protected constructor(protected readonly cdRef: ChangeDetectorRef) {}

    get isLoading(): boolean {
        return this.internalIsLoading
    }

    protected executeWithLoadingVisible<T>(
        action$: Observable<T>
    ): Observable<T> {
        this.showLoading()
        return action$.pipe(
            finalize(() => {
                this.hideLoading()
            })
        )
    }

    private showLoading(): void {
        this.internalIsLoading = true
        this.cdRef.markForCheck()
    }

    private hideLoading(): void {
        this.internalIsLoading = false
        this.cdRef.markForCheck()
    }
}
