import { ChangeDetectionStrategy, Component, Input } from '@angular/core'

@Component({
    selector: 'informative-panel',
    templateUrl: './informative-panel.component.html',
    styleUrls: ['./informative-panel.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
})
export class InformativePanelComponent {
    @Input()
    title?: string

    @Input()
    subTitle?: string

    @Input()
    imageSource =
        'https://static.foyer.lu/images/DA/folder.DAFA2AB5F527ECBA91AB1EA0B98F8FC9D67EDD4B.svg'
}
