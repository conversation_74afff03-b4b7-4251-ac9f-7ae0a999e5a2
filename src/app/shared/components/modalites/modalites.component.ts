import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    DoCheck,
    inject,
    Input,
} from '@angular/core'
import { MessageBannerComponent } from '../message-banner/message-banner.component'

import { I18nPipe } from '@foyer/ng-i18n'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { NgxMaskDirective } from 'ngx-mask'
import { ModalitesForm } from './forms/modalites.form'
import { ModalitesFormStructure } from './forms/modalites-form-structure'
import { PourcentageTva } from '../../../auto/enums/pourcentage-tva.enum'

@Component({
    selector: 'modalites',
    standalone: true,
    imports: [
        I18nPipe,
        ReactiveFormsModule,
        MessageBannerComponent,
        NgxMaskDirective,
    ],
    templateUrl: './modalites.component.html',
    styleUrl: './modalites.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalitesComponent implements DoCheck {
    private readonly changeDetectorRef: ChangeDetectorRef =
        inject(ChangeDetectorRef)

    @Input()
    form?: ModalitesForm
    @Input()
    hasPourcentageTvaRecupere = false

    readonly TRANSLATION_PREFIX: string = 'common.modalites.'

    protected readonly PourcentageTva = PourcentageTva

    asFormGroup(form: ModalitesForm): FormGroup<ModalitesFormStructure> {
        return form as unknown as FormGroup<ModalitesFormStructure>
    }

    ngDoCheck(): void {
        this.changeDetectorRef.markForCheck()
    }
}
