import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ModalitesComponent } from './modalites.component'
import { circonstancesMock } from '../../mocks/circonstances.mock'
import { ModalitesForm } from './forms/modalites.form'
import { PourcentageTva } from '../../../auto/enums/pourcentage-tva.enum'

describe('ModalitesComponent', () => {
    let component: ModalitesComponent
    let fixture: ComponentFixture<ModalitesComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ModalitesComponent],
        }).compileComponents()
    })

    const initializeComponent = (hasPourcentageTvaRecupere: boolean): void => {
        fixture = TestBed.createComponent(ModalitesComponent)
        component = fixture.componentInstance
        component.form = new ModalitesForm(hasPourcentageTvaRecupere)
        component.hasPourcentageTvaRecupere = hasPourcentageTvaRecupere
        fixture.detectChanges()
    }

    describe('testing around compteBancaire', () => {
        beforeEach(() => {
            initializeComponent(true)
        })

        it('is invalid when compteBancaire iban is not valid : ****************', () => {
            component.form?.controls.compteBancaire.setValue('****************')
            component.form?.controls.compteBancaire.markAsTouched()
            fixture.detectChanges()
            expect(component.form?.controls.compteBancaire.valid).toBeFalsy()
            expect(
                component.form?.controls.compteBancaire.errors &&
                    component.form?.controls.compteBancaire.errors['isIban']
            ).toBeTruthy()
        })

        it('is valid when compteBancaire is : ****************', () => {
            component.form?.controls.compteBancaire.setValue(
                circonstancesMock()[0].key
            )
            component.form?.controls.compteBancaire.setValue('****************')
            component.form?.controls.compteBancaire.markAsTouched()
            fixture.detectChanges()
            expect(component.form?.controls.compteBancaire.valid).toBeTruthy()
        })

        it('should convert empty string to undefined', () => {
            component.form?.controls.compteBancaire.setValue('')
            fixture.detectChanges()

            expect(
                component.form?.controls.compteBancaire.value
            ).toBeUndefined()
        })

        it('should filter out invalid characters', () => {
            component.form?.controls.compteBancaire.setValue(
                'BE50-3630-5159-2118'
            )
            fixture.detectChanges()

            expect(component.form?.controls.compteBancaire.value).toBe(
                '****************'
            )
        })
    })

    describe('Habitation Mode', () => {
        beforeEach(() => {
            initializeComponent(true)
        })

        describe('testing pourcentageTvaRecupere', () => {
            it('should disable pourcentageTvaRecupere when preneurSoumisTva is false', () => {
                component.form?.setPreneurSoumisTva(false)
                fixture.detectChanges()

                const pourcentageControl =
                    component.form?.controls.pourcentageTvaRecupere
                expect(pourcentageControl).toBeDefined()
                expect(pourcentageControl?.disabled).toBeTruthy()
                expect(pourcentageControl?.value).toBeNull()
            })

            it('should enable pourcentageTvaRecupere when preneurSoumisTva is true', () => {
                component.form?.setPreneurSoumisTva(true)
                fixture.detectChanges()

                const pourcentageControl =
                    component.form?.controls.pourcentageTvaRecupere
                expect(pourcentageControl).toBeDefined()
                expect(pourcentageControl?.enabled).toBeTruthy()
            })

            it('should correctly identify currentPourcentage', () => {
                component.form?.setPreneurSoumisTva(true)

                component.form?.setPourcentageTvaRecupere(35)
                expect(component.form?.currentPourcentage).toBe(
                    PourcentageTva.TRENTE_CINQ
                )

                component.form?.setPourcentageTvaRecupere(75)
                expect(component.form?.currentPourcentage).toBe(
                    PourcentageTva.AUTRE
                )
            })
        })

        describe('form validation', () => {
            it('should be invalid when required fields are empty', () => {
                expect(component.form?.valid).toBeFalsy()
            })

            it('should be valid when all required fields are filled correctly', () => {
                component.form?.setPreneurSoumisTva(true)
                component.form?.setPourcentageTvaRecupere(100)
                component.form?.setTva6Pourcent(false)
                component.form?.controls.compteBancaire.setValue(
                    '****************'
                )

                expect(component.form?.valid).toBeTruthy()
            })
        })
    })

    describe('Auto Mode', () => {
        beforeEach(() => {
            initializeComponent(false)
        })

        describe('form structure', () => {
            it('should not have pourcentageTvaRecupere control', () => {
                expect(
                    component.form?.controls.pourcentageTvaRecupere
                ).toBeUndefined()
            })

            it('should have only the required controls for auto', () => {
                expect(component.form?.controls.preneurSoumisTva).toBeDefined()
                expect(component.form?.controls.tva6Pourcent).toBeDefined()
                expect(component.form?.controls.compteBancaire).toBeDefined()
                expect(Object.keys(component.form?.controls ?? []).length).toBe(
                    3
                )
            })
        })

        describe('form validation', () => {
            it('should be invalid when required fields are empty', () => {
                expect(component.form?.valid).toBeFalsy()
            })

            it('should be valid when all required fields are filled correctly', () => {
                component.form?.setPreneurSoumisTva(true)
                component.form?.setTva6Pourcent(false)
                component.form?.controls.compteBancaire.setValue(
                    '****************'
                )

                expect(component.form?.valid).toBeTruthy()
            })
        })
    })
})
