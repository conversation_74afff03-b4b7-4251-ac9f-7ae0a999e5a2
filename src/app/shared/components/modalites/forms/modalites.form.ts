import { FormControl, FormGroup, Validators } from '@angular/forms'
import { ModalitesFormStructure } from './modalites-form-structure'
import { isIbanValidator } from '../../circonstances/forms/is-iban.validator'
import { Subscription } from 'rxjs'
import { PourcentageTva } from '../../../../auto/enums/pourcentage-tva.enum'
import { ModalitesHabitation } from '../../../../habitation/models/modalites-habitation'
import { ModalitesAuto } from '../../../../auto/models/modalites-auto'

export class ModalitesForm extends FormGroup<ModalitesFormStructure> {
    constructor(hasPourcentageTvaRecupere: boolean) {
        const controls: ModalitesFormStructure = {
            preneurSoumisTva: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            tva6Pourcent: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            compteBancaire: new FormControl(undefined, {
                nonNullable: true,
                validators: isIbanValidator(),
            }),
        }

        if (hasPourcentageTvaRecupere) {
            controls.pourcentageTvaRecupere = new FormControl(null, {
                nonNullable: true,
                validators: [
                    Validators.required,
                    Validators.min(1),
                    Validators.max(100),
                ],
            })
        }

        super(controls)

        if (hasPourcentageTvaRecupere && this.controls.pourcentageTvaRecupere) {
            this.registerPreneurSoumisTvaChanges()
        }

        this.registerCompteBancaireInputFilter()
    }

    get currentPourcentage(): PourcentageTva {
        if (!this.controls.pourcentageTvaRecupere) {
            return PourcentageTva.NON_SELECTIONNE
        }

        const value = this.controls.pourcentageTvaRecupere.value

        if (value === null || value === undefined) {
            return PourcentageTva.NON_SELECTIONNE
        }

        switch (value) {
            case 35:
                return PourcentageTva.TRENTE_CINQ
            case 50:
                return PourcentageTva.CINQUANTE
            case 100:
                return PourcentageTva.CENT
            case 0:
                return PourcentageTva.AUTRE
            default:
                return value > 0
                    ? PourcentageTva.AUTRE
                    : PourcentageTva.NON_SELECTIONNE
        }
    }

    setPourcentageTvaRecupere(value: number): void {
        if (this.controls.pourcentageTvaRecupere) {
            this.controls.pourcentageTvaRecupere.setValue(value)
        }
    }

    setPreneurSoumisTva(value: boolean): void {
        this.controls.preneurSoumisTva.setValue(value)
    }

    setTva6Pourcent(value: boolean): void {
        this.controls.tva6Pourcent.setValue(value)
    }

    getValueHabitation(): ModalitesHabitation {
        return this.value as ModalitesHabitation
    }

    getValueAuto(): ModalitesAuto {
        return this.value as ModalitesAuto
    }

    private registerPreneurSoumisTvaChanges(): void {
        if (!this.controls.pourcentageTvaRecupere) {
            return
        }

        this.controls.preneurSoumisTva.valueChanges.subscribe((isSoumisTva) => {
            const pourcentageControl = this.controls.pourcentageTvaRecupere

            if (!pourcentageControl) {
                return
            }

            if (isSoumisTva) {
                pourcentageControl.enable()
            } else {
                pourcentageControl.disable()
                pourcentageControl.setValue(null)
            }
        })
    }

    private registerCompteBancaireInputFilter(): Subscription {
        return this.controls.compteBancaire.valueChanges.subscribe(
            (valueChange) => {
                if (!valueChange) {
                    this.controls.compteBancaire.setValue(undefined, {
                        emitEvent: false,
                    })
                    return
                }

                const filteredValue = valueChange?.replace(/[^A-Z0-9]/g, '')

                if (filteredValue === '') {
                    this.controls.compteBancaire.setValue(undefined, {
                        emitEvent: false,
                    })
                } else {
                    this.controls.compteBancaire.setValue(filteredValue, {
                        emitEvent: false,
                    })
                }
            }
        )
    }
}
