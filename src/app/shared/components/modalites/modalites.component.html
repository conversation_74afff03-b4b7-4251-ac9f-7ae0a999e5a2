@if (form) {
    <ng-container [formGroup]="asFormGroup(form)">
        <div class="Panel u-is-txt-monospace">
            <div class="Panel-header has-small-title">
                <span>{{ TRANSLATION_PREFIX + 'regime-tva' | translate }}</span>
            </div>
            <div class="Panel-body u-has-padding-24">
                <div class="GridFlex-row Form-row">
                    <div class="GridFlex-col-12">
                        <div class="Form-field is-medium">
                            <label class="Form-field-label">
                                {{ TRANSLATION_PREFIX + 'preneur-soumis-tva' | translate }}
                            </label>
                            <div class="Form-field-input">
                                <div class="ButtonGroup is-medium is-full-width" role="radiogroup" aria-label="preneurSoumisTva">
                                    <button
                                        class="is-light"
                                        type="button"
                                        role="radio"
                                        [attr.aria-checked]="form.controls.preneurSoumisTva.value"
                                        (click)="form.setPreneurSoumisTva(true)">
                                        {{ 'common.oui' | translate }}
                                    </button>
                                    <button
                                        class="is-light"
                                        type="button"
                                        role="radio"
                                        [attr.aria-checked]="form.controls.preneurSoumisTva.value === false"
                                        (click)="form.setPreneurSoumisTva(false)">
                                        {{ 'common.non' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        @if ((form.controls.preneurSoumisTva.touched || form.controls.preneurSoumisTva.dirty) && form.controls.preneurSoumisTva.invalid) {
                            <message-banner>
                                @if (form.controls.preneurSoumisTva.errors && form.controls.preneurSoumisTva.errors['required']) {
                                    <span>
                                        {{ 'common.is-option-required' | translate }}
                                    </span>
                                }
                            </message-banner>
                        }
                    </div>
                </div>
                @if (hasPourcentageTvaRecupere && form.controls.pourcentageTvaRecupere && form.controls.preneurSoumisTva.value) {
                    <div class="GridFlex-row Form-row">
                        <div class="GridFlex-col-12">
                            <div class="Form-field is-medium">
                                <label class="Form-field-label">
                                    {{ TRANSLATION_PREFIX + 'pourcentage-tva-recupere' | translate }}
                                </label>
                                <div class="Form-field-input">
                                    <div class="ButtonGroup is-medium is-full-width" role="radiogroup" aria-label="pourcentageTvaRecupere">
                                        <button
                                            class="is-light"
                                            type="button"
                                            role="radio"
                                            [attr.aria-checked]="form.currentPourcentage === PourcentageTva.TRENTE_CINQ"
                                            (click)="form.setPourcentageTvaRecupere(35)">
                                            {{ 'enums.pourcentage-tva.TRENTE_CINQ' | translate }}
                                        </button>
                                        <button
                                            class="is-light"
                                            type="button"
                                            role="radio"
                                            [attr.aria-checked]="form.currentPourcentage === PourcentageTva.CINQUANTE"
                                            (click)="form.setPourcentageTvaRecupere(50)">
                                            {{ 'enums.pourcentage-tva.CINQUANTE' | translate }}
                                        </button>
                                        <button
                                            class="is-light"
                                            type="button"
                                            role="radio"
                                            [attr.aria-checked]="form.currentPourcentage === PourcentageTva.CENT"
                                            (click)="form.setPourcentageTvaRecupere(100)">
                                            {{ 'enums.pourcentage-tva.CENT' | translate }}
                                        </button>
                                        <button
                                            class="is-light"
                                            type="button"
                                            role="radio"
                                            [attr.aria-checked]="form.currentPourcentage === PourcentageTva.AUTRE"
                                            (click)="form.setPourcentageTvaRecupere(0)">
                                            {{ 'enums.pourcentage-tva.AUTRE' | translate }}
                                        </button>
                                    </div>
                                    @if (form.currentPourcentage === PourcentageTva.AUTRE) {
                                        <div class="Form-field-input u-has-margin-left-8">
                                            <input
                                                class="Input pourcentageTvaRecupere is-medium u-is-txt-right"
                                                formControlName="pourcentageTvaRecupere"
                                                [placeholder]="TRANSLATION_PREFIX + 'tva' | translate"
                                                type="text"
                                                suffix="%"
                                                mask="separator.2"
                                                separatorLimit="100" />
                                        </div>
                                    }
                                </div>
                                @if (
                                    (form.controls.pourcentageTvaRecupere.touched || form.controls.pourcentageTvaRecupere.dirty) &&
                                    form.controls.pourcentageTvaRecupere.invalid
                                ) {
                                    <message-banner>
                                        @if (form.controls.pourcentageTvaRecupere.errors && form.controls.pourcentageTvaRecupere.errors['required']) {
                                            <span>
                                                {{ 'common.is-required' | translate: { field: TRANSLATION_PREFIX + 'pourcentage-tva-recupere' | translate } }}
                                            </span>
                                        }
                                        @if (form.controls.pourcentageTvaRecupere.errors && form.controls.pourcentageTvaRecupere.errors['min']) {
                                            <span>
                                                {{ 'common.min' | translate: { min: '1%' } }}
                                            </span>
                                        }
                                        @if (form.controls.pourcentageTvaRecupere.errors && form.controls.pourcentageTvaRecupere.errors['max']) {
                                            <span>
                                                {{ 'common.max' | translate: { max: '100%' } }}
                                            </span>
                                        }
                                    </message-banner>
                                }
                            </div>
                        </div>
                    </div>
                }
                <div class="GridFlex-row Form-row">
                    <div class="GridFlex-col-12">
                        <div class="Form-field is-medium">
                            <label class="Form-field-label">
                                {{ TRANSLATION_PREFIX + 'tva-6-pourcent' | translate }}
                            </label>
                            <div class="Form-field-input">
                                <div class="ButtonGroup is-medium is-full-width" role="radiogroup" aria-label="tva6Pourcent">
                                    <button
                                        class="is-light"
                                        type="button"
                                        role="radio"
                                        [attr.aria-checked]="form.controls.tva6Pourcent.value"
                                        (click)="form.setTva6Pourcent(true)">
                                        {{ 'common.oui' | translate }}
                                    </button>
                                    <button
                                        class="is-light"
                                        type="button"
                                        role="radio"
                                        [attr.aria-checked]="form.controls.tva6Pourcent.value === false"
                                        (click)="form.setTva6Pourcent(false)">
                                        {{ 'common.non' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        @if ((form.controls.tva6Pourcent.touched || form.controls.tva6Pourcent.dirty) && form.controls.tva6Pourcent.invalid) {
                            <message-banner>
                                @if (form.controls.tva6Pourcent.errors && form.controls.tva6Pourcent.errors['required']) {
                                    <span>
                                        {{ 'common.is-option-required' | translate }}
                                    </span>
                                }
                            </message-banner>
                        }
                    </div>
                </div>
            </div>
        </div>
        <div class="Panel u-is-txt-monospace">
            <div class="Panel-header has-small-title">
                <span>{{ TRANSLATION_PREFIX + 'compte-bancaire' | translate }}</span>
            </div>
            <div class="Panel-body u-has-padding-24">
                <div class="GridFlex-row Form-row">
                    <div class="GridFlex-col-12">
                        <div
                            class="Form-field is-medium"
                            [class.is-danger]="
                                (form.controls.compteBancaire.touched || form.controls.compteBancaire.dirty) && form.controls.compteBancaire.invalid
                            ">
                            <label for="compteBancaire" class="Form-field-label">
                                {{ TRANSLATION_PREFIX + 'compte-bancaire' | translate }}
                                <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                            </label>
                            <div class="Form-field-input">
                                <input
                                    type="text"
                                    id="compteBancaire"
                                    formControlName="compteBancaire"
                                    class="Input u-is-full-width is-medium"
                                    [placeholder]="TRANSLATION_PREFIX + 'compte-bancaire-placeholder' | translate" />
                            </div>
                            @if ((form.controls.compteBancaire.touched || form.controls.compteBancaire.dirty) && form.controls.compteBancaire.invalid) {
                                <message-banner>
                                    @if (form.controls.compteBancaire.errors && form.controls.compteBancaire.errors['isIban']) {
                                        <span>
                                            {{ TRANSLATION_PREFIX + 'is-iban' | translate }}
                                        </span>
                                    }
                                </message-banner>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ng-container>
}
