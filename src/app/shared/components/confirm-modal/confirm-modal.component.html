<div class="Modal-wrapper" role="modal" aria-modal="true">
    <div class="Modal is-confirm" [class.is-showed]="visible">
        <div class="Panel">
            @if (config.title && config.title.length > 0) {
                <div class="Panel-header has-small-title">
                    <span id="Modal-Title">{{ config.title | translate }}</span>
                </div>
            }
            <div class="Panel-body">
                @if (config.message) {
                    {{ config.message | translate }}
                }
            </div>
            <div class="Panel-footer">
                <button class="ButtonText is-small buttonCancel" (click)="onCancelClick()">
                    {{ config.buttonCancelLabel | translate }}
                </button>
                <button class="ButtonText is-small buttonConfirm" (click)="onConfirmClick()">
                    {{ config.buttonConfirmLabel | translate }}
                </button>
            </div>
        </div>
    </div>
    <div class="Modal-backdrop"></div>
</div>
