import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    Input,
} from '@angular/core'
import { HideableModalComponent } from '../hideable-modal.component'

import { I18nPipe } from '@foyer/ng-i18n'
import { ConfirmModalConfig } from '../../models/confirm-modal-config'

@Component({
    selector: 'confirm-modal',
    templateUrl: './confirm-modal.component.html',
    styleUrls: ['./confirm-modal.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [I18nPipe],
})
export class ConfirmModalComponent extends HideableModalComponent<
    void,
    ConfirmModalConfig
> {
    @Input()
    visible = true

    constructor(private readonly _changeDetectorRef: ChangeDetectorRef) {
        super(_changeDetectorRef)
    }

    onCancelClick() {
        this.closed.emit()
    }

    onConfirmClick() {
        this.closedWithResult.emit()
    }
}
