import { ComponentFixture, TestBed } from '@angular/core/testing'

import { ConfirmModalComponent } from './confirm-modal.component'
import { ConfirmModalConfig } from '../../models/confirm-modal-config'

describe('ConfirmModalComponent', () => {
    let component: ConfirmModalComponent
    let fixture: ComponentFixture<ConfirmModalComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ConfirmModalComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(ConfirmModalComponent)
        component = fixture.componentInstance
        component.config = {
            title: 'titre',
            buttonConfirmLabel: 'confirm',
            buttonCancelLabel: 'cancel',
        } as ConfirmModalConfig
        fixture.detectChanges()
    })

    it('cancel button click should emit onCancelClick when clicking on "annuler" button', () => {
        spyOn(component, 'onCancelClick')
        spyOn(component.closed, 'emit')
        const button =
            fixture.debugElement.nativeElement.querySelector('.buttonCancel')
        button.click()
        fixture.detectChanges()
        expect(component.onCancelClick).toHaveBeenCalled()
    })

    it('confirm button click should onConfirmClick true when clicking on "confirm" button', () => {
        spyOn(component, 'onConfirmClick')
        spyOn(component.closedWithResult, 'emit')
        const button =
            fixture.debugElement.nativeElement.querySelector(`.buttonConfirm`)
        button.click()
        fixture.detectChanges()
        expect(component.onConfirmClick).toHaveBeenCalled()
    })
})
