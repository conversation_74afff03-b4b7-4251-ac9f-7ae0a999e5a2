@if (text) {
    <div #tooltip class="Tooltip is-top-oriented" role="tooltip" [hidden]="!isTooltipVisible" aria-live="polite" tabindex="0">
        @switch (textType) {
            @case (TextType.TEXT) {
                <p>{{ text | translate }}</p>
            }
            @case (TextType.HTML) {
                <p><span [innerHTML]="text"></span></p>
            }
            @case (TextType.TEXT_TRANSLATED) {
                <p>{{ text }}</p>
            }
        }
    </div>
}
<span (mouseenter)="showTooltip($event)" (mouseleave)="hideTooltip()">
    <ng-content></ng-content>
</span>
