import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    Input,
    ViewChild,
} from '@angular/core'
import { TextType } from '../../enums/text-type.enum'

import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'with-tooltip',
    templateUrl: './with-tooltip.component.html',
    styleUrls: ['./with-tooltip.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [I18nPipe],
})
export class WithTooltipComponent {
    @Input()
    text?: string
    @Input()
    textType: TextType = TextType.TEXT
    @Input()
    preventDefaultClickBehavior = true

    @ViewChild('tooltip') tooltipElement?: ElementRef<HTMLDivElement>
    isTooltipVisible = false

    protected readonly TextType = TextType

    showTooltip(event: MouseEvent) {
        this.isTooltipVisible = !this.isTooltipVisible
        if (this.tooltipElement) {
            const tooltip = this.tooltipElement.nativeElement
            const boundingClientRect = tooltip.getBoundingClientRect()
            const width = boundingClientRect.width
            tooltip.style.left = `${event.x - width + 55}px`
            tooltip.style.top = `${event.y - boundingClientRect.height - 20}px`
        }
        if (this.preventDefaultClickBehavior) {
            event.preventDefault()
            event.stopPropagation()
        }
    }

    hideTooltip() {
        this.isTooltipVisible = false
    }
}
