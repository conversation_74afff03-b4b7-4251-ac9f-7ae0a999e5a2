import { ComponentFixture, TestBed } from '@angular/core/testing'
import { WithTooltipComponent } from './with-tooltip.component'
import { By } from '@angular/platform-browser'
import { expectElementToExist } from '../../utils/test.utils'

describe('WithTooltipComponent', () => {
    let component: WithTooltipComponent
    let fixture: ComponentFixture<WithTooltipComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [WithTooltipComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(WithTooltipComponent)
        component = fixture.componentInstance
        component.isTooltipVisible = false
        component.text = 'Hello world!'
    })

    it('should show tooltip if request to', () => {
        component.showTooltip({
            x: 110,
            y: 120,
            preventDefault: () => {},
            stopPropagation: () => {},
        } as MouseEvent)
        fixture.detectChanges()
        const tooltip = fixture.debugElement.query(By.css('.Tooltip'))
        expectElementToExist(fixture, '.Tooltip')
        expect(tooltip.attributes['hidden']).not.toBeNull()
    })

    it('should hide tooltip if request to', () => {
        component.hideTooltip()
        fixture.detectChanges()
        const tooltip = fixture.debugElement.query(By.css('.Tooltip'))
        expectElementToExist(fixture, '.Tooltip')
        expect(tooltip.attributes['hidden']).toBe('')
    })
})
