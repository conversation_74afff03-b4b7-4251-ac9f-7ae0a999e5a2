import { ComponentFixture, TestBed } from '@angular/core/testing'

import { FilterBarComponent } from './filter-bar.component'
import { FiltersService } from '../../services/filter/filters.service'
import { FiltersServiceMock } from '../../services/filter/filters.service.mock'
import { By } from '@angular/platform-browser'
import { FilterType } from '../../enums/filter-type.enum'
import { FiltersBuilder } from '../../builders/filters-builder'
import { DebugElement } from '@angular/core'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { FilterDate } from '../../enums/filter-date.enum'
import { FilterBarForm } from './forms/filter-bar.form'
import { filterContrat } from '../../mocks/filter.mock'
import { provideTranslation } from '../../../core/providers/translation.provider'

describe('FilterBarComponent', () => {
    let component: FilterBarComponent
    let fixture: ComponentFixture<FilterBarComponent>
    let filtersService: FiltersService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [FilterBarComponent],
            providers: [
                provideTranslation(),
                { provide: FiltersService, useClass: FiltersServiceMock },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(FilterBarComponent)
        component = fixture.componentInstance
        filtersService = TestBed.inject(FiltersService)
    })

    describe('Toggle', () => {
        it('should toggle expandedFilters when toggleDropdown is called', () => {
            expect(component.expandedFilters).toBeFalsy()
            component.toggleDropdown()
            expect(component.expandedFilters).toBeTruthy()
        })

        it('should add "is-opened" class when expandedFilters is true', () => {
            component.expandedFilters = true
            fixture.detectChanges()
            const element = fixture.debugElement.query(By.css('.Dropdown-menu'))
            expect(
                element.nativeElement.classList.contains('is-opened')
            ).toBeTrue()
        })

        it('should remove "is-opened" class when expandedFilters is false', () => {
            component.expandedFilters = false
            fixture.detectChanges()
            const element = fixture.debugElement.query(By.css('.Dropdown-menu'))
            expect(
                element.nativeElement.classList.contains('is-opened')
            ).toBeFalse()
        })
    })

    describe('When we remove filters', () => {
        it('should call onRemoveAllFilters from filtersService when onRemoveAllFilters is called', () => {
            spyOn(filtersService, 'removeAllFilters')
            component.onRemoveAllFilters()
            expect(filtersService.removeAllFilters).toHaveBeenCalledOnceWith()
        })

        it('should call onRemoveFilter from filtersService when onRemoveFilter is called with expected params', () => {
            spyOn(filtersService, 'removeFilter')
            component.onRemoveFilter(filterContrat()[0])
            expect(filtersService.removeFilter).toHaveBeenCalledOnceWith(
                filterContrat()[0]
            )
        })
    })

    describe('Badges', () => {
        let badges: DebugElement[]

        beforeEach(() => {
            component.filters = new FiltersBuilder()
                .addFilter(FilterType.CONTRAT, '12345678')
                .addFilter(FilterType.GARANTIES, TypeGaranties.TGN)
                .addFilter(FilterType.DATE_DE_SURVENANCE, FilterDate.SEMAINE)
                .build()
            fixture.detectChanges()
            badges = fixture.debugElement.queryAll(By.css('.Badge'))
        })

        const checkBadgeContentForFilter = (
            badgeNumberSelector: number,
            textToCheck: string
        ): void => {
            const elementText =
                badges[badgeNumberSelector].nativeElement.textContent.trim()
            expect(elementText).toContain(textToCheck)
        }

        it('should display badge content for filter CONTRAT', () => {
            checkBadgeContentForFilter(1, '12345678')
        })

        it('should display badge content for filter GARANTIES', () => {
            checkBadgeContentForFilter(2, 'Tempête, grêle, neige')
        })

        it('should display badge content for filter DATE_DE_SURVENANCE', () => {
            checkBadgeContentForFilter(3, 'Cette semaine')
        })

        it('should call onRemoveFilter when close button is clicked', () => {
            spyOn(component, 'onRemoveFilter')
            const closeButton = badges[2].query(By.css('i')).nativeElement
            closeButton.click()
            fixture.detectChanges()
            expect(component.onRemoveFilter).toHaveBeenCalledOnceWith(
                component.filters[1]
            )
        })
    })

    describe('Search', () => {
        beforeEach(() => {
            spyOn(component, 'onSearchButtonPushed').and.callThrough()
            spyOn(component, 'onEnterKeyPressed').and.callThrough()
            spyOn(filtersService, 'applyNewFilters')
            component.form = new FilterBarForm()
            component.form.controls.search.setValue('12345678')
            component.searchTextControl.setValue('12345678')
            fixture.detectChanges()
        })

        it('should execute onEnterKeyPressed and call applyNewFilters from filtersService when pressing on enter', () => {
            const event = new KeyboardEvent('keyup', {
                key: 'Enter',
                code: 'Enter',
            })
            component.onEnterKeyPressed(event)
            expect(component.onEnterKeyPressed).toHaveBeenCalledOnceWith(event)
            expect(filtersService.applyNewFilters).toHaveBeenCalledOnceWith(
                filterContrat()
            )
        })

        it('should execute onSearchButtonPushed and call onEnterKeyPressed then applyNewFilters from filtersService when pressing searchButton', () => {
            const searchButton =
                fixture.debugElement.nativeElement.querySelector(
                    `.searchButton`
                )
            searchButton.click()
            expect(component.onSearchButtonPushed).toHaveBeenCalled()
            expect(component.onEnterKeyPressed).toHaveBeenCalledOnceWith()
            expect(filtersService.applyNewFilters).toHaveBeenCalledOnceWith(
                filterContrat()
            )
        })
    })
})
