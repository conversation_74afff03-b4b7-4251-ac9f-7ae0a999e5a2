<div class="FilterBar u-has-margin-bottom-24">
    @if (form && form.controls.search) {
        <div class="FilterBar-item has-search">
            <div class="FormGroup-field">
                <div class="FormGroup-field-data-filter is-rounded">
                    <div class="Dropdown">
                        <div class="Dropdown-toggle">
                            <button class="SelectAdvanced searchButton" type="button" (click)="onSearchButtonPushed()"></button>
                        </div>
                    </div>
                    <ng-content select="[search-bar-input]"></ng-content>
                </div>
            </div>
        </div>
    }
    <div class="Dropdown FilterBar-item" (click)="toggleDropdown()">
        <button class="ButtonText is-neutral u-has-chevron Dropdown-toggle" aria-controls="filters" aria-expanded="false">
            <i class="mi-filter_list"></i>
            <span>{{ TRANSLATION_PREFIX + 'filtres' | translate }}</span>
            <span class="Badge is-inverted is-primary FilterBar-filter-items-number">1</span>
        </button>
        <div
            id="filters"
            class="Dropdown-menu FilterBar-filter-dropdown has-visible-backdrop is-fullscreen-portrait"
            role="listbox"
            aria-hidden="false"
            [class.is-opened]="expandedFilters">
            <div class="GridFlex">
                <div class="GridFlex-row is-justified-evenly is-nowrap">
                    <ng-content select="[data-col]"></ng-content>
                </div>
            </div>
            <div class="FilterBar-filter-dropdown-footer">
                <button class="ButtonText" type="button">{{ TRANSLATION_PREFIX + 'fermer' | translate }}</button>
                <button class="ButtonContained is-light" type="button" (click)="onRemoveAllFilters()">
                    {{ TRANSLATION_PREFIX + 'reinitialiser' | translate }}
                </button>
            </div>
        </div>
    </div>
    <div class="FilterBar-item has-selected-filters u-is-hidden-portrait">
        <div class="FilterBar-item-visible-selected-filters">
            <div class="badge-col">
                <ng-content select="[badge-col]"></ng-content>
            </div>
            @for (filter of filters; track filter) {
                <span class="Badge is-inverted is-primary">
                    @if (filter.type === FilterType.CONTRAT) {
                        {{ filter.value }}
                    } @else {
                        {{ (filter.type | getTranslationKeyFromFilter) + filter.value | translate }}
                    }
                    <i class="mi-close" (click)="onRemoveFilter(filter)"></i>
                </span>
            }
        </div>
    </div>
</div>
