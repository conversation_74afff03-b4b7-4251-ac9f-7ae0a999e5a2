import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { Filter } from '../../builders/filter'
import { FilterBarForm } from './forms/filter-bar.form'
import { FilterType } from '../../enums/filter-type.enum'
import { FiltersBuilder } from '../../builders/filters-builder'
import { FormControl } from '@angular/forms'
import { FiltersService } from '../../services/filter/filters.service'
import { GetTranslationKeyFromFilterPipe } from '../../pipes/translation/get-translation-key-from-filter.pipe'

import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'filter-bar',
    templateUrl: './filter-bar.component.html',
    styleUrls: ['./filter-bar.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [I18nPipe, GetTranslationKeyFromFilterPipe],
})
export class FilterBarComponent {
    @Input()
    filters: Filter[] = []
    @Input()
    form!: FilterBarForm

    readonly TRANSLATION_PREFIX: string = 'common.filter-bar.'

    expandedFilters = false
    searchTextControl: FormControl = new FormControl('')

    protected readonly FilterType = FilterType

    constructor(private readonly filtersService: FiltersService) {}

    toggleDropdown(): void {
        this.expandedFilters = !this.expandedFilters
    }

    onRemoveAllFilters(): void {
        this.filtersService.removeAllFilters()
    }

    onRemoveFilter(filter: Filter): void {
        this.filtersService.removeFilter(filter)
    }

    onEnterKeyPressed(event?: KeyboardEvent): void {
        const fullText = event?.target
            ? (event.target as HTMLInputElement).value
            : this.form.controls.search.value
        const filter = new FiltersBuilder()
            .addFilter(FilterType.CONTRAT, fullText)
            .build()
        this.filtersService.applyNewFilters(filter)
        this.form.controls.search.reset()
    }

    onSearchButtonPushed(): void {
        this.onEnterKeyPressed()
    }
}
