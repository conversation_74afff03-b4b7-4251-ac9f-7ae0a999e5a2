<nav class="Pagination is-table-display">
    <div class="Pagination-menu">
        <div class="Pagination-menu-left has-separator">
            <label for="Pagination-items-per-page">{{ 'common.pagination.lignes-par-page' | translate }}</label>
            <select #pageSizeSelect id="pagination-items-per-page" name="pagination-items-per-page" class="Select" (change)="handlePageSizeChange($event)">
                @for (pageSizeEntry of PageSizeList; track pageSizeEntry) {
                    <option [value]="pageSizeEntry" [selected]="currentPageSize === pageSizeEntry">
                        {{ pageSizeEntry }}
                    </option>
                }
            </select>
        </div>
        @if (paginationBoundaries$ | async; as paginationBoundaries) {
            <div class="Pagination-menu-right">
                <div>
                    {{
                        'common.pagination.pagination-boundaries'
                            | translate
                                : {
                                      lowerBoundary: paginationBoundaries.lowerBoundary,
                                      higherBoundary: paginationBoundaries.higherBoundary,
                                      totalRecords,
                                  }
                    }}
                </div>
                <div>
                    <button
                        class="Pagination-prev ButtonIcon is-small"
                        id="pagination-previous"
                        name="pagination-previous"
                        type="button"
                        title="Previous"
                        aria-label="Previous"
                        (click)="onPreviousPage()"
                        [disabled]="currentPage === 0"
                        [class.is-disabled]="currentPage === 0">
                        <i class="mi-navigate_before" aria-hidden="true" [class.is-disabled]="currentPage === 0"></i>
                    </button>
                    <button
                        class="Pagination-next ButtonIcon is-small"
                        id="pagination-next"
                        name="pagination-next"
                        type="button"
                        title="Next"
                        aria-label="Next"
                        (click)="onNextPage()"
                        [class.is-disabled]="currentPage === totalPages - 1"
                        [disabled]="currentPage === totalPages - 1">
                        <i class="mi-navigate_next" aria-hidden="true" [class.is-disabled]="currentPage === totalPages - 1"></i>
                    </button>
                </div>
            </div>
        }
    </div>
</nav>
