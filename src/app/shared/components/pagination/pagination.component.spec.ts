import { ComponentFixture, TestBed } from '@angular/core/testing'
import { PaginationComponent } from './pagination.component'
import { PageSize } from '../../enums/page-size.enum'
import { expectElementToExist } from '../../utils/test.utils'

describe('PaginationComponent', () => {
    let component: PaginationComponent
    let fixture: ComponentFixture<PaginationComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [PaginationComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(PaginationComponent)
        component = fixture.componentInstance

        component.totalRecords = 63
        component.currentPageSize = PageSize.TWENTY_FIVE_ELEMENTS
        component.currentPage = 2
        component.totalPages = 3
        fixture.detectChanges()
    })

    it('should create Lignes par page dropdown', () => {
        expectElementToExist(fixture, '#pagination-items-per-page')
    })

    it('should create Précedent et Suivant buttons', () => {
        expectElementToExist(fixture, '#pagination-previous')
        expectElementToExist(fixture, '#pagination-next')
    })

    it('should call onNextPage', () => {
        spyOn(component, 'onNextPage').and.callThrough()
        spyOn(component.pageChangedEvent, 'emit')
        component.onNextPage()
        expect(component.onNextPage).toHaveBeenCalled()
        expect(component.currentPage).toEqual(2)
    })

    it('should call onPreviousPage', () => {
        spyOn(component, 'onPreviousPage').and.callThrough()
        spyOn(component.pageChangedEvent, 'emit')
        component.onPreviousPage()
        expect(component.onPreviousPage).toHaveBeenCalled()
        expect(component.currentPage).toEqual(1)
    })

    it('should call setCurrentPage', () => {
        spyOn(component, 'setCurrentPage').and.callThrough()
        spyOn(component.pageChangedEvent, 'emit')
        component.setCurrentPage(2)
        expect(component.setCurrentPage).toHaveBeenCalled()
        expect(component.currentPage).toEqual(2)
    })

    it('should call handlePageSizeChange', () => {
        spyOn(component, 'handlePageSizeChange').and.callThrough()
        spyOn(component.pageSizeChangedEvent, 'emit')
        component.handlePageSizeChange({
            target: { value: PageSize.FIFTY_ELEMENTS },
        } as unknown as Event)
        expect(component.handlePageSizeChange).toHaveBeenCalled()
        expect(component.currentPage).toEqual(0)
        expect(component.currentPageSize).toEqual(PageSize.FIFTY_ELEMENTS)
    })

    it('should call ngOnChanges', () => {
        spyOn(component, 'ngOnChanges').and.callThrough()
        spyOn(component.pageSizeChangedEvent, 'emit')
        component.ngOnChanges()
        expect(component.ngOnChanges).toHaveBeenCalled()
        component.paginationBoundaries$.subscribe((result) => {
            expect(result).toEqual(
                Object({ lowerBoundary: 51, higherBoundary: 63 })
            )
        })
    })
})
