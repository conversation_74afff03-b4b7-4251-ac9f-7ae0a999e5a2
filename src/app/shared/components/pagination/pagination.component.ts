import {
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnChanges,
    Output,
    ViewChild,
} from '@angular/core'
import { PageSize } from '../../enums/page-size.enum'
import { PaginationBoundariesModel } from './models/pagination-boundaries-model'
import { BehaviorSubject } from 'rxjs'

import { ReactiveFormsModule } from '@angular/forms'
import { AsyncPipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'pagination',
    templateUrl: './pagination.component.html',
    styleUrls: ['./pagination.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [ReactiveFormsModule, AsyncPipe, I18nPipe],
})
export class PaginationComponent implements OnChanges {
    @Input()
    totalPages = 0
    @Input()
    totalRecords = 0
    @Input()
    currentPage = 0
    @Input()
    currentPageSize: PageSize = PageSize.TWENTY_FIVE_ELEMENTS

    @Output()
    readonly pageChangedEvent: EventEmitter<number> = new EventEmitter<number>()
    @Output()
    readonly pageSizeChangedEvent: EventEmitter<PageSize> =
        new EventEmitter<PageSize>()

    @ViewChild('pageSizeSelect') pageSizeSelect!: ElementRef

    PageSizeList = Object.values(PageSize)

    paginationBoundaries$ = new BehaviorSubject<PaginationBoundariesModel>({
        lowerBoundary: 0,
        higherBoundary: 0,
    })

    ngOnChanges(): void {
        this.getPaginationBoundaries()
    }

    onNextPage() {
        if (this.currentPage < this.totalPages - 1) {
            this.setCurrentPage(this.currentPage + 1)
        }
    }

    onPreviousPage() {
        if (this.currentPage > 0) {
            this.setCurrentPage(this.currentPage - 1)
        }
    }

    setCurrentPage(page: number) {
        if (this.currentPage !== page) {
            this.currentPage = page
            this.pageChangedEvent.emit(this.currentPage)
        }
    }

    handlePageSizeChange(event: Event): void {
        const selectElement = event.target as HTMLSelectElement
        this.setCurrentPage(0)
        this.currentPageSize = selectElement.value as PageSize
        this.pageSizeChangedEvent.emit(this.currentPageSize)
    }

    private getPaginationBoundaries(): void {
        const totalRecords: number = this.totalRecords
        const lowerBoundary: number =
            this.currentPage * +this.currentPageSize + 1
        const higherBoundary: number =
            lowerBoundary + +this.currentPageSize - 1 > totalRecords
                ? totalRecords
                : lowerBoundary + +this.currentPageSize - 1
        this.paginationBoundaries$.next({ lowerBoundary, higherBoundary })
    }
}
