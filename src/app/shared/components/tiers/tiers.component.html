<div class="Panel u-is-txt-monospace u-has-margin-top-16">
    <div class="Panel-header has-small-title">
        <span>{{ TRANSLATION_PREFIX + 'title' | translate }}</span>
    </div>

    <div class="Panel-body">
        <ng-content select="[specificForm]"></ng-content>
    </div>

    @if (withTiersForm()) {
        <div class="Panel-body tiersForm" [formGroup]="parentFormGroup()" @expand>
            @if (getDisplayedTiers().length > 0) {
                <div class="Panel tiers-panel">
                    @for (item of getDisplayedTiers(); track item.tiers; let i = $index; let last = $last) {
                        <tiers-datatile [tiers]="item.tiers">
                            <div side class="u-is-flex-align-center">
                                <button type="button" class="ButtonIcon is-small" (click)="editTiers(item.originalIndex)">
                                    <i class="mi-mode_edit u-is-txt-primary-500"></i>
                                </button>
                                <button type="button" class="ButtonIcon is-small" (click)="deleteTiers(item.originalIndex)">
                                    <i class="mi-delete u-is-txt-danger-500"></i>
                                </button>
                            </div>
                        </tiers-datatile>
                        @if (!last) {
                            <hr class="Panel-separator u-has-margin-0" />
                        }
                    }
                </div>
            }

            <ng-container formArrayName="tiers">
                @for (tiers of form().controls; track tiers; let i = $index) {
                    @if (editingIndex === i && shouldShowForm) {
                        <ng-container [formGroupName]="i">
                            <div class="GridFlex-row Form-row">
                                <div class="GridFlex-col-6">
                                    <div
                                        class="Form-field is-medium"
                                        [class.is-danger]="(tiers.controls.nom.touched || tiers.controls.nom.dirty) && tiers.controls.nom.invalid">
                                        <label class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'nom' | translate }}
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                formControlName="nom"
                                                class="Input u-is-full-width"
                                                [placeholder]="TRANSLATION_PREFIX + 'nom-placeholder' | translate" />
                                        </div>
                                    </div>
                                </div>
                                <div class="GridFlex-col-6">
                                    <div class="Form-field is-medium">
                                        <label class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'prenom' | translate }}
                                            <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                formControlName="prenom"
                                                class="Input u-is-full-width"
                                                [placeholder]="TRANSLATION_PREFIX + 'prenom-placeholder' | translate" />
                                        </div>
                                    </div>
                                </div>

                                <div class="GridFlex-col-12">
                                    @if ((tiers.controls.nom.touched || tiers.controls.nom.dirty) && tiers.controls.nom.invalid) {
                                        <message-banner>
                                            @if (tiers.controls.nom.errors && tiers.controls.nom.errors['required']) {
                                                <span>
                                                    {{ 'common.is-required' | translate: { field: TRANSLATION_PREFIX + 'nom' | translate } }}
                                                </span>
                                            }
                                        </message-banner>
                                    }
                                </div>
                            </div>

                            <div class="GridFlex-row Form-row">
                                <div class="GridFlex-col-6">
                                    <div class="Form-field is-medium">
                                        <label class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'numero-plaque' | translate }}
                                            <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                formControlName="numeroPlaque"
                                                class="Input u-is-full-width"
                                                [placeholder]="TRANSLATION_PREFIX + 'numero-plaque-placeholder' | translate" />
                                        </div>
                                    </div>
                                </div>
                                <div class="GridFlex-col-6">
                                    <div class="Form-field is-medium">
                                        <label class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'marque-vehicule' | translate }}
                                            <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                formControlName="marqueVehicule"
                                                class="Input u-is-full-width"
                                                [placeholder]="TRANSLATION_PREFIX + 'marque-vehicule-placeholder' | translate" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="GridFlex-row Form-row">
                                <div class="GridFlex-col-6">
                                    <div class="Form-field is-medium">
                                        <label class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'compagnie-adverse' | translate }}
                                            <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                formControlName="compagnieAdverse"
                                                class="Input u-is-full-width"
                                                [placeholder]="TRANSLATION_PREFIX + 'compagnie-adverse-placeholder' | translate" />
                                        </div>
                                    </div>
                                </div>
                                <div class="GridFlex-col-6">
                                    <div class="Form-field is-medium">
                                        <label class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'contrat-assurance-chez-compagnie-adverse' | translate }}
                                            <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                formControlName="contratAssuranceChezCompagnieAdverse"
                                                class="Input u-is-full-width"
                                                [placeholder]="TRANSLATION_PREFIX + 'contrat-assurance-chez-compagnie-adverse-placeholder' | translate" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <adresse [form]="form().at(i).controls.adresse"></adresse>

                            <div class="GridFlex-row Form-row u-has-margin-vertical-16">
                                <div class="GridFlex-col-12">
                                    <div class="Form-field is-medium">
                                        <label class="Form-field-label">
                                            <span>{{ TRANSLATION_PREFIX + 'tiers-blesse' | translate }}</span>
                                        </label>
                                        <div class="Form-field-input u-is-flex-column">
                                            <yes-no-choice-card
                                                formControlName="tiersBlesse"
                                                [withUnknown]="true"
                                                [useEnum]="OuiNonInconnu"></yes-no-choice-card>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    }
                }

                @if (isEditing) {
                    <button class="ButtonContained is-light" type="button" (click)="confirmEditTiers()">
                        {{ TRANSLATION_PREFIX + 'modifier-tiers' | translate }}
                    </button>
                } @else {
                    <button class="ButtonContained is-light" type="button" (click)="addTiers()">
                        {{ TRANSLATION_PREFIX + (shouldShowForm ? 'ajouter-tiers' : 'ajouter-autre-tiers') | translate }}
                    </button>
                }
            </ng-container>
        </div>
    }
</div>
