import {
    AfterViewChecked,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    effect,
    ElementRef,
    inject,
    input,
} from '@angular/core'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'

import { I18nPipe } from '@foyer/ng-i18n'
import { expandAnimation } from '../../animations/expand.animation'
import { YesNoChoiceCardComponent } from '../yes-no-choice-card/yes-no-choice-card.component'
import { OuiNonInconnu } from '../../enums/oui-non-inconnu.enum'
import { TiersForm } from '../../models/forms/tiers.form'
import { MessageBannerComponent } from '../message-banner/message-banner.component'
import { AdresseComponent } from '../adresse/adresse.component'
import { TiersDatatileComponent } from '../tiers-datatile/tiers-datatile.component'
import { scrollToFirstErrorInForm } from '../../utils/error-display.utils'

@Component({
    selector: 'tiers',
    standalone: true,
    imports: [
        ReactiveFormsModule,
        I18nPipe,
        YesNoChoiceCardComponent,
        MessageBannerComponent,
        AdresseComponent,
        TiersDatatileComponent,
    ],
    templateUrl: './tiers.component.html',
    styleUrls: ['./tiers.component.scss'],
    animations: [expandAnimation],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TiersComponent implements AfterViewChecked {
    private readonly changeDetectorRef: ChangeDetectorRef =
        inject(ChangeDetectorRef)
    private readonly elementRef: ElementRef = inject(ElementRef)

    parentFormGroup = input.required<FormGroup>()
    form = input.required<TiersForm>()
    withTiersForm = input.required()

    readonly TRANSLATION_PREFIX: string = 'common.tiers.'

    shouldShowForm = true
    isEditing = false
    editingIndex: number | null = null

    protected readonly OuiNonInconnu = OuiNonInconnu

    constructor() {
        effect(() => {
            if (this.form().length === 0) {
                this.editingIndex = 0
            } else {
                this.shouldShowForm = false
            }
        })
    }

    ngAfterViewChecked(): void {
        this.changeDetectorRef.markForCheck()
    }

    addTiers(): void {
        if (!this.shouldShowForm) {
            this.form().createArrayControl()
            this.editingIndex = this.form().length - 1
            this.shouldShowForm = true
            this.isEditing = false
            return
        }

        if (this.form().valid) {
            this.shouldShowForm = false
            this.editingIndex = null
        } else {
            this.form().markAllAsTouched()
            scrollToFirstErrorInForm(this.elementRef)
        }
    }

    editTiers(index: number): void {
        if (index < 0 || index >= this.form().length) {
            return
        }
        this.editingIndex = index
        this.shouldShowForm = true
        this.isEditing = true
    }

    confirmEditTiers(): void {
        this.editingIndex = null
        this.shouldShowForm = false
        this.isEditing = false
    }

    deleteTiers(index: number): void {
        this.form().removeAt(index)
    }
}
