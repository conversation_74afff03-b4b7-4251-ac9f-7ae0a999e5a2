import { ComponentFixture, TestBed } from '@angular/core/testing'
import { TiersComponent } from './tiers.component'
import {
    expectElementToExist,
    expectElementToNotExist,
} from '../../utils/test.utils'
import { TiersForm } from '../../models/forms/tiers.form'
import { DetailsSinistreAutoForm } from '../../../auto/components/details-sinistre-auto/forms/details-sinistre-auto.form'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { provideTranslation } from '../../../core/providers/translation.provider'

describe('TiersComponent', () => {
    let component: TiersComponent
    let fixture: ComponentFixture<TiersComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [TiersComponent],
            providers: [provideNoopAnimations(), provideTranslation()],
        }).compileComponents()

        fixture = TestBed.createComponent(TiersComponent)
        component = fixture.componentInstance

        fixture.componentRef.setInput(
            'parentFormGroup',
            new DetailsSinistreAutoForm()
        )
        fixture.componentRef.setInput('form', new TiersForm())
        fixture.componentRef.setInput('withTiersForm', true)
    })

    describe('template', () => {
        it('should hide tiers form when shouldShowTiersForm is false', () => {
            fixture.componentRef.setInput('withTiersForm', false)
            fixture.detectChanges()
            expectElementToNotExist(fixture, '.tiersForm')
        })

        it('should show tiers form when shouldShowTiersForm is true', () => {
            fixture.componentRef.setInput('withTiersForm', true)
            fixture.detectChanges()
            expectElementToExist(fixture, '.tiersForm')
        })

        it('should display "modifier-tiers" button when isEditing is true', () => {
            component.isEditing = true
            fixture.detectChanges()
            const btn = fixture.nativeElement.querySelector(
                'button.ButtonContained'
            )
            expect(btn.textContent).toContain('Modifier le tiers')
        })

        it('should display "ajouter-tiers" or "ajouter-autre-tiers" button when isEditing is false', () => {
            component.isEditing = false
            component.shouldShowForm = true
            fixture.detectChanges()
            let btn = fixture.nativeElement.querySelector(
                'button.ButtonContained'
            )
            expect(btn.textContent).toContain('Ajouter le tiers')

            component.shouldShowForm = false
            fixture.detectChanges()
            btn = fixture.nativeElement.querySelector('button.ButtonContained')
            expect(btn.textContent).toContain('Ajouter un autre tiers')
        })
    })

    describe('addTiers', () => {
        it('should create new array control and show form if shouldShowForm is false', () => {
            component.shouldShowForm = false
            spyOn(component.form(), 'createArrayControl')

            component.addTiers()

            expect(component.form().createArrayControl).toHaveBeenCalled()
            expect(component.editingIndex).toBe(component.form().length - 1)
            expect(component.shouldShowForm).toBeTruthy()
            expect(component.isEditing).toBeFalsy()
        })

        it('should hide form and reset editingIndex if form is valid and shouldShowForm is true', () => {
            component.shouldShowForm = true

            component.addTiers()

            expect(component.shouldShowForm).toBeFalsy()
            expect(component.editingIndex).toBeNull()
        })

        it('should mark all as touched and scroll to first error if form is invalid and shouldShowForm is true', () => {
            component.shouldShowForm = true
            component.form().setErrors({ error: 'Errror' })
            spyOn(component.form(), 'markAllAsTouched')

            component.addTiers()

            expect(component.form().markAllAsTouched).toHaveBeenCalled()
        })
    })

    describe('editTiers', () => {
        it('should do nothing if index is out of bounds (negative)', () => {
            component.shouldShowForm = false

            component.editTiers(-1)

            expect(component.editingIndex).toBeNull()
            expect(component.shouldShowForm).toBeFalsy()
            expect(component.isEditing).toBeFalsy()
        })

        it('should do nothing if index is out of bounds (>= length)', () => {
            component.editingIndex = null
            component.shouldShowForm = false

            component.editTiers(3)

            expect(component.editingIndex).toBeNull()
            expect(component.shouldShowForm).toBeFalsy()
            expect(component.isEditing).toBeFalsy()
        })

        it('should set editingIndex, show form and set isEditing to true for valid index', () => {
            component.form().createArrayControl()
            component.form().createArrayControl()

            component.editTiers(1)

            expect(component.editingIndex).toBe(1)
            expect(component.shouldShowForm).toBeTruthy()
            expect(component.isEditing).toBeTruthy()
        })
    })

    describe('confirmEditTiers', () => {
        it('should reset editingIndex, hide form and set isEditing to false', () => {
            component.editingIndex = 1
            component.shouldShowForm = true
            component.isEditing = true

            component.confirmEditTiers()

            expect(component.editingIndex).toBeNull()
            expect(component.shouldShowForm).toBeFalsy()
            expect(component.isEditing).toBeFalsy()
        })
    })

    describe('deleteTiers', () => {
        it('should call removeAt on form with given index', () => {
            spyOn(component.form(), 'removeAt')
            component.deleteTiers(2)

            expect(component.form().removeAt).toHaveBeenCalledWith(2)
        })
    })
})
