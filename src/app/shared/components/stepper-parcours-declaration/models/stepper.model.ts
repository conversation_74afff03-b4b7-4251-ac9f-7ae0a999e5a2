import { RoutingPath } from '../../../../routing-path.enum'
import { SubRoutingPath } from '../enums/sub-routing-path.enum'

export interface Stepper {
    routingPath: RoutingPath
    subPaths: SubPath[]
}

export interface SubPath {
    path: SubRoutingPath
    valid: boolean
}

export const initialStepperHabitation: Stepper[] = [
    {
        routingPath: RoutingPath.GARANTIES,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.CIRCONSTANCE,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.DOMMAGES,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.MODALITES,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.PIECES_JOINTES,
        subPaths: [],
    },
]

export const initialStepperAuto: Stepper[] = [
    {
        routingPath: RoutingPath.CIRCONSTANCE,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.DOMMAGES,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.GARAGE,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.MODALITES,
        subPaths: [],
    },
    {
        routingPath: RoutingPath.PIECES_JOINTES,
        subPaths: [],
    },
]
