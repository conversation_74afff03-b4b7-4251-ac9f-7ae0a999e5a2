import { ComponentFixture, TestBed } from '@angular/core/testing'

import { StepperParcoursDeclarationComponent } from './stepper-parcours-declaration.component'
import { By } from '@angular/platform-browser'
import {
    dommagesStepperMock,
    garageStepperMock,
    initialCirconstanceHabitationSubPathsMock,
    initialDommagesSubPathsMock,
    initialGarageSubPathsMock,
    initialGarantiesSubPathsMock,
    initialModalitesSubPathsMock,
    initialPiecesJointesSubPathsMock,
    modalitesStepperMock,
    piecesJointesStepperMock,
} from '../../mocks/stepper.mock'
import { StepperService } from '../../services/stepper-state/stepper.service'
import { StepperServiceMock } from '../../services/stepper-state/stepper.service.mock'
import { signal } from '@angular/core'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { RoutingPath } from 'src/app/routing-path.enum'
import {
    checkElementToContainClass,
    checkTextContentForElement,
} from '../../utils/test.utils'
import { SubPath } from './models/stepper.model'

describe('StepperParcoursDeclarationComponent', () => {
    let component: StepperParcoursDeclarationComponent
    let fixture: ComponentFixture<StepperParcoursDeclarationComponent>
    let stepperService: StepperService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [StepperParcoursDeclarationComponent],
            providers: [
                provideTranslation(),
                {
                    provide: StepperService,
                    useClass: StepperServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(StepperParcoursDeclarationComponent)
        component = fixture.componentInstance
        stepperService = TestBed.inject(StepperService)

        spyOn(stepperService, 'setStepper')
        spyOn(stepperService, 'getSteppers')
    })

    const assertStepperElements = (expectedStepper: SubPath[]): void => {
        const subElements = document.querySelectorAll(
            '.TableOfContents-section-content-subheading'
        )

        expectedStepper.forEach((element, index) => {
            const validValue = element.valid

            const elementClassList = subElements[index].classList
            if (validValue) {
                expect(elementClassList).toContain('is-success')
            } else {
                expect(elementClassList).toContain('is-active')
            }
        })
    }

    describe('When currentPath is GARANTIES', () => {
        beforeEach(() => {
            component.currentPath = RoutingPath.GARANTIES
            const initialGarantiesStepper = [
                {
                    routingPath: RoutingPath.GARANTIES,
                    subPaths: initialGarantiesSubPathsMock(),
                },
            ]
            component.steppers = signal(initialGarantiesStepper)
            fixture.detectChanges()
        })

        it('should display expected title and add u-collapse-is-opened class', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section.garanties',
                'u-collapse-is-opened'
            )
            checkTextContentForElement(
                fixture,
                '.TableOfContents-section-heading.garanties',
                'Garanties'
            )
        })

        it('should have 3 li sub elements and they should be as expected', () => {
            const subElements = fixture.debugElement
                .query(By.css('.TableOfContents-section-content'))
                .nativeElement.getElementsByTagName('li')
            expect(subElements.length).toEqual(3)
            assertStepperElements(initialGarantiesSubPathsMock())
        })
    })

    describe('When currentPath is CIRCONSTANCE', () => {
        beforeEach(() => {
            component.currentPath = RoutingPath.CIRCONSTANCE
            const initialCirconstanceStepper = [
                {
                    routingPath: RoutingPath.CIRCONSTANCE,
                    subPaths: initialCirconstanceHabitationSubPathsMock(),
                },
            ]
            component.steppers = signal(initialCirconstanceStepper)
            fixture.detectChanges()
        })

        it('should display expected title and add u-collapse-is-opened class', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section.circonstance',
                'u-collapse-is-opened'
            )
            checkTextContentForElement(
                fixture,
                '.TableOfContents-section-heading.circonstance',
                'Circonstance'
            )
        })

        it('should have 2 li sub elements and they should be as expected', () => {
            const subElements = fixture.debugElement
                .query(By.css('.TableOfContents-section-content'))
                .nativeElement.getElementsByTagName('li')
            expect(subElements.length).toEqual(2)
            assertStepperElements(initialCirconstanceHabitationSubPathsMock())
        })
    })

    describe('When currentPath is DOMMAGES', () => {
        beforeEach(() => {
            component.currentPath = RoutingPath.DOMMAGES
            component.steppers = signal([dommagesStepperMock()])
            fixture.detectChanges()
        })

        it('should display expected title and add u-collapse-is-opened class', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section.dommages',
                'u-collapse-is-opened'
            )
            checkTextContentForElement(
                fixture,
                '.TableOfContents-section-heading.dommages',
                'Dommages'
            )
        })

        it('should have 4 li sub elements and they should be as expected', () => {
            const subElements = fixture.debugElement
                .query(By.css('.TableOfContents-section-content'))
                .nativeElement.getElementsByTagName('li')
            expect(subElements.length).toEqual(4)
            assertStepperElements(initialDommagesSubPathsMock())
        })

        it('sub elements should be is-success', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section-content-subheading.elements-exterieurs',
                'is-success'
            )
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section-content-subheading.pieces',
                'is-success'
            )
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section-content-subheading.objets-dommage',
                'is-success'
            )
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section-content-subheading.autres-objets-dommage',
                'is-success'
            )
        })
    })

    describe('When currentPath is MODALITES', () => {
        beforeEach(() => {
            component.currentPath = RoutingPath.MODALITES
            component.steppers = signal([modalitesStepperMock()])
            fixture.detectChanges()
        })

        it('should display expected title and add u-collapse-is-opened class', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section.modalites',
                'u-collapse-is-opened'
            )
            checkTextContentForElement(
                fixture,
                '.TableOfContents-section-heading.modalites',
                'Modalités'
            )
        })

        it('should have 2 li sub elements and they should be as expected', () => {
            const subElements = fixture.debugElement
                .query(By.css('.TableOfContents-section-content'))
                .nativeElement.getElementsByTagName('li')
            expect(subElements.length).toEqual(2)
            assertStepperElements(initialModalitesSubPathsMock())
        })
    })

    describe('When currentPath is GARAGE', () => {
        beforeEach(() => {
            component.currentPath = RoutingPath.GARAGE
            component.steppers = signal([garageStepperMock()])
            fixture.detectChanges()
        })

        it('should display expected title and add u-collapse-is-opened class', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section.garage',
                'u-collapse-is-opened'
            )
            checkTextContentForElement(
                fixture,
                '.TableOfContents-section-heading.garage',
                'Garage'
            )
        })

        it('should have 2 li sub elements and they should be as expected', () => {
            const subElements = fixture.debugElement
                .query(By.css('.TableOfContents-section-content'))
                .nativeElement.getElementsByTagName('li')
            expect(subElements.length).toEqual(1)
            assertStepperElements(initialGarageSubPathsMock())
        })
    })

    describe('When currentPath is PIECES_JOINTES', () => {
        beforeEach(() => {
            component.currentPath = RoutingPath.PIECES_JOINTES
            component.steppers = signal([piecesJointesStepperMock()])
            fixture.detectChanges()
        })

        it('should display expected title and add u-collapse-is-opened class', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section.pieces-jointes',
                'u-collapse-is-opened'
            )
            checkTextContentForElement(
                fixture,
                '.TableOfContents-section-heading.pieces-jointes',
                'Pièces jointes'
            )
        })

        it('should have 1 li sub elements and they should be as expected', () => {
            const subElements = fixture.debugElement
                .query(By.css('.TableOfContents-section-content'))
                .nativeElement.getElementsByTagName('li')
            expect(subElements.length).toEqual(1)
            assertStepperElements(initialPiecesJointesSubPathsMock())
        })

        it('sub element should be is-success', () => {
            checkElementToContainClass(
                fixture,
                '.TableOfContents-section-content-subheading.pieces-jointes',
                'is-success'
            )
        })
    })
})
