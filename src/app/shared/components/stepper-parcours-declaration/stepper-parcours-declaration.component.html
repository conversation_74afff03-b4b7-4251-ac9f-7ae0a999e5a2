@if (steppers().length > 0) {
    <div class="Panel u-is-txt-monospace">
        <nav class="TableOfContents u-has-cursor-auto">
            @for (stepper of steppers(); track stepper) {
                <div class="Panel-body">
                    <div
                        class="TableOfContents-section u-collapse {{ stepper.routingPath }} "
                        [ngClass]="currentPath === stepper.routingPath ? 'u-collapse-is-opened' : ''">
                        <div
                            class="TableOfContents-section-heading u-collapse-header {{ stepper.routingPath }} u-has-cursor-auto"
                            [ngClass]="stepper.subPaths | subPathsAreSuccessOrActive: currentPath === stepper.routingPath">
                            {{ TRANSLATION_PREFIX + stepper.routingPath + '.title' | translate }}
                        </div>
                        <ol class="TableOfContents-section-content u-collapse-content">
                            @for (subPath of stepper.subPaths; track subPath) {
                                <li
                                    class="TableOfContents-section-content-subheading {{ subPath.path }}"
                                    [ngClass]="subPath.valid ? 'is-success' : 'is-active'">
                                    {{ TRANSLATION_PREFIX + stepper.routingPath + '.sub-title.' + subPath.path | translate }}
                                </li>
                            }
                        </ol>
                    </div>
                </div>
            }
        </nav>
    </div>
}
