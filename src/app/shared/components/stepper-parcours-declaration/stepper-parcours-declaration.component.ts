import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Input,
} from '@angular/core'
import { StepperService } from '../../services/stepper-state/stepper.service'
import { SubPathsAreSuccessOrActivePipe } from '../../pipes/style/sub-paths-are-success-or-active.pipe'

import { NgClass } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { RoutingPath } from '../../../routing-path.enum'

@Component({
    selector: 'stepper-parcours-declaration',
    templateUrl: './stepper-parcours-declaration.component.html',
    styleUrls: ['./stepper-parcours-declaration.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgClass, I18nPipe, SubPathsAreSuccessOrActivePipe],
})
export class StepperParcoursDeclarationComponent {
    private readonly stepperService: StepperService = inject(StepperService)

    @Input()
    currentPath!: RoutingPath

    readonly TRANSLATION_PREFIX: string = 'common.stepper-parcours-declaration.'

    steppers = this.stepperService.getSteppers()

    protected readonly RoutingPath = RoutingPath
}
