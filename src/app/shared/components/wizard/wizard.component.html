<div class="CONTAINER">
    <header class="HEADER">
        <div class="Header is-white">
            <img class="Header-logo" src="https://static.foyer.lu/images/a9/logo-foyer-icon-blue.a9867c812eab98b3484922526a60bcb1179e6214.svg" alt="" />
            <div class="Header-navigation-items has-no-border-left has-no-border-right">
                @if (isShowMenuTabs) {
                    <div class="Tabs-wrapper">
                        <nav class="Tabs" role="tablist" aria-orientation="horizontal" aria-label="navigation">
                            <a
                                class="Tab"
                                data-testid="declarationSinistreTab"
                                role="tab"
                                (click)="goToSelectionRisque()"
                                [ngClass]="{
                                    'is-active u-has-cursor-pointer': !isRouteListeDeclaration(),
                                }"
                                aria-controls="tab-content-1">
                                <span class="Tab-label">
                                    {{ TRANSLATION_PREFIX + 'declarer-un-sinistre' | translate }}
                                </span>
                            </a>
                            <a
                                class="Tab"
                                data-testid="declarationListeTab"
                                role="tab"
                                (click)="goToDeclaration()"
                                [ngClass]="{ 'is-active u-has-cursor-pointer': isRouteListeDeclaration() }"
                                aria-controls="tab-content-2">
                                <span class="Tab-label">
                                    {{ TRANSLATION_PREFIX + 'liste-des-declarations' | translate }}
                                </span>
                            </a>
                        </nav>
                    </div>
                }
            </div>
            @if (isWithFeatureLanguageSelector) {
                <div class="Header-item u-is-hidden-portrait">
                    <div class="Dropdown" (click)="toggleDropdown()">
                        <button class="ButtonText is-neutral u-has-chevron Dropdown-toggle" aria-expanded="false">
                            <span>{{ TRANSLATION_PREFIX + 'language.' + currentLanguage | translate }}</span>
                        </button>
                        <ul class="Dropdown-menu" role="listbox" aria-hidden="false" [class.is-opened]="expandedLanguageDropdown">
                            <li
                                class="Dropdown-item"
                                id="lang-nl"
                                role="option"
                                [class.is-selected]="currentLanguage === Language.DUTCH"
                                (click)="changeLanguage(Language.DUTCH)">
                                <span>{{ TRANSLATION_PREFIX + 'language.nl' | translate }}</span>
                            </li>
                            <li
                                class="Dropdown-item"
                                id="lang-fr"
                                role="option"
                                [class.is-selected]="currentLanguage === Language.FRENCH"
                                (click)="changeLanguage(Language.FRENCH)">
                                <span>{{ TRANSLATION_PREFIX + 'language.fr' | translate }}</span>
                            </li>
                        </ul>
                    </div>
                </div>
            }
        </div>
    </header>
    <main class="CONTENT" [ngClass]="{ 'has-sticky-footer': isShowNavigation }">
        <div class="CONTENT-BODY">
            <router-outlet></router-outlet>
        </div>
    </main>
    @if (isShowNavigation) {
        <footer class="FOOTER">
            <nav class="AppBarBottom is-focused" aria-label="navigation">
                <div class="is-on-left">
                    @if (isPreviousButtonVisible) {
                        <button class="ButtonText AppBarBottom-btn-nav previousButton" (click)="clickPreviousButton()">
                            <i class="mi-keyboard_arrow_left"></i>
                            <span>
                                {{ 'common.precedent' | translate }}
                            </span>
                        </button>
                    }
                </div>
                <div class="is-centered">
                    @if (isSecondaryButtonVisible) {
                        <button
                            [class.is-loading]="isLoading"
                            (click)="clickSecondaryButton()"
                            class="ButtonContained is-neutral AppBarBottom-btn-main is-icon-on-left secondaryButton">
                            <i [className]="secondaryStepIcon" role="presentation"></i>
                            <span>
                                {{ secondaryStepSubTitle | translate }}
                            </span>
                            <span>
                                {{ secondaryStepTitle | translate }}
                            </span>
                        </button>
                    }
                    @if (isNextButtonVisible) {
                        <button
                            [class.is-loading]="isLoading"
                            [disabled]="isNextButtonDisabled"
                            (click)="clickNextButton()"
                            class="ButtonContained is-primary AppBarBottom-btn-main nextButton">
                            <span>
                                {{ nextStepSubTitle | translate }}
                            </span>
                            <span>
                                {{ nextStepTitle | translate }}
                            </span>
                            <i class="mi-keyboard_arrow_right"></i>
                        </button>
                    }
                </div>
                <div class="is-on-right"></div>
            </nav>
        </footer>
    }
</div>
<ng-template viewContainerHost></ng-template>
