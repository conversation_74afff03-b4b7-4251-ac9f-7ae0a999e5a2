import {
    ComponentFixture,
    fakeAsync,
    TestBed,
    tick,
} from '@angular/core/testing'
import { WizardComponent } from './wizard.component'
import { WizardService } from '../../services/wizard/wizard.service'
import { WizardAction } from '../../enums/wizard-action.enum'
import { WizardSteps } from '../../enums/wizard-steps.enum'
import { By } from '@angular/platform-browser'
import { DeclarationService } from '../../services/declaration/declaration.service'
import { DeclarationServiceMock } from '../../services/declaration/declaration.service.mock'
import { provideRouter, Router } from '@angular/router'
import { RoutingPath } from '../../../routing-path.enum'
import { ConfirmModalComponent } from '../confirm-modal/confirm-modal.component'
import { ModalService } from '../../services/modal/modal.service'
import { EventEmitter } from '@angular/core'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { Language } from '../../enums/language.enum'
import { environment } from '../../../../environments/environment'
import { provideTranslation } from '../../../core/providers/translation.provider'
import {
    expectElementToExist,
    expectElementToNotExist,
} from '../../utils/test.utils'

describe('WizardComponent', () => {
    let component: WizardComponent
    let fixture: ComponentFixture<WizardComponent>
    let wizardService: WizardService
    let router: Router
    let declarationService: DeclarationService
    let translationService: TranslationService
    let modalService: ModalService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [WizardComponent],
            providers: [
                provideRouter([]),
                {
                    provide: Router,
                    useValue: {
                        routerState: {
                            snapshot: {
                                url: RoutingPath.LISTE_DECLARATION,
                            },
                        },
                        isActive: jasmine.createSpy('isActive'),
                        navigate: jasmine.createSpy('navigate'),
                    },
                },
                provideTranslation(),
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
            ],
        }).compileComponents()
        fixture = TestBed.createComponent(WizardComponent)
        component = fixture.componentInstance
        wizardService = TestBed.inject(WizardService)
        component = fixture.componentInstance
        modalService = TestBed.inject(ModalService)
        router = TestBed.inject(Router)
        declarationService = TestBed.inject(DeclarationService)
        translationService = TestBed.inject(TranslationService)
        environment.featureWithLanguageSelector = 'oui'
    })

    describe('translation', () => {
        it('should show language radio buttons', () => {
            fixture.detectChanges()
            expectElementToExist(fixture, '#lang-fr')
            expectElementToExist(fixture, '#lang-nl')
        })

        it('should switch language when clicking on nl language button', () => {
            fixture.detectChanges()
            spyOn(translationService, 'changeLanguage')
            const nlButton = fixture.debugElement.query(By.css('#lang-nl'))
            expectElementToExist(fixture, '#lang-nl')
            nlButton.nativeElement.click()

            expect(component.currentLanguage).toEqual(Language.DUTCH)
            expect(translationService.changeLanguage).toHaveBeenCalledWith(
                Language.DUTCH
            )
        })

        it('should switch language when clicking on fr language button', () => {
            fixture.detectChanges()
            spyOn(translationService, 'changeLanguage')
            const button = fixture.debugElement.query(By.css('#lang-fr'))
            expectElementToExist(fixture, '#lang-fr')
            button.nativeElement.click()

            expect(component.currentLanguage).toEqual(Language.FRENCH)
            expect(translationService.changeLanguage).toHaveBeenCalledWith(
                Language.FRENCH
            )
        })
    })

    describe('previousPage button', () => {
        it('should be shown if SHOW_PREVIOUS configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.GARANTIES)
            tick(200)
            expectElementToExist(fixture, '.previousButton')
        }))

        it('should trigger ON_PREVIOUS event when clicked', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.GARANTIES)
            tick(200)
            spyOn(wizardService, 'updateState').and.callThrough()
            const previousButton = fixture.debugElement.query(
                By.css('.previousButton')
            )
            previousButton.nativeElement.click()

            expect(wizardService.updateState).toHaveBeenCalledWith(
                WizardAction.ON_PREVIOUS
            )
        }))

        it('should be hidden if HIDE_PREVIOUS configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.SELECTION_RISQUE)
            tick(200)
            expectElementToNotExist(fixture, '.previousButton')
        }))
    })

    describe('nextPage button', () => {
        it('should be shown if SHOW_NEXT configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.SELECTION_RISQUE)
            tick(200)
            expectElementToExist(fixture, '.nextButton')
        }))

        it('should be loading if SHOW_LOADING configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.PIECES_JOINTES)
            wizardService.updateState(WizardAction.SHOW_LOADING)
            tick(200)
            const nextButton = fixture.debugElement.query(By.css('.nextButton'))
            expect(
                nextButton.nativeElement.classList.contains('is-loading')
            ).toBeTruthy()
        }))

        it('should not be loading if HIDE_LOADING configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.PIECES_JOINTES)
            wizardService.updateState(WizardAction.HIDE_LOADING)
            tick(200)
            const nextButton = fixture.debugElement.query(By.css('.nextButton'))
            expect(
                nextButton.nativeElement.classList.contains('is-loading')
            ).toBeFalsy()
        }))

        it('should trigger ON_NEXT event when clicked', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.SELECTION_RISQUE)
            tick(200)
            spyOn(wizardService, 'updateState').and.callThrough()
            const nextButton = fixture.debugElement.query(By.css('.nextButton'))
            nextButton.nativeElement.click()

            expect(wizardService.updateState).toHaveBeenCalledWith(
                WizardAction.ON_NEXT
            )
        }))

        it('should be hidden if HIDE_NEXT configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.PIECES_JOINTES)
            wizardService.updateState(WizardAction.HIDE_NEXT)
            tick(200)
            expectElementToNotExist(fixture, '.nextButton')
        }))
    })

    describe('secondaryButton button', () => {
        it('should be shown if SHOW_NEXT configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.GARANTIES)
            tick(200)
            expectElementToExist(fixture, '.secondaryButton')
        }))

        it('should trigger ON_SECONDARY event when clicked', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.GARANTIES)
            tick(200)
            spyOn(wizardService, 'updateState').and.callThrough()
            const nextButton = fixture.debugElement.query(
                By.css('.secondaryButton')
            )
            nextButton.nativeElement.click()

            expect(wizardService.updateState).toHaveBeenCalledWith(
                WizardAction.ON_SECONDARY
            )
        }))

        it('should be hidden if HIDE_SECONDARY configured', fakeAsync(() => {
            fixture.detectChanges()
            wizardService.setCurrentStep(WizardSteps.SELECTION_RISQUE)
            tick(200)
            expectElementToNotExist(fixture, '.secondaryButton')
        }))
    })

    describe('goToSelectionRisque', () => {
        describe('with unsaved changes', () => {
            it('should trigger navigate when current position in wizard is in declaration path and modal is confirmed', () => {
                spyOn(declarationService, 'isUpdateInProgress').and.returnValue(
                    true
                )

                const closedWithResult = new EventEmitter<void>()
                spyOn(modalService, 'showConfirmModal').and.callFake(
                    () =>
                        ({
                            closedWithResult,
                        }) as ConfirmModalComponent
                )
                component.goToSelectionRisque()

                expect(modalService.showConfirmModal).toHaveBeenCalledOnceWith(
                    component.viewContainerHost.viewContainerRef,
                    component.getSurDeContinuerModalConfig()
                )
                closedWithResult.emit()

                expect(router.navigate).toHaveBeenCalledOnceWith([
                    '../',
                    RoutingPath.SELECTION_RISQUE,
                ])
            })

            it('should not trigger navigate when current position in wizard is in declaration path and modal is declined', () => {
                spyOn(declarationService, 'isUpdateInProgress').and.returnValue(
                    true
                )
                const closed = new EventEmitter<void>()
                spyOn(modalService, 'showConfirmModal').and.callFake(
                    () =>
                        ({
                            closed,
                            closedWithResult: new EventEmitter<void>(),
                        }) as ConfirmModalComponent
                )

                component.goToSelectionRisque()

                expect(modalService.showConfirmModal).toHaveBeenCalledOnceWith(
                    component.viewContainerHost.viewContainerRef,
                    component.getSurDeContinuerModalConfig()
                )
                closed.emit()

                expect(router.navigate).not.toHaveBeenCalled()
            })
        })

        describe('with saved changes', () => {
            beforeEach(() => {
                spyOn(component, 'isRouteListeDeclaration').and.returnValue(
                    false
                )
                spyOn(declarationService, 'isUpdateInProgress').and.returnValue(
                    false
                )
            })

            it('should trigger navigate when current position in wizard is not in declaration path', () => {
                const closed = new EventEmitter<void>()
                spyOn(modalService, 'showConfirmModal').and.callFake(
                    () =>
                        ({
                            closed,
                            closedWithResult: new EventEmitter<void>(),
                        }) as ConfirmModalComponent
                )

                component.goToSelectionRisque()

                expect(modalService.showConfirmModal).not.toHaveBeenCalled()
                expect(router.navigate).toHaveBeenCalledWith([
                    '../',
                    RoutingPath.SELECTION_RISQUE,
                ])
            })

            it('should trigger navigate when current position in wizard is in declaration path', () => {
                const closed = new EventEmitter<void>()
                spyOn(modalService, 'showConfirmModal').and.callFake(
                    () =>
                        ({
                            closed,
                            closedWithResult: new EventEmitter<void>(),
                        }) as ConfirmModalComponent
                )

                component.goToSelectionRisque()

                expect(modalService.showConfirmModal).not.toHaveBeenCalled()
                expect(router.navigate).toHaveBeenCalledWith([
                    '../',
                    RoutingPath.SELECTION_RISQUE,
                ])
            })
        })
    })

    describe('goToDeclaration', () => {
        it('should trigger navigate to liste declaration when no update in progress', () => {
            spyOn(declarationService, 'isUpdateInProgress').and.returnValue(
                false
            )
            component.goToDeclaration()

            expect(router.navigate).toHaveBeenCalledOnceWith([
                '../',
                RoutingPath.LISTE_DECLARATION,
            ])
        })

        it('should show confirm modal when update in progress and navigate on confirm', () => {
            spyOn(declarationService, 'isUpdateInProgress').and.returnValue(
                true
            )
            const closedWithResult = new EventEmitter<void>()
            spyOn(modalService, 'showConfirmModal').and.callFake(
                () =>
                    ({
                        closedWithResult,
                    }) as ConfirmModalComponent
            )

            component.goToDeclaration()

            expect(modalService.showConfirmModal).toHaveBeenCalledOnceWith(
                component.viewContainerHost.viewContainerRef,
                component.getSurDeContinuerModalConfig()
            )
            closedWithResult.emit()

            expect(router.navigate).toHaveBeenCalledOnceWith([
                '../',
                RoutingPath.LISTE_DECLARATION,
            ])
        })

        it('should show confirm modal when update in progress and stay on cancel', () => {
            spyOn(declarationService, 'isUpdateInProgress').and.returnValue(
                true
            )
            const closed = new EventEmitter<void>()
            spyOn(modalService, 'showConfirmModal').and.callFake(
                () =>
                    ({
                        closed,
                        closedWithResult: new EventEmitter<void>(),
                    }) as ConfirmModalComponent
            )

            component.goToDeclaration()

            expect(modalService.showConfirmModal).toHaveBeenCalledOnceWith(
                component.viewContainerHost.viewContainerRef,
                component.getSurDeContinuerModalConfig()
            )
            closed.emit()

            expect(router.navigate).not.toHaveBeenCalledOnceWith([
                '../',
                RoutingPath.LISTE_DECLARATION,
            ])
        })
    })
})
