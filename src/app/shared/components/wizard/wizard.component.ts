import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    effect,
    On<PERSON><PERSON>roy,
    ViewChild,
} from '@angular/core'
import { WizardService } from '../../services/wizard/wizard.service'
import { WizardAction } from '../../enums/wizard-action.enum'
import { Router, RouterOutlet } from '@angular/router'
import { DeclarationService } from '../../services/declaration/declaration.service'
import { ModalService } from '../../services/modal/modal.service'
import { ConfirmModalComponent } from '../confirm-modal/confirm-modal.component'
import { ViewContainerHostDirective } from '../../directives/view-container-host.directive'
import { Language } from '../../enums/language.enum'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { NgClass } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { environment } from '../../../../environments/environment'
import { ConfirmModalConfig } from '../../models/confirm-modal-config'
import { RoutingPath } from '../../../routing-path.enum'

@Component({
    selector: 'wizard',
    templateUrl: './wizard.component.html',
    styleUrls: ['./wizard.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgClass, RouterOutlet, ViewContainerHostDirective, I18nPipe],
})
export class WizardComponent implements AfterViewInit, OnDestroy {
    @ViewChild(ViewContainerHostDirective, { static: true })
    viewContainerHost!: ViewContainerHostDirective

    readonly TRANSLATION_PREFIX: string = 'common.wizard.'
    readonly DEFAULT_NEXT_STEP_TITLE = 'common.suivant'
    readonly nextStepSubTitle = 'common.prochaine-etape'

    isNextButtonVisible = true
    isNextButtonDisabled = false
    isSecondaryButtonVisible = false
    isPreviousButtonVisible = true
    isLoading = false
    isShowNavigation = true
    isShowMenuTabs = true
    nextStepTitle = this.DEFAULT_NEXT_STEP_TITLE
    secondaryStepTitle = ''
    secondaryStepSubTitle = ''
    secondaryStepIcon = ''
    currentLanguage = Language.FRENCH
    isWithFeatureLanguageSelector = false
    expandedLanguageDropdown = false

    protected readonly RoutingPath = RoutingPath
    protected readonly Language = Language

    constructor(
        private readonly wizardService: WizardService,
        private readonly cdRef: ChangeDetectorRef,
        private readonly router: Router,
        private readonly declarationService: DeclarationService,
        protected readonly modalService: ModalService,
        private readonly translationService: TranslationService
    ) {
        effect(
            () => {
                const step = this.wizardService.currentStep$()
                this.nextStepTitle =
                    step.nextStepTitle ?? this.DEFAULT_NEXT_STEP_TITLE
                this.secondaryStepTitle = step.secondaryStepTitle ?? ''
                this.secondaryStepSubTitle = step.secondaryStepSubTitle ?? ''
                this.secondaryStepIcon = step.secondaryStepIcon ?? ''
                this.isShowNavigation = step.isShowNavigation
                this.isShowMenuTabs = step.isShowMenuTabs
                ;(step.actions || []).forEach((action) => {
                    this.updateButtons(action)
                })

                this.cdRef.detectChanges()
            },
            { allowSignalWrites: true }
        )

        effect(() => {
            const action = this.wizardService.wizardState$()
            if (action) {
                this.updateButtons(action)
                this.cdRef.detectChanges()
            }
        })
    }

    ngAfterViewInit(): void {
        this.currentLanguage = this.translationService.getCurrentLanguage()
        this.isWithFeatureLanguageSelector =
            environment.featureWithLanguageSelector === 'oui'
        this.cdRef.detectChanges()
    }

    ngOnDestroy(): void {
        this.cdRef.detach()
    }

    clickPreviousButton() {
        return this.wizardService.updateState(WizardAction.ON_PREVIOUS)
    }

    clickNextButton() {
        return this.wizardService.updateState(WizardAction.ON_NEXT)
    }

    clickSecondaryButton() {
        return this.wizardService.updateState(WizardAction.ON_SECONDARY)
    }

    isRouteListeDeclaration(): boolean {
        return this.router.routerState.snapshot.url.includes(
            RoutingPath.LISTE_DECLARATION
        )
    }

    goToDeclaration(): void {
        if (!this.declarationService.isUpdateInProgress()) {
            this.router.navigate(['../', RoutingPath.LISTE_DECLARATION])
        } else {
            this.showSurDeContinuerModalAndNavigateIfOk(
                RoutingPath.LISTE_DECLARATION
            )
        }
    }

    goToSelectionRisque(): void {
        if (
            this.isRouteListeDeclaration() &&
            this.declarationService.isUpdateInProgress()
        ) {
            this.showSurDeContinuerModalAndNavigateIfOk(
                RoutingPath.SELECTION_RISQUE
            )
        } else {
            this.resetDeclarationHabitationFormAndNavigate(
                RoutingPath.SELECTION_RISQUE
            )
        }
    }

    getSurDeContinuerModalConfig(): ConfirmModalConfig {
        const modalTranslationPrefix =
            this.TRANSLATION_PREFIX + 'sur-de-continuer-confirm-modal.'
        return {
            title: modalTranslationPrefix + 'title',
            message: modalTranslationPrefix + 'etes-vous-sur',
            buttonConfirmLabel: modalTranslationPrefix + 'confirm',
            buttonCancelLabel: modalTranslationPrefix + 'cancel',
        }
    }

    changeLanguage(language: Language): void {
        this.currentLanguage = language
        this.translationService.changeLanguage(language)
    }

    toggleDropdown() {
        this.expandedLanguageDropdown = !this.expandedLanguageDropdown
    }

    private showSurDeContinuerModalAndNavigateIfOk(
        destination: RoutingPath
    ): void {
        const modal: ConfirmModalComponent = this.modalService.showConfirmModal(
            this.viewContainerHost.viewContainerRef,
            this.getSurDeContinuerModalConfig()
        )
        modal.closedWithResult.subscribe(() => {
            this.resetDeclarationHabitationFormAndNavigate(destination)
        })
    }

    private resetDeclarationHabitationFormAndNavigate(
        destination: RoutingPath
    ): void {
        this.declarationService.clearDeclaration()
        this.router.navigate(['../', destination])
    }

    private updateButtons(action: WizardAction) {
        switch (action) {
            case WizardAction.SHOW_PREVIOUS:
                this.isPreviousButtonVisible = true
                break
            case WizardAction.HIDE_PREVIOUS:
                this.isPreviousButtonVisible = false
                break
            case WizardAction.SHOW_NEXT:
                this.isNextButtonVisible = true
                break
            case WizardAction.HIDE_NEXT:
                this.isNextButtonVisible = false
                break
            case WizardAction.ON_NEXT:
                break
            case WizardAction.ON_PREVIOUS:
                break
            case WizardAction.ENABLE_NEXT:
                this.isNextButtonDisabled = false
                break
            case WizardAction.DISABLE_NEXT:
                this.isNextButtonDisabled = true
                break
            case WizardAction.SHOW_LOADING:
                this.isLoading = true
                break
            case WizardAction.HIDE_LOADING:
                this.isLoading = false
                break
            case WizardAction.SHOW_SECONDARY:
                this.isSecondaryButtonVisible = true
                break
            case WizardAction.HIDE_SECONDARY:
                this.isSecondaryButtonVisible = false
                break
        }
    }
}
