import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CaracteresRestantsComponent } from './caracteres-restants.component'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { checkTextContentForElement } from '../../utils/test.utils'

describe('CaracteresRestantsComponent', () => {
    let component: CaracteresRestantsComponent
    let fixture: ComponentFixture<CaracteresRestantsComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [CaracteresRestantsComponent],
            providers: [provideTranslation()],
        }).compileComponents()

        fixture = TestBed.createComponent(CaracteresRestantsComponent)
        component = fixture.componentInstance
    })

    it('should return expected number (30) when getCaracteresRestants is executed', () => {
        component.formControlLength = 20
        component.maxLength = 50
        const result = component.getCaracteresRestants()
        fixture.detectChanges()
        expect(result).toEqual(30)
    })

    it('should show "caracteres restants" when length is > 1', () => {
        component.formControlLength = 8
        component.maxLength = 10
        component.getCaracteresRestants()
        fixture.detectChanges()
        checkTextContentForElement(
            fixture,
            '.Form-field-helper',
            '2 caractères restants'
        )
    })

    it('should show "caractere restant" when length is 1', () => {
        component.formControlLength = 9
        component.maxLength = 10
        component.getCaracteresRestants()
        fixture.detectChanges()
        checkTextContentForElement(
            fixture,
            '.Form-field-helper',
            '1 caractère restant'
        )
    })

    it('should show "caractere restant" when length is 0', () => {
        component.formControlLength = 10
        component.maxLength = 10
        component.getCaracteresRestants()
        fixture.detectChanges()
        checkTextContentForElement(
            fixture,
            '.Form-field-helper',
            '0 caractère restant'
        )
    })
})
