import { ChangeDetectionStrategy, Component, Input } from '@angular/core'

import { LowerCasePipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'caracteres-restants',
    templateUrl: './caracteres-restants.component.html',
    styleUrls: ['./caracteres-restants.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [LowerCasePipe, I18nPipe],
})
export class CaracteresRestantsComponent {
    @Input()
    formControlLength = 0
    @Input()
    maxLength = 0
    getCaracteresRestants(): number {
        return this.maxLength - this.formControlLength
    }
}
