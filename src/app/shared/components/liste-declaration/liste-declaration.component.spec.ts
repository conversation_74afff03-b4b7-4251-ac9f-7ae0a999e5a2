import { ComponentFixture, TestBed } from '@angular/core/testing'

import { ListeDeclarationComponent } from './liste-declaration.component'
import { DeclarationService } from '../../services/declaration/declaration.service'
import {
    declarationPageItemMock,
    DeclarationServiceMock,
} from '../../services/declaration/declaration.service.mock'
import { InformativePanelComponent } from '../informative-panel/informative-panel.component'
import { By } from '@angular/platform-browser'
import { ModalService } from '../../services/modal/modal.service'
import { ModalServiceMock } from '../../services/modal/modal.service.mock'
import { FoyerDatePipe } from '../../pipes/foyer/foyer-date.pipe'
import { DatePipe, TitleCasePipe } from '@angular/common'
import { NumeroRisqueToPersonnePipe } from '../../pipes/personne/numero-risque-to-personne.pipe'
import { RisquesService } from '../../services/risques/risques.service'
import { RisquesServiceMock } from '../../services/risques/risques.service.mock'
import { BadgeColorDeclarationKindPipe } from '../../pipes/style/badge-color/badge-color-declaration-kind.pipe'
import { PaginationComponent } from '../pagination/pagination.component'
import { PersonneNamePipe } from '../../pipes/personne/personne-name.pipe'
import { PageSize } from '../../enums/page-size.enum'
import { provideRouter, Router } from '@angular/router'
import { EventEmitter } from '@angular/core'
import { ConfirmModalComponent } from '../confirm-modal/confirm-modal.component'
import { ViewContainerHostDirective } from '../../directives/view-container-host.directive'
import { throwError } from 'rxjs'
import { NotificationLevel } from '../../enums/notification-level.enum'
import { TableEmptyCellComponent } from '../table-empty-cell/table-empty-cell.component'
import { WithTooltipComponent } from '../with-tootip/with-tooltip.component'
import { FormatPoliceShortenPipe } from '../../pipes/format-police-shorten.pipe'
import { GetTranslationForArrayEnumPipe } from '../../pipes/translation/get-translation-for-array-enum.pipe'
import { TranslationService } from '../../../core/services/translation/translation.service'
import { Language } from '../../enums/language.enum'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { checkTextContentForElement } from '../../utils/test.utils'
import { Declaration } from '../../models/declaration'
import { RoutingPath } from '../../../routing-path.enum'

describe('ListeDeclarationComponent', () => {
    let component: ListeDeclarationComponent
    let fixture: ComponentFixture<ListeDeclarationComponent>
    let declarationService: DeclarationService
    let modalService: ModalService
    let router: Router
    let translationService: TranslationService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [
                ListeDeclarationComponent,
                InformativePanelComponent,
                FoyerDatePipe,
                NumeroRisqueToPersonnePipe,
                BadgeColorDeclarationKindPipe,
                PaginationComponent,
                PersonneNamePipe,
                ViewContainerHostDirective,
                TableEmptyCellComponent,
                GetTranslationForArrayEnumPipe,
                WithTooltipComponent,
                FormatPoliceShortenPipe,
            ],
            providers: [
                provideRouter([]),
                provideTranslation(),
                TitleCasePipe,
                DatePipe,
                {
                    provide: ModalService,
                    useClass: ModalServiceMock,
                },
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(ListeDeclarationComponent)
        component = fixture.componentInstance
        modalService = TestBed.inject(ModalService)
        router = TestBed.inject(Router)
        declarationService = TestBed.inject(DeclarationService)
        translationService = TestBed.inject(TranslationService)
    })

    describe('data in table', () => {
        beforeEach(() => {
            translationService.changeLanguage(Language.FRENCH)
            fixture.detectChanges()
        })

        it('should show a table with contrat', () => {
            checkTextContentForElement(
                fixture,
                'td.numero-police-column',
                '213546'
            )
        })

        it('should show a table with date de modification', () => {
            checkTextContentForElement(
                fixture,
                'td.modifie-le-column',
                '26.10.2023'
            )
        })

        it('should show a table with risque adresse when habitation', () => {
            checkTextContentForElement(
                fixture,
                'td.risque-column',
                'ROUTE D\'ECH 237, 1471 LUXEMBOURG, LUXEMBOURG'
            )
        })

        it('should show a table with garanties separated by comma', () => {
            checkTextContentForElement(
                fixture,
                'td.garanties-column',
                'Catastrophes naturelles, Accident de la circulation'
            )
        })

        it('should show a table with circonstance', () => {
            checkTextContentForElement(
                fixture,
                'td.circonstance-column',
                'Grêle'
            )
        })

        it('should show a table with nom preneur', () => {
            checkTextContentForElement(
                fixture,
                'td.nom-preneur-column',
                'BALBOA'
            )
        })

        it('should show a table with date-de-survenance', () => {
            checkTextContentForElement(
                fixture,
                'td.date-de-survenance-column',
                '26.10.2023'
            )
        })
    })

    it('should call findWithFilters with sorting with updateAt descending and 10 elements by default', () => {
        spyOn(declarationService, 'findWithFilters')
        fixture.detectChanges()

        expect(declarationService.findWithFilters).toHaveBeenCalledWith(
            PageSize.TEN_ELEMENTS,
            0,
            '-updatedAt'
        )
    })

    it('should redirect to circonstance page with the loaded declaration when clicking on declaration line', () => {
        spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
        spyOn(declarationService, 'updateDeclaration').and.callThrough()

        fixture.detectChanges()
        const element = fixture.debugElement.query(By.css('.declaration-line'))
        element.nativeElement.click()

        expect(declarationService.updateDeclaration).toHaveBeenCalledOnceWith(
            declarationPageItemMock as Declaration
        )
        expect(router.navigate).toHaveBeenCalledOnceWith([
            RoutingPath.HABITATION,
            RoutingPath.GARANTIES,
        ])
    })

    describe('delete button', () => {
        const closedWithResult = new EventEmitter<void>()

        it('should show a confirmation modal and trigger abandonnerDeclaration when clicking on delete button', () => {
            spyOn(declarationService, 'abandonnerDeclaration').and.callThrough()
            spyOn(modalService, 'showConfirmModal').and.callFake(
                () =>
                    ({
                        closedWithResult,
                    }) as ConfirmModalComponent
            )

            fixture.detectChanges()
            const element = fixture.debugElement.query(By.css('.delete-button'))
            element.nativeElement.click()

            expect(modalService.showConfirmModal).toHaveBeenCalledOnceWith(
                component.viewContainerHost.viewContainerRef,
                component.getAbandonnerModalConfig()
            )
            closedWithResult.emit()

            expect(
                declarationService.abandonnerDeclaration
            ).toHaveBeenCalledWith('1265')
        })

        it('should show a an error message when abandonnerDeclaration fails', () => {
            spyOn(modalService, 'showNotification').and.callThrough()
            spyOn(declarationService, 'abandonnerDeclaration').and.returnValue(
                throwError(() => new Error('Failed to load response data'))
            )
            spyOn(modalService, 'showConfirmModal').and.callFake(
                () =>
                    ({
                        closedWithResult,
                    }) as ConfirmModalComponent
            )

            fixture.detectChanges()
            const element = fixture.debugElement.query(By.css('.delete-button'))
            element.nativeElement.click()

            expect(modalService.showConfirmModal).toHaveBeenCalledOnceWith(
                component.viewContainerHost.viewContainerRef,
                component.getAbandonnerModalConfig()
            )
            closedWithResult.emit()

            expect(modalService.showNotification).toHaveBeenCalledOnceWith(
                {
                    notificationLevel: NotificationLevel.DANGER,
                    message: component.TRANSLATION_PREFIX + 'abandonner-error',
                },
                component.viewContainerHost.viewContainerRef
            )
        })
    })
})
