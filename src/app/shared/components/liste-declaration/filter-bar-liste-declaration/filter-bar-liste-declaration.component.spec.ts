import { ComponentFixture, TestBed } from '@angular/core/testing'

import { FilterBarListeDeclarationComponent } from './filter-bar-liste-declaration.component'
import { FiltersService } from '../../../services/filter/filters.service'
import { FiltersServiceMock } from '../../../services/filter/filters.service.mock'
import { By } from '@angular/platform-browser'
import { FilterType } from '../../../enums/filter-type.enum'
import { FilterDate } from '../../../enums/filter-date.enum'
import {
    defaultFilters,
    filterContrat,
    filterDateAujourdhui,
} from '../../../mocks/filter.mock'
import { TypeGaranties } from '../../../enums/type-garanties.enum'
import { provideNgxMask } from 'ngx-mask'
import { provideTranslation } from '../../../../core/providers/translation.provider'

describe('FilterBarListeDeclarationComponent', () => {
    let component: FilterBarListeDeclarationComponent
    let fixture: ComponentFixture<FilterBarListeDeclarationComponent>
    let filtersService: FiltersService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [FilterBarListeDeclarationComponent],
            providers: [
                provideNgxMask(),
                provideTranslation(),
                {
                    provide: FiltersService,
                    useClass: FiltersServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(FilterBarListeDeclarationComponent)
        component = fixture.componentInstance
        filtersService = TestBed.inject(FiltersService)
        fixture.detectChanges()
    })

    describe('When we search a contrat', () => {
        beforeEach(() => {
            component.form.controls.search.setValue('12345678')
        })

        it('should have input value as "12345678"', (done) => {
            fixture.whenStable().then(() => {
                const searchInput = fixture.debugElement.query(
                    By.css('.search')
                )
                const inputValue = searchInput.nativeElement.value
                expect(inputValue).toEqual('12345678')
                done()
            })
        })

        it('should execute onEnterKeyPressed from filterBarComponent when enterKeyPressed', () => {
            const mockEvent = new KeyboardEvent('keyup', { key: 'Enter' })
            spyOn(component.filterBarComponent, 'onEnterKeyPressed')
            component.enterKeyPressed(mockEvent)
            expect(
                component.filterBarComponent.onEnterKeyPressed
            ).toHaveBeenCalledWith(mockEvent)
        })
    })

    describe('When we set the filters', () => {
        beforeEach(() => {
            spyOn(filtersService, 'applyNewFilters')
            spyOn(component.updateFilter, 'emit')
        })

        it('should set the filters correctly when setFilterByFilterType is called', () => {
            component.setFilterByFilterType(FilterType.CONTRAT, '12345678')
            expect(filtersService.applyNewFilters).toHaveBeenCalledWith(
                filterContrat()
            )
            expect(component.updateFilter.emit).toHaveBeenCalled()
        })

        it('should set the filters correctly when setFilterDate is called', () => {
            component.setFilterDate(FilterDate.AUJOURDHUI)
            expect(filtersService.applyNewFilters).toHaveBeenCalledWith(
                filterDateAujourdhui()
            )
            expect(component.updateFilter.emit).toHaveBeenCalled()
        })
    })

    describe('When isCheckedByFilterType is called', () => {
        describe('testing dates with FilterDate AUJOURDHUI is on the list', () => {
            const filters = [...defaultFilters(), ...filterDateAujourdhui()]

            it('should return true when we are looking FilterType DATE_DE_SURVENANCE (AUJOURDHUI)', () => {
                const result = component.isCheckedByFilterType(
                    FilterType.DATE_DE_SURVENANCE,
                    filters,
                    FilterDate.AUJOURDHUI
                )
                expect(result).toBeTruthy()
            })

            it('should return false when we are looking FilterType DATE_DE_SURVENANCE (SEMAINE)', () => {
                const result = component.isCheckedByFilterType(
                    FilterType.DATE_DE_SURVENANCE,
                    filters,
                    FilterDate.SEMAINE
                )
                expect(result).toBeFalsy()
            })
        })

        describe('testing on others filters', () => {
            it('should return true when we are looking FilterType GARANTIES', () => {
                const filters = [...defaultFilters(), ...filterContrat()]
                const result = component.isCheckedByFilterType(
                    FilterType.GARANTIES,
                    filters,
                    TypeGaranties.TGN
                )
                expect(result).toBeFalsy()
            })

            it('should return true when we are looking FilterType CONTRAT', () => {
                const filters = [...defaultFilters(), ...filterContrat()]
                const result = component.isCheckedByFilterType(
                    FilterType.CONTRAT,
                    filters,
                    '12345678'
                )
                expect(result).toBeTruthy()
            })
        })
    })

    describe('When we want to set the filters for badge with default filters for WITHOUT_KIND', () => {
        it('should return filters with length of 0 and don t display WITHOUT_KIND', () => {
            const result = component.filtersForBadge(defaultFilters())
            expect(result.length).toEqual(0)
        })

        it('should filters with length of 1 with DATE_DE_SURVENANCE and don t display WITHOUT_KIND', () => {
            const filters = [...defaultFilters(), ...filterDateAujourdhui()]
            const result = component.filtersForBadge(filters)
            expect(result.length).toEqual(1)
            expect(result[0].type).toEqual(FilterType.DATE_DE_SURVENANCE)
        })
    })
})
