import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core'
import { FilterBarForm } from '../../filter-bar/forms/filter-bar.form'
import { FilterType } from '../../../enums/filter-type.enum'
import { Filter } from '../../../builders/filter'
import { FiltersBuilder } from '../../../builders/filters-builder'
import { FilterBarComponent } from '../../filter-bar/filter-bar.component'
import { Observable, tap } from 'rxjs'
import { DeclarationStateKind } from '../../../enums/declaration-state-kind.enum'
import { FiltersService } from '../../../services/filter/filters.service'
import { TypeGaranties } from '../../../enums/type-garanties.enum'
import { FilterDate } from '../../../enums/filter-date.enum'
import { DefaultOrderKeyValuePipe } from '../../../pipes/default-oder-key-value.pipe'
import { GenerateInputIdPipe } from '../../../pipes/generate-input-id.pipe'
import { EnterKeyEventDirective } from '../../../directives/enter-key-event.directive'
import { ReactiveFormsModule } from '@angular/forms'
import { AsyncPipe, KeyValuePipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { NgxMaskDirective } from 'ngx-mask'
import {
    getDateBoundaries,
    getPeriodFromDates,
} from '../../../utils/date.utils'

@Component({
    selector: 'filter-bar-liste-declaration',
    templateUrl: './filter-bar-liste-declaration.component.html',
    styleUrls: ['./filter-bar-liste-declaration.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        FilterBarComponent,
        ReactiveFormsModule,
        EnterKeyEventDirective,
        AsyncPipe,
        KeyValuePipe,
        I18nPipe,
        GenerateInputIdPipe,
        DefaultOrderKeyValuePipe,
        NgxMaskDirective,
    ],
})
export class FilterBarListeDeclarationComponent implements OnInit {
    @Output()
    readonly updateFilter: EventEmitter<void> = new EventEmitter<void>()

    @ViewChild(FilterBarComponent)
    filterBarComponent!: FilterBarComponent

    readonly TRANSLATION_PREFIX: string =
        'common.liste-declaration.filter-bar-liste-declaration.'

    readonly FilterType = FilterType
    readonly DeclarationStateKind = DeclarationStateKind
    readonly TypeGaranties = TypeGaranties
    readonly FilterDate = FilterDate
    readonly GARANTIES_FOR_FILTER: TypeGaranties[] = [
        TypeGaranties.INCN,
        TypeGaranties.ATTN,
        TypeGaranties.TGN,
        TypeGaranties.CNN,
        TypeGaranties.DGEN,
        TypeGaranties.DELN,
        TypeGaranties.BDGN,
        TypeGaranties.VOLN,
        TypeGaranties.RCIN,
        TypeGaranties.RCN,
        TypeGaranties.JAPI,
    ]

    form: FilterBarForm = new FilterBarForm()
    filters$: Observable<Filter[]> | undefined

    constructor(private readonly filtersService: FiltersService) {}

    ngOnInit(): void {
        this.filters$ = this.filtersService
            .getFilters$()
            .pipe(tap(() => this.updateFilter.emit()))
    }

    enterKeyPressed($event: KeyboardEvent): void {
        this.filterBarComponent.onEnterKeyPressed($event)
    }

    setFilterByFilterType(type: FilterType, value: string): void {
        const newFilters = new FiltersBuilder().addFilter(type, value).build()
        this.filtersService.applyNewFilters(newFilters)
        this.updateFilter.emit()
    }

    setFilterDate(date: FilterDate): void {
        const datePeriod = getDateBoundaries(date)
        const newFilters = new FiltersBuilder()
            .addFilter(
                FilterType.DATE_DE_SURVENANCE,
                datePeriod.beginPeriod.operator + datePeriod.beginPeriod.value
            )
            .addFilter(
                FilterType.DATE_DE_SURVENANCE,
                datePeriod.endPeriod
                    ? datePeriod.endPeriod?.operator +
                          datePeriod.endPeriod?.value
                    : ''
            )
            .build()
        this.filtersService.applyNewFilters(newFilters)
        this.updateFilter.emit()
    }

    isCheckedByFilterType(
        filterType: FilterType,
        filters: Filter[],
        value: string
    ): boolean {
        if (filterType === FilterType.DATE_DE_SURVENANCE) {
            const filterDates = filters
                .filter(
                    (filter) => filter.type === FilterType.DATE_DE_SURVENANCE
                )
                .map((date) => date.value)
            return value === getPeriodFromDates(filterDates)
        }
        return !!filters.find(
            (filter) => filter.type === filterType && filter.value === value
        )
    }

    filtersForBadge(filters: Filter[]): Filter[] {
        const copiedFilters = [...filters]
        const filterDates = copiedFilters.filter(
            (filter) => filter.type === FilterType.DATE_DE_SURVENANCE
        )
        if (filterDates.length > 0) {
            const replacedFilterDates = [this.replaceFilterDates(filterDates)]
            return [
                ...copiedFilters.filter(
                    (filter) =>
                        filter.type !== FilterType.DATE_DE_SURVENANCE &&
                        filter.type !== FilterType.WITHOUT_KIND
                ),
                ...replacedFilterDates,
            ]
        } else {
            return copiedFilters.filter(
                (filter) => filter.type !== FilterType.WITHOUT_KIND
            )
        }
    }

    private replaceFilterDates(filterDates: Filter[]): Filter {
        const filterDatesValues = filterDates.map((date) => date.value)
        return new FiltersBuilder()
            .addFilter(
                FilterType.DATE_DE_SURVENANCE,
                getPeriodFromDates(filterDatesValues)
            )
            .build()[0]
    }
}
