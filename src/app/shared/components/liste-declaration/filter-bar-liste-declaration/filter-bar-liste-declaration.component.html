@if (filters$ | async; as filters) {
    <filter-bar [filters]="filtersForBadge(filters)" [form]="form">
        <input
            search-bar-input
            class="Input search is-medium"
            type="text"
            autocomplete="off"
            mask="0{8}"
            [formControl]="form.controls.search"
            [placeholder]="TRANSLATION_PREFIX + 'search-placeholder' | translate"
            (enterKeyEvent)="enterKeyPressed($event)" />
        <div data-col="1" class="GridFlex-col-6 GridFlex-col-md-auto">
            <h2 class="FilterBar-filter-dropdown-title">{{ TRANSLATION_PREFIX + 'col-garanties.title' | translate }}</h2>
            <ul class="FilterBar-filter-dropdown-content">
                @for (garantieKey of GARANTIES_FOR_FILTER; track garantieKey; let i = $index) {
                    <li class="Checkbox garanties is-seamless">
                        <input
                            [id]="FilterType.GARANTIES | generateInputId: i"
                            type="radio"
                            [checked]="isCheckedByFilterType(FilterType.GARANTIES, filters, garantieKey)"
                            (change)="setFilterByFilterType(FilterType.GARANTIES, garantieKey)" />
                        <label [for]="FilterType.GARANTIES | generateInputId: i">
                            {{ 'enums.code-garantie.' + garantieKey | translate }}
                        </label>
                    </li>
                }
            </ul>
        </div>
        <div data-col="2" class="GridFlex-col-12 GridFlex-col-md-auto">
            <h2 class="FilterBar-filter-dropdown-title">{{ TRANSLATION_PREFIX + 'col-statut.title' | translate }}</h2>
            <ul class="FilterBar-filter-dropdown-content">
                @for (statut of DeclarationStateKind | keyvalue; track statut; let i = $index) {
                    <li class="Checkbox statut is-seamless">
                        <input
                            [id]="FilterType.KIND | generateInputId: i"
                            type="radio"
                            [checked]="isCheckedByFilterType(FilterType.KIND, filters, statut.value)"
                            (change)="setFilterByFilterType(FilterType.KIND, statut.value)" />
                        <label [for]="FilterType.KIND | generateInputId: i">
                            {{ 'enums.declaration-kind.' + statut.value | translate }}
                        </label>
                    </li>
                }
            </ul>
        </div>
        <div data-col="3" class="GridFlex-col-12 GridFlex-col-md-auto">
            <h2 class="FilterBar-filter-dropdown-title">{{ TRANSLATION_PREFIX + 'col-date-de-survenance.title' | translate }}</h2>
            <ul class="FilterBar-filter-dropdown-content">
                @for (date of FilterDate | defaultOrderKeyValue; track date; let i = $index) {
                    <li class="Checkbox is-seamless">
                        <input
                            [id]="FilterType.DATE_DE_SURVENANCE | generateInputId: i"
                            type="radio"
                            [checked]="isCheckedByFilterType(FilterType.DATE_DE_SURVENANCE, filters, date.key)"
                            (change)="setFilterDate(date.value)" />
                        <label [for]="FilterType.DATE_DE_SURVENANCE | generateInputId: i">
                            {{ 'enums.filter-date.' + date.value | translate }}
                        </label>
                    </li>
                }
            </ul>
        </div>
    </filter-bar>
}
