@if (page$ | async; as page) {
    <div class="Panel u-is-txt-monospace u-is-bg-feather-grey-100 u-has-no-border">
        @if (page.totalItems === 0) {
            <informative-panel
                [title]="TRANSLATION_PREFIX + 'aucune-declaration-trouvee' | translate"
                id="emptyPanel"
                imageSource="https://static.foyer.lu/images/DA/folder.DAFA2AB5F527ECBA91AB1EA0B98F8FC9D67EDD4B.svg"></informative-panel>
        }
        @if (page.totalItems > 0) {
            <div class="Panel-body u-has-padding-0">
                <div class="Table-responsive-container">
                    <table class="Table has-odd-background">
                        <thead>
                            <tr>
                                <th scope="col" class="modifie-le-column">
                                    <span>{{ TRANSLATION_PREFIX + 'modifie-le' | translate }}</span>
                                </th>
                                <th scope="col" class="numero-police-column">
                                    <span>{{ TRANSLATION_PREFIX + 'numero-police' | translate }}</span>
                                </th>
                                <th scope="col" class="risque-column">
                                    <span>{{ TRANSLATION_PREFIX + 'risque' | translate }}</span>
                                </th>
                                <th scope="col" class="garanties-column">
                                    <span>{{ TRANSLATION_PREFIX + 'garanties' | translate }}</span>
                                </th>
                                <th scope="col" class="circonstance-column">
                                    <span>{{ TRANSLATION_PREFIX + 'circonstance' | translate }}</span>
                                </th>
                                <th scope="col" class="nom-preneur-column">
                                    <span>{{ TRANSLATION_PREFIX + 'nom-preneur' | translate }}</span>
                                </th>
                                <th scope="col" class="date-de-survenance-column">
                                    <span>{{ TRANSLATION_PREFIX + 'date-de-survenance' | translate }}</span>
                                </th>
                                <th scope="col" class="numero-dossier-column">
                                    <span>{{ TRANSLATION_PREFIX + 'numero-dossier' | translate }}</span>
                                </th>
                                <th scope="col" class="statut-column">
                                    <span>{{ TRANSLATION_PREFIX + 'statut' | translate }}</span>
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            @for (declaration of page.content; track declaration; let i = $index) {
                                <tr
                                    class="declaration-line"
                                    [ngClass]="{ 'u-has-cursor-pointer': declaration.stateKind === DeclarationStateKind.CREE }"
                                    (click)="loadAndGoToDeclarationForCree(declaration)">
                                    <td class="modifie-le-column">{{ declaration.updatedAt | foyerDate }}</td>
                                    <td class="numero-police-column is-nowrap">
                                        {{ declaration.numeroRisque | formatPoliceShorten }}
                                    </td>
                                    <td class="risque-column truncated-cell">
                                        @if (declaration.numeroRisque; as numeroRisque) {
                                            @if (declaration._kind === DeclarationKind.AUTO) {
                                                @if (numeroRisque | numeroRisqueToPlaque | async; as plaque) {
                                                    <with-tooltip [text]="plaque" [textType]="TextType.TEXT_TRANSLATED">
                                                        {{ plaque }}
                                                    </with-tooltip>
                                                }
                                            } @else {
                                                @if (numeroRisque | numeroRisqueToAdresse | async; as adresse) {
                                                    <with-tooltip [text]="adresse" [textType]="TextType.TEXT_TRANSLATED">
                                                        {{ numeroRisque | numeroRisqueToAdresse: true | async }}
                                                    </with-tooltip>
                                                }
                                            }
                                        } @else {
                                            <table-empty-cell></table-empty-cell>
                                        }
                                    </td>
                                    <td class="garanties-column truncated-cell">
                                        @if (
                                            declaration.garanties ? (declaration.garanties | getTranslationForArrayEnum: 'enums.code-garantie.' | async) : null;
                                            as garanties
                                        ) {
                                            <with-tooltip [text]="garanties" [textType]="TextType.TEXT_TRANSLATED">
                                                {{ garanties }}
                                            </with-tooltip>
                                        }
                                    </td>
                                    <td class="circonstance-column truncated-cell">
                                        @if (declaration.circonstance?.circonstance; as circonstance) {
                                            <with-tooltip [text]="'enums.circonstance-key.' + circonstance | translate" [textType]="TextType.TEXT_TRANSLATED">
                                                {{ 'enums.circonstance-key.' + circonstance | translate }}
                                            </with-tooltip>
                                        } @else {
                                            <table-empty-cell></table-empty-cell>
                                        }
                                    </td>
                                    <td class="nom-preneur-column truncated-cell">
                                        @if (declaration.numeroRisque | numeroRisqueToPersonne | async; as personne) {
                                            {{ personne | personneName }}
                                        }
                                    </td>
                                    <td class="date-de-survenance-column">
                                        @if (declaration.survenance.dateDeSurvenance) {
                                            {{ declaration.survenance.dateDeSurvenance | foyerDate }}
                                        } @else {
                                            <table-empty-cell></table-empty-cell>
                                        }
                                    </td>
                                    <td class="date-de-survenance-column">
                                        @if (declaration.stateKind === DeclarationStateKind.SOUMIS && declaration.numeroDossierSinistre) {
                                            <span>{{ declaration.numeroDossierSinistre }}</span>
                                        } @else {
                                            <table-empty-cell></table-empty-cell>
                                        }
                                    </td>
                                    <td class="statut-column">
                                        <div class="u-is-flex-align-center u-is-flex-gap-24">
                                            <span
                                                class="Badge is-small is-inverted u-is-txt-bold"
                                                data-testid="statutBadge"
                                                [ngClass]="declaration.stateKind | badgeColorDeclarationKind">
                                                <span>
                                                    @switch (declaration.stateKind) {
                                                        @case (DeclarationStateKind.CREE) {
                                                            <i class="mi-mode_edit u-has-padding-right-4" aria-hidden="true"></i>
                                                        }
                                                        @case (DeclarationStateKind.SOUMIS) {
                                                            <i class="mi-check u-has-padding-right-4" aria-hidden="true"></i>
                                                        }
                                                    }
                                                </span>
                                                {{ 'enums.declaration-kind.' + declaration.stateKind | translate }}
                                            </span>
                                            @if (declaration.stateKind === DeclarationStateKind.CREE) {
                                                <span class="delete-button" (click)="abandonnerDeclaration(declaration)" stop-click-propagation>
                                                    <i class="mi-delete u-is-txt-danger-500 u-is-txt-16 u-has-cursor-pointer"></i>
                                                </span>
                                            }
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
                @if (page.totalItems > 0) {
                    <div class="u-has-margin-top-8">
                        <pagination
                            [totalRecords]="page.totalItems"
                            [totalPages]="page.totalPages"
                            [currentPage]="page.currentPage"
                            [currentPageSize]="page.pageSize"
                            (pageChangedEvent)="changePage($event)"
                            (pageSizeChangedEvent)="changePageSize($event)"></pagination>
                    </div>
                }
            </div>
        }
    </div>
}
<ng-template viewContainerHost></ng-template>
