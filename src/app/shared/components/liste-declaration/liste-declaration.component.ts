import {
    ChangeDetectionStrategy,
    Component,
    On<PERSON><PERSON>roy,
    OnInit,
} from '@angular/core'
import { PaginatedComponent } from '../../directives/paginated.component'
import { ModalService } from '../../services/modal/modal.service'
import { DeclarationService } from '../../services/declaration/declaration.service'
import { Subscription, switchMap, take } from 'rxjs'
import { PageSize } from '../../enums/page-size.enum'
import { DeclarationStateKind } from '../../enums/declaration-state-kind.enum'
import { SortDirection } from '../../enums/sort-direction.enum'
import { Router } from '@angular/router'
import { ConfirmModalComponent } from '../confirm-modal/confirm-modal.component'
import { NotificationLevel } from '../../enums/notification-level.enum'
import { FilterType } from '../../enums/filter-type.enum'
import { FiltersService } from '../../services/filter/filters.service'
import { GetTranslationForArrayEnumPipe } from '../../pipes/translation/get-translation-for-array-enum.pipe'
import { FormatPoliceShortenPipe } from '../../pipes/format-police-shorten.pipe'
import { NumeroRisqueToPersonnePipe } from '../../pipes/personne/numero-risque-to-personne.pipe'
import { BadgeColorDeclarationKindPipe } from '../../pipes/style/badge-color/badge-color-declaration-kind.pipe'
import { PersonneNamePipe } from '../../pipes/personne/personne-name.pipe'
import { FoyerDatePipe } from '../../pipes/foyer/foyer-date.pipe'
import { ViewContainerHostDirective } from '../../directives/view-container-host.directive'
import { PaginationComponent } from '../pagination/pagination.component'
import { StopClickPropagationDirective } from '../../directives/stop-click-propagation.directive'
import { TableEmptyCellComponent } from '../table-empty-cell/table-empty-cell.component'
import { WithTooltipComponent } from '../with-tootip/with-tooltip.component'
import { InformativePanelComponent } from '../informative-panel/informative-panel.component'
import { AsyncPipe, NgClass, TitleCasePipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { TextType } from '../../enums/text-type.enum'
import { StepperService } from '../../services/stepper-state/stepper.service'
import { Declaration } from '../../models/declaration'
import { ConfirmModalConfig } from '../../models/confirm-modal-config'
import { toSortQueryParam } from '../../utils/pagination-utils'
import { ListeDeclarationPageItem } from '../../models/liste-declaration-page-item'
import { PaginationConfigModel } from '../../models/pagination-config.model'
import { WizardService } from '../../services/wizard/wizard.service'
import { WizardSteps } from '../../enums/wizard-steps.enum'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { NumeroRisqueToPlaquePipe } from '../../pipes/declaration/numero-risque-to-plaque.pipe'
import { NumeroRisqueToAdressePipe } from '../../pipes/declaration/numero-risque-to-adresse.pipe'

@Component({
    selector: 'liste-declaration',
    templateUrl: './liste-declaration.component.html',
    styleUrls: ['./liste-declaration.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        InformativePanelComponent,
        NgClass,
        WithTooltipComponent,
        TableEmptyCellComponent,
        StopClickPropagationDirective,
        PaginationComponent,
        ViewContainerHostDirective,
        AsyncPipe,
        I18nPipe,
        FoyerDatePipe,
        PersonneNamePipe,
        BadgeColorDeclarationKindPipe,
        NumeroRisqueToPersonnePipe,
        NumeroRisqueToPlaquePipe,
        NumeroRisqueToAdressePipe,
        FormatPoliceShortenPipe,
        GetTranslationForArrayEnumPipe,
    ],
    providers: [TitleCasePipe],
})
export class ListeDeclarationComponent
    extends PaginatedComponent<ListeDeclarationPageItem>
    implements OnInit, OnDestroy
{
    readonly TRANSLATION_PREFIX: string = 'common.liste-declaration.'
    readonly DeclarationStateKind = DeclarationStateKind
    readonly DeclarationKind = DeclarationKind

    protected readonly TextType = TextType

    private readonly subscriptions: Subscription = new Subscription()

    constructor(
        protected override readonly modalService: ModalService,
        private readonly declarationService: DeclarationService,
        private readonly router: Router,
        private readonly filtersService: FiltersService,
        private readonly stepperService: StepperService,
        private readonly wizardService: WizardService
    ) {
        super(modalService)
    }

    ngOnInit(): void {
        this.configPaginatedParams()
        this.configPage()
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe()
    }

    loadAndGoToDeclarationForCree(declaration: ListeDeclarationPageItem): void {
        if (declaration.stateKind === DeclarationStateKind.CREE) {
            this.declarationService.initializeForm(declaration._kind)

            this.declarationService.updateDeclaration(
                declaration as Declaration
            )

            this.wizardService.setDeclarationKind(declaration._kind)
            this.stepperService.setInitialStepper(declaration._kind)

            const step = this.wizardService.getNextStep(
                WizardSteps.LISTE_DECLARATION
            )

            this.wizardService.navigateToStep(step, this.router)
        }
    }

    abandonnerDeclaration(declaration: ListeDeclarationPageItem): void {
        const modal: ConfirmModalComponent = this.modalService.showConfirmModal(
            this.viewContainerHost.viewContainerRef,
            this.getAbandonnerModalConfig()
        )
        modal.closedWithResult
            .pipe(
                switchMap(() =>
                    this.declarationService.abandonnerDeclaration(
                        declaration.id
                    )
                )
            )
            .subscribe({
                next: () => {
                    this.refreshView()
                },
                error: () => {
                    this.modalService.showNotification(
                        {
                            notificationLevel: NotificationLevel.DANGER,
                            message:
                                this.TRANSLATION_PREFIX + 'abandonner-error',
                        },
                        this.viewContainerHost.viewContainerRef
                    )
                },
            })
    }

    getAbandonnerModalConfig(): ConfirmModalConfig {
        const modalTranslationPrefix =
            this.TRANSLATION_PREFIX + 'abandonner-confirm-modal.'
        return {
            title: modalTranslationPrefix + 'title',
            message: modalTranslationPrefix + 'etes-vous-sur',
            buttonConfirmLabel: modalTranslationPrefix + 'confirm',
            buttonCancelLabel: modalTranslationPrefix + 'cancel',
        }
    }

    protected configPaginatedParams(): void {
        this.changePageSize(PageSize.TEN_ELEMENTS)
        this.changeSortDirection(
            new Map<string, SortDirection>([
                [FilterType.UPDATED_AT, SortDirection.DESCENDING],
            ])
        )
    }

    protected configPage(): void {
        this.filtersService.applyDefaultFiltersForDeclaration()
        this.page$ = this.paginationConfig$.pipe(
            switchMap((paginationConfig: PaginationConfigModel) =>
                this.declarationService.findWithFilters(
                    paginationConfig.pageSize,
                    paginationConfig.pageIndex,
                    toSortQueryParam(paginationConfig.sort)
                )
            )
        )
    }

    private refreshView() {
        this.subscriptions.add(
            this.paginationConfig$
                .pipe(take(1))
                .subscribe((paginationConfig: PaginationConfigModel) =>
                    this.changePageSize(paginationConfig.pageSize)
                )
        )
    }
}
