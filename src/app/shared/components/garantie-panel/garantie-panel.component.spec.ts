import { ComponentFixture, TestBed } from '@angular/core/testing'

import { GarantiePanelComponent } from './garantie-panel.component'
import { By } from '@angular/platform-browser'
import { DatePipe } from '@angular/common'
import { garantiesMock } from '../../mocks/garanties.mock'

describe('GarantiePanelComponent', () => {
    let component: GarantiePanelComponent
    let fixture: ComponentFixture<GarantiePanelComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [GarantiePanelComponent],
            providers: [DatePipe],
        }).compileComponents()

        fixture = TestBed.createComponent(GarantiePanelComponent)
        component = fixture.componentInstance
        component.garantie = garantiesMock()[0]
        fixture.detectChanges()
    })

    describe('When the user click anywhere on the panel', () => {
        beforeEach(() => {
            component.garantie = garantiesMock()[0]
            fixture.detectChanges()
        })

        it('should emit the selected garantie event', () => {
            spyOn(component.selectGarantie, 'emit')
            const panelElement = fixture.debugElement.query(By.css('.DataTile'))

            panelElement.nativeElement.click()
            expect(component.selectGarantie.emit).toHaveBeenCalledWith(
                garantiesMock()[0]
            )
        })

        it('should not emit the selected garantie event when isDisabled is true', () => {
            spyOn(component.selectGarantie, 'emit')
            const panelElement = fixture.debugElement.query(By.css('.DataTile'))

            component.isDisabled = true
            fixture.detectChanges()

            panelElement.nativeElement.click()
            expect(component.selectGarantie.emit).not.toHaveBeenCalled()
        })
    })
})
