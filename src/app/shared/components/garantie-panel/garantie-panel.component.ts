import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    Output,
} from '@angular/core'
import { NgClass } from '@angular/common'
import { Garantie } from '../../models/garantie'

@Component({
    selector: 'garantie-panel',
    templateUrl: './garantie-panel.component.html',
    styleUrls: ['./garantie-panel.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgClass],
})
export class GarantiePanelComponent {
    @Input()
    garantie?: Garantie
    @Input()
    isDisabled = false

    @Output()
    readonly selectGarantie = new EventEmitter<Garantie>()

    onSelectGarantie(garantie: Garantie) {
        this.selectGarantie.emit(garantie)
    }
}
