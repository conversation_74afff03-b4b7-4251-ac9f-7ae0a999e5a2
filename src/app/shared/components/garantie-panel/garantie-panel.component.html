@if (garantie) {
    <div class="DataTile u-has-padding-16" [ngClass]="{ 'is-disabled u-has-cursor-auto': isDisabled }" (click)="!isDisabled && onSelectGarantie(garantie)">
        <div class="DataTile-content u-has-padding-0">
            <div class="DataTile-label u-is-txt-monospace" [ngClass]="{ 'u-is-txt-feather-grey-300': isDisabled }">
                {{ garantie.nom }}
            </div>
        </div>
        <ng-content></ng-content>
    </div>
}
