import {
    ChangeDetectionStrategy,
    Component,
    inject,
    Input,
    OnInit,
} from '@angular/core'
import { map, Observable } from 'rxjs'
import {
    ControlValueAccessor,
    FormControl,
    FormsModule,
    NG_VALUE_ACCESSOR,
    ReactiveFormsModule,
} from '@angular/forms'
import { SortCountryPipe } from '../../pipes/adresse/sort-country.pipe'
import { CodeLabel } from '../../models/code-label'
import { DataSource } from '../../data-sources/data.source'
import { AdresseService } from '../../services/adresse/adresse.service'
import { I18nPipe } from '@foyer/ng-i18n'
import { AutocompleteComponent, HighlightPipe } from '@foyer/ng-select'
import { AsyncPipe } from '@angular/common'

@Component({
    selector: 'country-select',
    templateUrl: './country-select.component.html',
    styleUrls: ['./country-select.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: CountrySelectComponent,
        },
    ],
    imports: [
        I18nPipe,
        HighlightPipe,
        FormsModule,
        AsyncPipe,
        AutocompleteComponent,
        ReactiveFormsModule,
    ],
})
export class CountrySelectComponent implements OnInit, ControlValueAccessor {
    private readonly adresseService = inject(AdresseService)
    private readonly sortCountryPipe = new SortCountryPipe()

    @Input()
    withAllOption = false
    @Input()
    formControl: FormControl<string | undefined> | undefined

    readonly TRANSLATION_PREFIX = 'common.country-select.'

    paysDataSource$!: Observable<DataSource>

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onChange = (country: string): void => {}
    onTouched = (): void => {}

    writeValue(obj: string): void {
        this.onChange(obj)
    }

    registerOnChange(onChange: (country: string) => void): void {
        this.onChange = onChange
    }

    registerOnTouched(onTouched: () => void): void {
        this.onTouched = onTouched
    }

    ngOnInit(): void {
        this.paysDataSource$ = this.buildPaysDataSource()
    }

    unsetPaysValue(event: MouseEvent): void {
        this.formControl?.reset()
        event.stopPropagation()
    }

    private buildPaysDataSource(): Observable<DataSource> {
        return this.adresseService.getPays().pipe(
            map(this.sortCountryPipe.transform),
            map((pays: CodeLabel[]) =>
                pays.map((paysItem) => ({
                    key: paysItem.code,
                    displayValue: paysItem.label,
                }))
            ),
            map(
                (items) =>
                    new DataSource(
                        this.withAllOption
                            ? [{ key: 'other', displayValue: 'Tous' }, ...items]
                            : items
                    )
            )
        )
    }
}
