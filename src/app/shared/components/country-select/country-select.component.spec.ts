import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CountrySelectComponent } from './country-select.component'
import { FormControl, NgControl } from '@angular/forms'
import { switchMap } from 'rxjs'
import { AdresseService } from '../../services/adresse/adresse.service'
import { AdresseServiceMock } from '../../services/adresse/adresse.service.spec.mock'

describe('CountrySelectComponent', () => {
    let component: CountrySelectComponent
    let fixture: ComponentFixture<CountrySelectComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [CountrySelectComponent],
            providers: [
                { provide: AdresseService, useClass: AdresseServiceMock },
                NgControl,
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(CountrySelectComponent)
        component = fixture.componentInstance
    })

    describe('unselectValue', () => {
        const mockEvent = new MouseEvent('click')

        beforeEach(() => {
            spyOn(mockEvent, 'stopPropagation')
        })

        it('if defined then unsetPays should call reset with the defined value', () => {
            fixture.detectChanges()
            component.formControl = new FormControl()
            spyOn(component.formControl, 'reset')
            expect(component).toBeTruthy()

            component.unsetPaysValue(mockEvent)
            expect(mockEvent.stopPropagation).toHaveBeenCalled()
        })

        it('if not defined then unsetPays should call reset with undefined', () => {
            fixture.detectChanges()
            component.formControl = new FormControl()
            spyOn(component.formControl, 'reset')
            expect(component).toBeTruthy()

            component.unsetPaysValue(mockEvent)

            expect(mockEvent.stopPropagation).toHaveBeenCalled()
        })
    })

    describe('withAllOption', () => {
        it('when true should show "Tous" as first option', (done) => {
            fixture.detectChanges()
            component.formControl = new FormControl()
            component.withAllOption = true

            const expectedResult = {
                displayValue: 'Tous',
                key: 'other',
            }

            component.paysDataSource$
                .pipe(switchMap((dataSource) => dataSource.data))
                .subscribe((result) => {
                    expect(result[0]).toEqual(expectedResult)
                    done()
                })
        })

        it('when false should not show "Tous" as an option', (done) => {
            component.formControl = new FormControl()
            component.withAllOption = false
            fixture.detectChanges()
            const expectedResult = {
                displayValue: 'Tous',
                key: 'other',
            }

            component.paysDataSource$
                .pipe(switchMap((dataSource) => dataSource.data))
                .subscribe((result) => {
                    expect(result).not.toContain(expectedResult)
                    done()
                })
        })
    })
})
