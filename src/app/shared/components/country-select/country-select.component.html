@if (formControl) {
    <div class="Form-field-input countrySelect u-is-full-width">
        @if (paysDataSource$ | async; as pays) {
            <lib-autocomplete
                class="u-is-full-width"
                [data-source]="pays"
                [debounce]="100"
                [no-result-template]="noResultTemplate"
                [select-item-template]="optionListItemTemplate"
                [i18n-search-input-placeholder]="TRANSLATION_PREFIX + 'rechercher-pays' | translate"
                [is-custom-option-inline]="true"
                [label-key]="'displayValue'"
                [value-key]="'key'"
                [formControl]="formControl"
                (blur)="onTouched()"></lib-autocomplete>
        }
        <ng-template #optionListItemTemplate let-pays="item" let-search="search">
            <div class="DataTile">
                <img
                    src="https://static.foyer.lu/images/static/flags/squared/{{ pays.key.toLowerCase() }}.svg"
                    alt="Country {{ pays.displayValue }}.label }} logo"
                    class="u-has-padding-right-16" />
                <div class="DataTile-content u-has-padding-0">
                    <div class="DataTile-value" [innerHTML]="pays.displayValue | highlight: search">{{ pays.displayValue }}</div>
                </div>
            </div>
        </ng-template>
        <ng-template #noResultTemplate>
            <div class="u-is-txt-center u-is-txt-16 object-not-found u-is-bg-grey-100 u-has-margin-bottom-16 u-has-margin-top-8">
                {{ 'common.aucun-resultat-trouve' | translate }}
            </div>
        </ng-template>
    </div>
}
