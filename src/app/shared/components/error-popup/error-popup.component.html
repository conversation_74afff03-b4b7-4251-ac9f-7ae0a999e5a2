@if (config; as popup) {
    <div aria-labelledby="Modal-Title" aria-modal="true" class="Modal-wrapper" role="modal">
        <div class="Modal is-medium" [class.is-showed]="isModalVisible">
            <div class="Panel">
                <div [ngClass]="popup.level ? 'Panel-header-' + popup.level : 'Panel-header'">
                    <i class="mi-report"></i>
                    @if (popup.title) {
                        <span>{{ popup.title | translate }}</span>
                    }
                </div>
                <div [innerHTML]="popup.content | translate" class="Panel-body u-is-txt-center"></div>
                <div class="Panel-footer is-aligned-center">
                    @if (popup.actionLabel) {
                        <button (click)="triggerHideAndClose()" [ngClass]="popup.level ? 'is-' + popup.level : ''" class="ButtonContained is-rounded">
                            {{ popup.actionLabel | translate }}
                        </button>
                    }
                </div>
            </div>
        </div>
        <div class="Modal-backdrop"></div>
    </div>
}
