import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
} from '@angular/core'
import { HideableModalComponent } from '../hideable-modal.component'
import { NgClass } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { ErrorMessage } from '../../models/error-message.model'

@Component({
    selector: 'popup',
    templateUrl: './error-popup.component.html',
    styleUrls: ['./error-popup.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgClass, I18nPipe],
})
export class ErrorPopupComponent extends HideableModalComponent<
    void,
    ErrorMessage
> {
    constructor(private readonly _changeDetectorRef: ChangeDetectorRef) {
        super(_changeDetectorRef)
    }

    triggerHideAndClose(): void {
        if (this.config.actionCallback) {
            this.config.actionCallback()
        }

        this.close()
    }

    close(): void {
        this.closedWithResult.emit()
    }
}
