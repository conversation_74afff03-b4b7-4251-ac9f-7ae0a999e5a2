import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ErrorPopupComponent } from './error-popup.component'
import { ErrorLevel } from '../../enums/error-type.enum'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { createPopupMessage } from '../../utils/error-display.utils'
import {
    checkTextContentForElement,
    expectElementToExist,
} from '../../utils/test.utils'

describe('ErrorPopupComponent', () => {
    let component: ErrorPopupComponent
    let fixture: ComponentFixture<ErrorPopupComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ErrorPopupComponent],
            providers: [provideTranslation()],
        }).compileComponents()
    })

    beforeEach(() => {
        fixture = TestBed.createComponent(ErrorPopupComponent)
        component = fixture.componentInstance
        component.config = createPopupMessage(
            'common.authentication.session-expired',
            'common.authentication.session-expired-message',
            ErrorLevel.DANGER,
            'common.authentication.session-expired-action'
        )
        fixture.detectChanges()
    })

    it('should create a panel with danger css class and bind message title and content', () => {
        expectElementToExist(fixture, '.Panel-header-danger')
        checkTextContentForElement(
            fixture,
            '.Panel-header-danger',
            'Session expirée'
        )
        checkTextContentForElement(
            fixture,
            '.Panel-body',
            "Désolé, votre session a expiré. Vous allez être rerédigé vers la page d'authentification. Si le problème persiste, merci de contacter le support applicatif au 3500."
        )
        checkTextContentForElement(
            fixture,
            '.ButtonContained',
            "Charger la page d'authentification"
        )
    })
})
