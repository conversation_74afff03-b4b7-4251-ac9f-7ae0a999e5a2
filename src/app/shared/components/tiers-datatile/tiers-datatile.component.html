<div class="DataTile-container">
    @if (tiers(); as tiers) {
        <div class="DataTile u-has-padding-16">
            <div class="DataTile-icon">
                <img [src]="DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE" alt="" />
            </div>
            <div class="DataTile-content u-has-padding-0">
                @if (tiers.tiersBlesse) {
                    <div class="Badge is-small is-light is-danger">
                        {{ 'common.blesse' | translate }}
                    </div>
                }

                <div class="DataTile-label u-has-margin-left-0">{{ tiers.prenom | titlecase }} {{ tiers.nom | uppercase }}</div>

                <div class="DataTile-value sub-value u-is-txt-monospace">
                    @if (tiers.numeroPlaque) {
                        <span class="numeroPlaque">{{ tiers.numeroPlaque }}</span>
                    }
                    @if (tiers.adresse) {
                        <span class="adresse" [innerHTML]="tiers.adresse | prettifyAdresse"></span>
                    }
                </div>
            </div>
            <ng-content select="[side]"></ng-content>
        </div>
    }
    <ng-content select="[below]"></ng-content>
</div>
