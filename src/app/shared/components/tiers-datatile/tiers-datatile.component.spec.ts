import { ComponentFixture, TestBed } from '@angular/core/testing'

import { TiersDatatileComponent } from './tiers-datatile.component'
import { multipleTiersMock, singleTiersMock } from '../../mocks/tiers.mock'
import {
    checkTextContentForElement,
    expectElementToExist,
    expectElementToNotExist,
} from '../../utils/test.utils'

describe('TiersDatatileComponent', () => {
    let fixture: ComponentFixture<TiersDatatileComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [TiersDatatileComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(TiersDatatileComponent)
    })

    it('should display correct info of tiers without tiersBlesse and adresse', () => {
        fixture.componentRef.setInput('tiers', singleTiersMock())
        fixture.detectChanges()

        expectElementToNotExist(fixture, '.Badge')

        checkTextContentForElement(
            fixture,
            '.DataTile-value .numeroPlaque',
            '123'
        )
        expectElementToNotExist(fixture, '.DataTile-value .adresse')

        checkTextContentForElement(
            fixture,
            '.DataTile-label',
            'Satoshi NAKAMOTO'
        )
    })

    it('should display correct info of tiers without tiersBlesse and adresse', () => {
        fixture.componentRef.setInput('tiers', multipleTiersMock()[1])
        fixture.detectChanges()

        expectElementToExist(fixture, '.Badge')

        checkTextContentForElement(
            fixture,
            '.DataTile-value .numeroPlaque',
            '123'
        )
        checkTextContentForElement(
            fixture,
            '.DataTile-value .adresse',
            'Metz, FR'
        )

        checkTextContentForElement(fixture, '.DataTile-label', 'Jean DUPONT')
    })
})
