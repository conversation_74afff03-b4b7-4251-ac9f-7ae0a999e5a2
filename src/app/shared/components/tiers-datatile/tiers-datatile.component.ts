import { ChangeDetectionStrategy, Component, input } from '@angular/core'
import { TitleCasePipe, UpperCasePipe } from '@angular/common'
import { Tiers } from '../../models/tiers'
import { DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE } from '../../utils/icone.utils'
import { PrettifyAdressePipe } from '../../pipes/adresse/prettify-adresse.pipe'
import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'tiers-datatile',
    standalone: true,
    imports: [UpperCasePipe, TitleCasePipe, PrettifyAdressePipe, I18nPipe],
    templateUrl: './tiers-datatile.component.html',
    styleUrl: './tiers-datatile.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TiersDatatileComponent {
    tiers = input.required<Tiers>()

    readonly TIERS_TRANSLATION_PREFIX: string = 'common.tiers.'

    protected readonly DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE =
        DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE
}
