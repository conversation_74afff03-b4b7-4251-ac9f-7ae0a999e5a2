import { WizardService } from '../services/wizard/wizard.service'
import { NotificationLevel } from '../enums/notification-level.enum'
import { WizardStep } from './wizard-step'
import { ModalService } from '../services/modal/modal.service'
import { ViewContainerHostDirective } from '../directives/view-container-host.directive'
import { ChangeDetectorRef, ElementRef, inject } from '@angular/core'
import { Router } from '@angular/router'
import { Observable, Subscription, switchMap, take } from 'rxjs'
import { Declaration } from '../models/declaration'
import { Risque } from '../models/risque'
import { RisquesService } from '../services/risques/risques.service'
import { Personne } from '../wrappers/personne-wrapper/models/personne/personne'
import { WizardSteps } from '../enums/wizard-steps.enum'
import { FormGroup } from '@angular/forms'
import { map } from 'rxjs/operators'
import { DeclarationService } from '../services/declaration/declaration.service'
import { scrollToFirstErrorInForm } from '../utils/error-display.utils'

export abstract class DeclarationWizardStep<
    TForm extends FormGroup | undefined,
    TDeclaration extends Declaration,
> extends WizardStep {
    private readonly elementRef: ElementRef = inject(ElementRef)

    protected abstract viewContainerHost: ViewContainerHostDirective
    protected abstract form?: TForm
    protected abstract declaration$?: Observable<TDeclaration>

    protected readonly subscriptions: Subscription = new Subscription()

    protected previousStep?: WizardSteps
    protected situationContratRisque$?: Observable<Risque>
    protected personne$?: Observable<Personne | undefined>

    protected constructor(
        protected override wizardService: WizardService,
        protected declarationService: DeclarationService,
        protected modalService: ModalService,
        protected changeDetectorRef: ChangeDetectorRef,
        protected router: Router,
        protected risquesService: RisquesService
    ) {
        super(wizardService)
    }

    protected abstract isFormValidInPage(): boolean

    protected abstract savePageContent(): void

    protected abstract setStepper(form: TForm): void

    protected initObs(): void {
        this.situationContratRisque$ = this.declaration$?.pipe(
            switchMap((declaration: Declaration) =>
                this.risquesService.getSituationContratRisque(
                    declaration.numeroRisque
                )
            )
        )
        this.personne$ = this.situationContratRisque$?.pipe(
            map((situation) => situation.preneur)
        )
    }

    protected initFormWithExistingData(): void {
        if (this.situationContratRisque$ && this.declaration$) {
            this.subscriptions.add(
                this.declaration$.pipe(take(1)).subscribe((declaration) => {
                    this.form?.patchValue(declaration)
                })
            )
        }
    }

    protected showNotification(inputs: {
        notificationLevel: NotificationLevel
        message: string
    }): void {
        this.modalService.showNotification(
            inputs,
            this.viewContainerHost.viewContainerRef
        )
    }

    protected async onNext(): Promise<boolean> {
        return this.isFormValidInPage()
            ? this.saveAndNavigate(this.currentWizardStep)
            : this.handleInvalidForm()
    }

    protected async onBack(): Promise<boolean> {
        return this.isFormValidInPage()
            ? this.saveAndNavigate(this.previousStep)
            : this.handleInvalidForm()
    }

    protected override onSecondary(): void {
        if (this.isFormValidInPage()) {
            this.enregistrerBrouillon()
        } else {
            this.handleInvalidForm()
        }
    }

    protected async navigateToStep(targetStep?: WizardSteps): Promise<boolean> {
        if (!targetStep || !this.declaration$) {
            return Promise.resolve(false)
        }

        try {
            const step = this.getNavigationStep(targetStep)
            await this.wizardService.navigateToStep(step, this.router)
            return true
        } catch {
            return false
        }
    }

    private getNavigationStep(targetStep: WizardSteps): WizardSteps {
        return targetStep === this.previousStep
            ? targetStep
            : this.wizardService.getNextStep(targetStep)
    }

    private async saveAndNavigate(targetStep?: WizardSteps): Promise<boolean> {
        this.savePageContent()
        return this.navigateToStep(targetStep)
    }

    private enregistrerBrouillon(): void {
        this.savePageContent()

        this.declarationService.upsertDeclaration().subscribe({
            next: () => {
                this.showNotification({
                    notificationLevel: NotificationLevel.SUCCESS,
                    message: `common.notification.save-declaration-success`,
                })
            },
            error: () => {
                this.showNotification({
                    notificationLevel: NotificationLevel.DANGER,
                    message: `common.notification.save-declaration-error`,
                })
            },
            complete: () => {
                this.initFormWithExistingData()
            },
        })
    }

    private handleInvalidForm(): Promise<boolean> {
        this.form?.markAllAsTouched()
        this.changeDetectorRef.detectChanges()
        scrollToFirstErrorInForm(this.elementRef)
        return Promise.resolve(false)
    }
}
