import { ComponentFixture, TestBed } from '@angular/core/testing'
import { provideTranslation } from '../../../../core/providers/translation.provider'
import {
    checkElementToContainClass,
    checkTextContentForElement,
} from 'src/app/shared/utils/test.utils'
import { ResumeTiersComponent } from './resume-tiers.component'
import { multipleTiersMock } from '../../../mocks/tiers.mock'

describe('ResumeTiersComponent', () => {
    let fixture: ComponentFixture<ResumeTiersComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ResumeTiersComponent],
            providers: [provideTranslation()],
        }).compileComponents()

        fixture = TestBed.createComponent(ResumeTiersComponent)
    })

    it('should display detail for tiers with sub-data-tile class', () => {
        fixture.componentRef.setInput('tiersList', multipleTiersMock())

        fixture.detectChanges()

        checkTextContentForElement(
            fixture,
            '.tiers-prenom-nom-data-tile .DataTile-label span',
            'Prénom NOM'
        )
        checkTextContentForElement(
            fixture,
            '.tiers-prenom-nom-data-tile .DataTile-value span',
            'Satoshi NAKAMOTO'
        )
        checkElementToContainClass(
            fixture,
            '.tiers-prenom-nom-data-tile .DataTile-content',
            'sub-data-tile'
        )

        checkTextContentForElement(
            fixture,
            '.tiers-numero-plaque-data-tile .DataTile-label span',
            'Numéro de plaque'
        )
        checkTextContentForElement(
            fixture,
            '.tiers-numero-plaque-data-tile .DataTile-value span',
            '123'
        )
        checkElementToContainClass(
            fixture,
            '.tiers-numero-plaque-data-tile .DataTile-content',
            'sub-data-tile'
        )

        checkTextContentForElement(
            fixture,
            '.tiers-compagnie-adverse-data-tile .DataTile-label span',
            'Compagnie adverse'
        )
        checkTextContentForElement(
            fixture,
            '.tiers-compagnie-adverse-data-tile .DataTile-value span',
            'AXA'
        )
        checkElementToContainClass(
            fixture,
            '.tiers-compagnie-adverse-data-tile .DataTile-content',
            'sub-data-tile'
        )

        checkTextContentForElement(
            fixture,
            '.tiers-contrat-data-tile .DataTile-label span',
            "Contrat d'assurance chez la compagnie adverse"
        )
        checkTextContentForElement(
            fixture,
            '.tiers-contrat-data-tile .DataTile-value span',
            'Contrat'
        )
        checkElementToContainClass(
            fixture,
            '.tiers-contrat-data-tile .DataTile-content',
            'sub-data-tile'
        )
    })

    it('should not display anything when tiersList is undefined or empty', () => {
        fixture.componentRef.setInput('tiersList', undefined)
        fixture.detectChanges()
        expect(fixture.nativeElement.querySelector('data-tile')).toBeNull()

        fixture.componentRef.setInput('tiersList', [])
        fixture.detectChanges()
        expect(fixture.nativeElement.querySelector('data-tile')).toBeNull()
    })

    it('should display multiple tiers with correct indexes and details', () => {
        fixture.componentRef.setInput('tiersList', multipleTiersMock())
        fixture.detectChanges()

        const tiersLabels =
            fixture.nativeElement.querySelectorAll('span.tiers-index')
        expect(tiersLabels.length).toBe(2)
        expect(tiersLabels[0].textContent).toContain('Tiers 1')
        expect(tiersLabels[1].textContent).toContain('Tiers 2')

        const nomDataTiles = fixture.nativeElement.querySelectorAll(
            '.tiers-prenom-nom-data-tile'
        )
        expect(nomDataTiles.length).toBe(2)
        expect(nomDataTiles[0].textContent).toContain('Satoshi NAKAMOTO')
        expect(nomDataTiles[1].textContent).toContain('Jean DUPONT')

        const tiersBlesseDataTiles = fixture.nativeElement.querySelectorAll(
            '.tiers-blesse-data-tile'
        )
        expect(tiersBlesseDataTiles.length).toBe(1)
        expect(tiersBlesseDataTiles[0].textContent).toContain('Oui')

        const tiersAdresseDataTiles = fixture.nativeElement.querySelectorAll(
            '.tiers-adresse-data-tile'
        )
        expect(tiersAdresseDataTiles.length).toBe(1)
        expect(tiersAdresseDataTiles[0].textContent).toContain('Metz, FR')
    })
})
