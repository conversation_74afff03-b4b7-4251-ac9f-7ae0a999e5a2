@if (hasNonNullValues(tiersList(), ['tiers.adresse'])) {
    <h5 class="u-has-margin-top-12 u-has-margin-bottom-4">
        <span>{{ TRANSLATION_PREFIX + 'liste-tiers' | translate }}</span>
    </h5>

    @for (tiers of tiersList(); track tiers; let i = $index) {
        <span class="u-is-txt-bold u-is-txt-14 tiers-index">{{ TRANSLATION_PREFIX + 'tiers' | translate: { index: i + 1 } }}</span>

        @if (tiers.nom || tiers.prenom) {
            <data-tile [isSubDataTile]="true" class="tiers-prenom-nom-data-tile">
                <span label>{{ TIERS_TRANSLATION_PREFIX + 'prenom' | translate }} {{ TIERS_TRANSLATION_PREFIX + 'nom' | translate | uppercase }}</span>
                <span value>{{ tiers.prenom | titlecase }} {{ tiers.nom | uppercase }}</span>
            </data-tile>
        }
        @if (tiers.numeroPlaque) {
            <data-tile [isSubDataTile]="true" class="tiers-numero-plaque-data-tile">
                <span label>{{ TIERS_TRANSLATION_PREFIX + 'numero-plaque' | translate }}</span>
                <span value>{{ tiers.numeroPlaque }}</span>
            </data-tile>
        }
        @if (tiers.marqueVehicule) {
            <data-tile [isSubDataTile]="true" class="tiers-marque-vehicule-data-tile">
                <span label>{{ TIERS_TRANSLATION_PREFIX + 'marque-vehicule' | translate }}</span>
                <span value>{{ tiers.marqueVehicule }}</span>
            </data-tile>
        }
        @if (tiers.compagnieAdverse) {
            <data-tile [isSubDataTile]="true" class="tiers-compagnie-adverse-data-tile">
                <span label>{{ TIERS_TRANSLATION_PREFIX + 'compagnie-adverse' | translate }}</span>
                <span value>{{ tiers.compagnieAdverse }}</span>
            </data-tile>
        }
        @if (tiers.numeroContratAssurance) {
            <data-tile [isSubDataTile]="true" class="tiers-numero-contrat-assurance-data-tile">
                <span label>{{ TIERS_TRANSLATION_PREFIX + 'numero-contrat-assurance' | translate }}</span>
                <span value>{{ tiers.compagnieAdverse }}</span>
            </data-tile>
        }
        @if (tiers.contratAssuranceChezCompagnieAdverse) {
            <data-tile [isSubDataTile]="true" class="tiers-contrat-data-tile">
                <span label>{{ TIERS_TRANSLATION_PREFIX + 'contrat-assurance-chez-compagnie-adverse' | translate }}</span>
                <span value>{{ tiers.contratAssuranceChezCompagnieAdverse }}</span>
            </data-tile>
        }
        @if (tiers.adresse) {
            <data-tile [isSubDataTile]="true" class="tiers-adresse-data-tile">
                <span label>{{ TRANSLATION_PREFIX + 'adresse' | translate }}</span>
                <span value>{{ tiers.adresse | prettifyAdresse }}</span>
            </data-tile>
        }
        @if (tiers.tiersBlesse) {
            <data-tile [isSubDataTile]="true" class="tiers-blesse-data-tile">
                <span label>{{ TIERS_TRANSLATION_PREFIX + 'tiers-blesse' | translate }}</span>
                <span value>{{ 'enums.oui-non-inconnu.' + tiers.tiersBlesse | translate }}</span>
            </data-tile>
        }
    }
}
