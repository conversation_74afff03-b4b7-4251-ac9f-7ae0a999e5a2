import { ChangeDetectionStrategy, Component, input } from '@angular/core'

import { I18nPipe } from '@foyer/ng-i18n'
import { Tiers } from '../../../models/tiers'
import { DataTileComponent } from '../../data-tile/data-tile.component'
import { hasNonNullValues } from '../../../utils/objects.utils'
import { TitleCasePipe, UpperCasePipe } from '@angular/common'
import { PrettifyAdressePipe } from '../../../pipes/adresse/prettify-adresse.pipe'

@Component({
    selector: 'resume-tiers',
    standalone: true,
    imports: [
        I18nPipe,
        DataTileComponent,
        UpperCasePipe,
        TitleCasePipe,
        PrettifyAdressePipe,
    ],
    templateUrl: './resume-tiers.component.html',
    styleUrl: './resume-tiers.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResumeTiersComponent {
    tiersList = input<Tiers[] | undefined>()

    readonly TRANSLATION_PREFIX: string = 'common.resume-tiers.'
    readonly TIERS_TRANSLATION_PREFIX: string = 'common.tiers.'

    protected readonly hasNonNullValues = hasNonNullValues
}
