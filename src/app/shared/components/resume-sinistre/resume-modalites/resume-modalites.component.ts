import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { DataTileComponent } from '../../data-tile/data-tile.component'
import { I18nPipe } from '@foyer/ng-i18n'
import { PercentPipe } from '@angular/common'
import { ModalitesAuto } from '../../../../auto/models/modalites-auto'

@Component({
    selector: 'resume-modalites',
    standalone: true,
    imports: [DataTileComponent, I18nPipe, PercentPipe],
    templateUrl: './resume-modalites.component.html',
    styleUrl: './resume-modalites.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResumeModalitesComponent {
    @Input()
    modalites?: ModalitesAuto

    readonly TRANSLATION_PREFIX: string = 'common.modalites.'
}
