<h5 class="u-has-margin-bottom-4">{{ 'common.modalites.title' | translate }}</h5>

@if (modalites) {
    @if (modalites.preneurSoumisTva === true || modalites.preneurSoumisTva === false) {
        <data-tile class="preneur-soumis-tva-data-tile">
            <span label>{{ 'common.modalites.preneur-soumis-tva' | translate }}</span>
            <span value>
                {{ 'common.' + modalites.preneurSoumisTva | translate }}
            </span>
        </data-tile>
    }
    @if (modalites.preneurSoumisTva && modalites.pourcentageTvaRecupere) {
        <data-tile class="pourcentage-tva-recupere-data-tile">
            <span label>{{ 'common.modalites.pourcentage-tva-recupere' | translate }}</span>
            <span value>
                {{ modalites.pourcentageTvaRecupere / 100 | percent }}
            </span>
        </data-tile>
    }
    @if (modalites.tva6Pourcent === true || modalites.tva6Pourcent === false) {
        <data-tile class="tva-6-pourcent-data-tile">
            <span label>{{ 'common.modalites.tva-6-pourcent' | translate }}</span>
            <span value>
                {{ 'common.' + modalites.tva6Pourcent | translate }}
            </span>
        </data-tile>
    }
    @if (modalites.compteBancaire) {
        <data-tile class="compte-bancaire-data-tile">
            <span label>{{ 'common.modalites.compte-bancaire' | translate }}</span>
            <span value>
                {{ modalites.compteBancaire }}
            </span>
        </data-tile>
    }
}
