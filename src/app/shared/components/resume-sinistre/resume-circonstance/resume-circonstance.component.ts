import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { DataTileComponent } from '../../data-tile/data-tile.component'
import { I18nPipe } from '@foyer/ng-i18n'

import { CirconstanceSinistre } from '../../../models/circonstance-sinistre'

@Component({
    selector: 'resume-circonstance',
    standalone: true,
    imports: [DataTileComponent, I18nPipe],
    templateUrl: './resume-circonstance.component.html',
    styleUrl: './resume-circonstance.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResumeCirconstanceComponent {
    @Input()
    circonstance?: CirconstanceSinistre

    readonly TRANSLATION_PREFIX: string = 'common.resume-circonstance.'
}
