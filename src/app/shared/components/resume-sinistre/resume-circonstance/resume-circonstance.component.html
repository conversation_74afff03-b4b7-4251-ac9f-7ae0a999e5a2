@if (circonstance) {
    @if (circonstance.circonstance) {
        <data-tile class="circonstance-data-tile">
            <span label>{{ 'common.circonstance-sinistre' | translate }}</span>
            <span value>
                {{ 'enums.circonstance-key.' + circonstance.circonstance | translate }}
            </span>
        </data-tile>
    }
    @if (circonstance.complementDeCirconstance) {
        <data-tile class="complement-circonstance-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'presence-informations-complementaires' | translate }}</span>
            <span value>
                {{ (circonstance.complementDeCirconstance.length > 0 ? 'common.oui' : 'common.non') | translate }}
            </span>
        </data-tile>
    }
}
