import { ComponentFixture, TestBed } from '@angular/core/testing'

import { ResumeCirconstanceComponent } from './resume-circonstance.component'

describe('ResumeCirconstanceComponent', () => {
    let component: ResumeCirconstanceComponent
    let fixture: ComponentFixture<ResumeCirconstanceComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ResumeCirconstanceComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(ResumeCirconstanceComponent)
        component = fixture.componentInstance
        fixture.detectChanges()
    })

    it('should create', () => {
        expect(component).toBeTruthy()
    })
})
