import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ResumePiecesJointesComponent } from './resume-pieces-jointes.component'
import { provideTranslation } from '../../../../core/providers/translation.provider'
import { checkTextContentForElement } from '../../../utils/test.utils'

describe('ResumePiecesJointesComponent', () => {
    let component: ResumePiecesJointesComponent
    let fixture: ComponentFixture<ResumePiecesJointesComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ResumePiecesJointesComponent],
            providers: [provideTranslation()],
        }).compileComponents()

        fixture = TestBed.createComponent(ResumePiecesJointesComponent)
        component = fixture.componentInstance
        component.piecesJointes = ['first', 'second']
    })

    it('should display the data tile with correct number of pieces jointes (2)', () => {
        fixture.detectChanges()
        checkTextContentForElement(
            fixture,
            '.pieces-jointes-data-tile .DataTile-label span',
            'Nombre de pièces jointes'
        )
        checkTextContentForElement(
            fixture,
            '.pieces-jointes-data-tile .DataTile-value span',
            '2'
        )
    })
})
