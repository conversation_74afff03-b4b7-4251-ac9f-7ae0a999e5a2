import { ChangeDetectionStrategy, Component, Input } from '@angular/core'

import { DataTileComponent } from '../../data-tile/data-tile.component'

import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'resume-pieces-jointes',
    templateUrl: './resume-pieces-jointes.component.html',
    styleUrls: ['./resume-pieces-jointes.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [DataTileComponent, I18nPipe],
})
export class ResumePiecesJointesComponent {
    @Input()
    piecesJointes?: string[]

    protected readonly TRANSLATION_PREFIX: string =
        'common.resume-pieces-jointes.'
}
