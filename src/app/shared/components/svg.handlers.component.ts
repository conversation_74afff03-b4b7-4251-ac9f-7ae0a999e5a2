import { ChangeDetectorRef } from '@angular/core'
import { ControlValueAccessor } from '@angular/forms'

export class SvgHandlersComponent<T> implements ControlValueAccessor {
    selectedZones: string[] = []
    hoverKey?: string
    zones: string[]
    hoverPosition = { x: 0, y: 20 }

    constructor(
        protected readonly changeDetectorRef: ChangeDetectorRef,
        zones: string[]
    ) {
        this.zones = zones
    }

    isSelected(key: string): boolean {
        return this.selectedZones.includes(key)
    }

    isHovering(key: string): boolean {
        return key === this.hoverKey
    }

    isNeither(key: string): boolean {
        return !this.isSelected(key) && !this.isHovering(key)
    }

    onSelection(key: string): void {
        this.selectedZones = this.isSelected(key)
            ? this.selectedZones.filter((item) => item !== key)
            : [...this.selectedZones, key]
        this.afterSelection()
    }

    onMouseLeaveArea(): void {
        this.hoverKey = undefined
    }

    onHover(key: string, isLeft: boolean = true, isTop: boolean = true): void {
        this.hoverKey = key
        this.hoverPosition = { x: isLeft ? 0 : 330, y: isTop ? 20 : 655 }
        this.changeDetectorRef.markForCheck()
    }

    afterSelection(): void {
        this.onTouched()
        this.onChange(this.selectedZones as T[])
    }

    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    onChange = (elements: T[]) => {}

    onTouched = () => {}

    registerOnChange(onChange: (elements: T[]) => void): void {
        this.onChange = onChange
    }

    registerOnTouched(onTouched: () => void): void {
        this.onTouched = onTouched
    }

    writeValue(zones: T[]): void {
        const zoneKeys = Object.values<string>(zones as string[])
        this.selectedZones = this.zones.filter((zone) =>
            zoneKeys.includes(zone)
        )
        this.changeDetectorRef.markForCheck()
    }
}
