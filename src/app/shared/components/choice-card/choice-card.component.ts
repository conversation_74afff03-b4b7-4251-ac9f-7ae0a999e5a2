import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    Output,
} from '@angular/core'
import { NgClass } from '@angular/common'

@Component({
    selector: 'choice-card',
    templateUrl: './choice-card.component.html',
    styleUrls: ['./choice-card.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgClass],
})
export class ChoiceCardComponent<T> {
    @Input()
    value!: T
    @Input()
    selected = false
    @Input()
    withMultiChoice = true

    @Output()
    selectionChange = new EventEmitter<T>()

    onClick() {
        this.selectionChange.emit(this.value)
    }
}
