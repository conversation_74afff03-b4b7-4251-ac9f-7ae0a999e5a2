import { ComponentFixture, TestBed } from '@angular/core/testing'

import { ChoiceCardComponent } from './choice-card.component'
import { By } from '@angular/platform-browser'

class TestComponent {}

describe('ChoiceCardComponent', () => {
    let component: ChoiceCardComponent<TestComponent>
    let fixture: ComponentFixture<ChoiceCardComponent<TestComponent>>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ChoiceCardComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(ChoiceCardComponent<TestComponent>)
        component = fixture.componentInstance
    })

    it('should render input type checkbox when withMultiChoice is true', () => {
        component.withMultiChoice = true
        fixture.detectChanges()
        const input = fixture.debugElement.query(By.css('input'))
        expect(input.attributes['type']).toBe('checkbox')
    })

    it('should render input type radio when withMultiChoice is false', () => {
        component.withMultiChoice = false
        fixture.detectChanges()
        const input = fixture.debugElement.query(By.css('input'))
        expect(input.attributes['type']).toBe('radio')
    })

    it('should have input unchecked when selected is false', () => {
        component.selected = false
        fixture.detectChanges()
        const input = fixture.debugElement.query(By.css('input'))
            .nativeElement as HTMLInputElement
        expect(input.checked).toBeFalsy()
    })

    it('should have input checked when selected is true', () => {
        component.selected = true
        fixture.detectChanges()
        const input = fixture.debugElement.query(By.css('input'))
            .nativeElement as HTMLInputElement
        expect(input.checked).toBeTruthy()
    })

    it('should emit selectionChange event with value when clicked', () => {
        component.value = 'test-value'

        spyOn(component.selectionChange, 'emit').and.callThrough()

        fixture.detectChanges()

        const wrapper = fixture.debugElement.query(
            By.css('.ChoiceCard-wrapper')
        )
        wrapper.triggerEventHandler('click', null)

        expect(component.selectionChange.emit).toHaveBeenCalledWith(
            'test-value'
        )
    })
})
