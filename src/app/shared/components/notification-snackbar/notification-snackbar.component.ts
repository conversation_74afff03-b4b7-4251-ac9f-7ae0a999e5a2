import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
} from '@angular/core'
import { NotificationLevel } from '../../enums/notification-level.enum'
import { HideableModalComponent } from '../hideable-modal.component'

import { I18nPipe } from '@foyer/ng-i18n'
import { NotificationMessage } from '../../models/notification.model'

@Component({
    selector: 'notification-snackbar',
    templateUrl: './notification-snackbar.component.html',
    styleUrls: ['./notification-snackbar.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [I18nPipe],
})
export class NotificationSnackbarComponent extends HideableModalComponent<
    void,
    NotificationMessage
> {
    NotificationLevel = NotificationLevel

    notifcationLevel!: NotificationLevel

    message!: string

    constructor(private readonly _changeDetectorRef: ChangeDetectorRef) {
        super(_changeDetectorRef)
    }

    override onInjectInputs(inputs: NotificationMessage): void {
        const FIVE_SECONDS_TIMEOUT = 5000
        this.notifcationLevel = inputs.notificationLevel
        this.message = inputs.message
        this._changeDetectorRef.markForCheck()

        setTimeout(() => {
            this.close()
        }, FIVE_SECONDS_TIMEOUT)
    }

    close(): void {
        this.closedWithResult.emit()
    }
}
