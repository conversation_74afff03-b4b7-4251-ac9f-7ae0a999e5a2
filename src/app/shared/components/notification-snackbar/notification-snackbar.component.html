<div
    class="Snackbar"
    data-testid="notificationSnackbar"
    [class.is-primary]="notifcationLevel === NotificationLevel.INFO"
    [class.is-success]="notifcationLevel === NotificationLevel.SUCCESS"
    [class.is-warning]="notifcationLevel === NotificationLevel.WARNING"
    [class.is-danger]="notifcationLevel === NotificationLevel.DANGER">
    <div class="Snackbar-icon">
        @if (notifcationLevel === NotificationLevel.SUCCESS) {
            <i class="mi-check_circle"></i>
        }
        @if (notifcationLevel === NotificationLevel.WARNING) {
            <i class="mi-warning"></i>
        }
        @if (notifcationLevel === NotificationLevel.DANGER) {
            <i class="mi-report"></i>
        }
        @if (notifcationLevel === NotificationLevel.INFO) {
            <i class="mi-info_square"></i>
        }
    </div>
    <div class="Snackbar-content">
        {{ message | translate }}
    </div>
    <button class="Snackbar-dismiss" (click)="close()"></button>
    <div class="Snackbar-progress"></div>
</div>
