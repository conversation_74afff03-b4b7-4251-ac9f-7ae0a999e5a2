import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { Personne } from '../../wrappers/personne-wrapper/models/personne/personne'
import { PersonneToAgePipe } from '../../pipes/personne/personne-to-age.pipe'
import { PersonneDateNaissancePipe } from '../../pipes/personne/personne-date-naissance.pipe'
import { PersonneNamePipe } from '../../pipes/personne/personne-name.pipe'
import { AsyncPipe, NgStyle, TitleCasePipe } from '@angular/common'

@Component({
    selector: 'person-datatile[personne]',
    templateUrl: './person-datatile.component.html',
    styleUrls: ['./person-datatile.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        NgStyle,
        AsyncPipe,
        PersonneNamePipe,
        PersonneDateNaissancePipe,
        PersonneToAgePipe,
    ],
    providers: [TitleCasePipe],
})
export class PersonDatatileComponent {
    @Input()
    personne!: Personne
    @Input()
    isAnnule = false
}
