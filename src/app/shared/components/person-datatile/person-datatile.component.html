<div class="DataTile-container" [ngStyle]="isAnnule ? { background: 'unset' } : null">
    @if (personne) {
        <div class="DataTile u-has-padding-16">
            <div class="DataTile-icon">
                <img [src]="personne.avatar" alt="" />
            </div>
            <div class="DataTile-content u-has-padding-0">
                <ng-content select="[top]"></ng-content>
                <div class="DataTile-label is-aligned-end u-has-margin-left-0">
                    {{ personne | personneName }}
                </div>
                <div class="DataTile-value sub-value">
                    <span>{{ personne | personneToAge | async }}</span>
                    {{ personne | personneDateNaissance | async }}
                </div>
            </div>
            <ng-content select="[side]"></ng-content>
        </div>
    }
    <ng-content select="[below]"></ng-content>
</div>
