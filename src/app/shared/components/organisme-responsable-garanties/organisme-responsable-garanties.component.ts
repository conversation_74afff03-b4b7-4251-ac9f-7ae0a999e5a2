import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { OrganismeResponsableGaranties } from '../../enums/organisme-responsable-garanties.enum'
import { GarantiePanelComponent } from '../garantie-panel/garantie-panel.component'
import { MessageBannerComponent } from '../message-banner/message-banner.component'

import { I18nPipe } from '@foyer/ng-i18n'
import { Garantie } from '../../models/garantie'
import { trackByGarantieCode } from '../../utils/garantie.utils'

@Component({
    selector: 'organisme-responsable-garanties',
    templateUrl: './organisme-responsable-garanties.component.html',
    styleUrls: ['./organisme-responsable-garanties.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [MessageBannerComponent, GarantiePanelComponent, I18nPipe],
})
export class OrganismeResponsableGarantiesComponent {
    @Input()
    garanties!: Garantie[]
    @Input()
    organismeResponsableGaranties!: OrganismeResponsableGaranties

    readonly TRANSLATION_PREFIX =
        'pages.garanties.garanties-group.organisme-responsable-garanties.'
    readonly trackByGarantieCode = trackByGarantieCode
}
