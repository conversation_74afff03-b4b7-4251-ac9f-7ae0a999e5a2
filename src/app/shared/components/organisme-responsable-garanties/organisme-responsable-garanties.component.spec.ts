import { ComponentFixture, TestBed } from '@angular/core/testing'
import { OrganismeResponsableGarantiesComponent } from './organisme-responsable-garanties.component'
import { OrganismeResponsableGaranties } from '../../enums/organisme-responsable-garanties.enum'
import { garantiesMock } from '../../mocks/garanties.mock'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { checkTextContentForElement } from '../../utils/test.utils'
import { Garantie } from '../../models/garantie'

describe('OrganismeResponsableGarantiesComponent', () => {
    let component: OrganismeResponsableGarantiesComponent
    let fixture: ComponentFixture<OrganismeResponsableGarantiesComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [OrganismeResponsableGarantiesComponent],
            providers: [provideTranslation()],
        }).compileComponents()

        fixture = TestBed.createComponent(
            OrganismeResponsableGarantiesComponent
        )
        component = fixture.componentInstance
        component.organismeResponsableGaranties =
            OrganismeResponsableGaranties.ARAG_BELGIUM
        component.garanties = garantiesMock()
        fixture.detectChanges()
    })

    it('should render correct title and details', () => {
        const element = fixture.nativeElement
        const garantiePanels = element.querySelectorAll('garantie-panel')

        checkTextContentForElement(
            fixture,
            '.organisme-responsable-label',
            'ARAG Belgium'
        )

        checkTextContentForElement(
            fixture,
            '.coordonnees-organisme-responsable',
            'Adresse e-mail: <EMAIL>. Téléphone : +32 2 643 12 06'
        )

        expect(garantiePanels.length).toBe(5)
    })

    it('should return the item code as the trackBy value', () => {
        const index = 0
        const item: Garantie = garantiesMock()[0]
        const result = component.trackByGarantieCode(index, item)
        expect(result).toBe(item.code)
    })
})
