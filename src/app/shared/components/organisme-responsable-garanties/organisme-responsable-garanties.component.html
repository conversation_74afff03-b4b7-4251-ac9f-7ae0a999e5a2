@if (garanties.length > 0) {
    <div class="Form-field is-medium">
        <label class="organisme-responsable-label Form-field-label">
            {{ TRANSLATION_PREFIX + organismeResponsableGaranties + '.title' | translate }}
        </label>
        <message-banner fdsColorClass="is-primary">
            <div class="u-is-txt-14">
                <span>
                    {{ TRANSLATION_PREFIX + (garanties.length === 1 ? 'la-garantie-geree-par' : 'les-garanties-gerees-par') | translate
                    }}{{ TRANSLATION_PREFIX + organismeResponsableGaranties + '.title' | translate }}.
                </span>
                <span
                    class="coordonnees-organisme-responsable"
                    [innerHTML]="TRANSLATION_PREFIX + organismeResponsableGaranties + '.coordonnees' | translate"></span>
            </div>
        </message-banner>
        <div class="Form-field-input">
            <div class="Panel u-has-margin-0 u-is-full-width u-has-margin-bottom-16">
                @for (garantie of garanties; track trackByGarantieCode($index, garantie); let last = $last) {
                    <garantie-panel [garantie]="garantie"></garantie-panel>
                    @if (!last) {
                        <hr class="Panel-separator u-has-margin-0" />
                    }
                }
            </div>
        </div>
    </div>
}
