import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    <PERSON><PERSON><PERSON><PERSON>,
    inject,
    Input,
    OnInit,
} from '@angular/core'
import { map, Observable } from 'rxjs'
import { ParametresService } from '../../services/parametres/parametres.service'
import { CirconstancesForm } from './forms/circonstances-form'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { CirconstancesFormStructure } from './forms/circonstances-form-structure'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { CaracteresRestantsComponent } from '../caracteres-restants/caracteres-restants.component'
import { MessageBannerComponent } from '../message-banner/message-banner.component'
import { AsyncPipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { AutocompleteComponent } from '@foyer/ng-select'
import { toDataSource } from '../../utils/data-source.utils'
import { DataSource } from '../../data-sources/data.source'
import { TypeCirconstance } from '../../enums/type-circonstance.enum'

@Component({
    selector: 'circonstances',
    templateUrl: './circonstances.component.html',
    styleUrls: ['./circonstances.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ReactiveFormsModule,
        MessageBannerComponent,
        CaracteresRestantsComponent,
        AsyncPipe,
        I18nPipe,
        AutocompleteComponent,
    ],
})
export class CirconstancesComponent implements OnInit, DoCheck {
    private readonly parametresService: ParametresService =
        inject(ParametresService)
    private readonly changeDetectorRef: ChangeDetectorRef =
        inject(ChangeDetectorRef)

    @Input()
    form?: CirconstancesForm
    @Input()
    typeGaranties?: TypeGaranties[]
    @Input()
    typeCirconstance: TypeCirconstance = TypeCirconstance.DAB

    readonly TRANSLATION_PREFIX: string = 'common.circonstances-du-sinistres.'

    circonstancesDataSource$!: Observable<DataSource>

    asFormGroup(
        form: CirconstancesForm
    ): FormGroup<CirconstancesFormStructure> {
        return form as unknown as FormGroup<CirconstancesFormStructure>
    }

    ngOnInit(): void {
        this.parametresService.setCirconstances(
            this.typeGaranties,
            this.typeCirconstance
        )
        this.circonstancesDataSource$ = this.parametresService
            .getCirconstancesDataSource()
            .pipe(map(toDataSource))
    }

    ngDoCheck(): void {
        this.changeDetectorRef.markForCheck()
    }

    removeCirconstance(): void {
        this.form?.controls.circonstance.setValue(undefined)
        this.form?.controls.circonstance.markAsDirty()
    }
}
