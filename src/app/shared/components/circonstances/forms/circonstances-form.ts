import { FormControl, FormGroup, Validators } from '@angular/forms'
import { CirconstancesFormStructure } from './circonstances-form-structure'
import { CirconstanceSinistre } from '../../../models/circonstance-sinistre'

const COMPLEMENT_DE_CIRCONSTANCE_MAX_LENGTH = 500

export class CirconstancesForm extends FormGroup<CirconstancesFormStructure> {
    constructor() {
        super({
            circonstance: new FormControl(undefined, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            complementDeCirconstance: new FormControl(undefined, {
                nonNullable: true,
            }),
        })
    }

    get complementDeCirconstanceMaxLength(): number {
        return COMPLEMENT_DE_CIRCONSTANCE_MAX_LENGTH
    }

    getValue(): CirconstanceSinistre {
        return this.value as CirconstanceSinistre
    }
}
