<div class="Panel u-is-txt-monospace">
    @if (form) {
        <form [formGroup]="asFormGroup(form)">
            <div class="Panel-header has-small-title">
                <span>{{ TRANSLATION_PREFIX + 'title' | translate }}</span>
            </div>
            <div class="Panel-body u-has-padding-24">
                <div class="GridFlex-row Form-row">
                    <div class="GridFlex-col-12">
                        <div
                            class="Form-field is-medium"
                            [class.is-danger]="(form.controls.circonstance.touched || form.controls.circonstance.dirty) && form.controls.circonstance.invalid">
                            <label for="circonstance" class="Form-field-label">
                                {{ TRANSLATION_PREFIX + 'circonstance-principale' | translate }}
                            </label>
                            @if (circonstancesDataSource$ | async; as circonstancesDataSource) {
                                <div class="Form-field-input">
                                    <lib-autocomplete
                                        class="u-is-full-width"
                                        [data-source]="circonstancesDataSource"
                                        [debounce]="100"
                                        [i18n-search-input-placeholder]="''"
                                        [label-key]="'displayValue'"
                                        [value-key]="'key'"
                                        [i18n-no-result-label]="'common.aucun-resultat-trouve' | translate"
                                        [select-first-option]="true"
                                        id="circonstance"
                                        formControlName="circonstance"></lib-autocomplete>
                                    @if (form.controls.circonstance.valid) {
                                        <i class="mi-close u-has-cursor-pointer" (click)="removeCirconstance()"></i>
                                    }
                                </div>
                            }
                        </div>
                        @if ((form.controls.circonstance.touched || form.controls.circonstance.dirty) && form.controls.circonstance.invalid) {
                            <message-banner>
                                @if (form.controls.circonstance.errors && form.controls.circonstance.errors['required']) {
                                    <span>
                                        {{ TRANSLATION_PREFIX + 'circonstance-is-required' | translate }}
                                    </span>
                                }
                            </message-banner>
                        }
                    </div>
                </div>
                <div class="GridFlex-row Form-row">
                    <div class="GridFlex-col-12">
                        <div class="Form-field is-medium">
                            <label for="complementDeCirconstance" class="Form-field-label">
                                {{ TRANSLATION_PREFIX + 'informations-complementaires-concernant-le-sinistre' | translate }}
                                <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                            </label>
                            <div class="Form-field-input">
                                <textarea
                                    id="complementDeCirconstance"
                                    class="Textarea"
                                    placeholder="{{ TRANSLATION_PREFIX + 'informations-complementaires-concernant-le-sinistre-placeholder' | translate }}"
                                    formControlName="complementDeCirconstance"
                                    [maxlength]="form.complementDeCirconstanceMaxLength"></textarea>
                            </div>
                            <caracteres-restants
                                [formControlLength]="form.controls.complementDeCirconstance.value?.length || 0"
                                [maxLength]="form.complementDeCirconstanceMaxLength"></caracteres-restants>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    }
</div>
