import { ComponentFixture, TestBed } from '@angular/core/testing'

import { CirconstancesComponent } from './circonstances.component'
import { ParametresService } from '../../services/parametres/parametres.service'
import { ParametresServiceMock } from '../../services/parametres/parametres.service.spec.mock'
import { CirconstancesForm } from './forms/circonstances-form'
import { circonstancesMock } from '../../mocks/circonstances.mock'

describe('CirconstancesComponent', () => {
    let component: CirconstancesComponent
    let fixture: ComponentFixture<CirconstancesComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [CirconstancesComponent],
            providers: [
                {
                    provide: ParametresService,
                    useClass: ParametresServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(CirconstancesComponent)
        component = fixture.componentInstance
        component.form = new CirconstancesForm()
        fixture.detectChanges()
    })

    describe('resetCirconstanceValue method', () => {
        it('should remove the circonstance value and mark field as dirty', () => {
            component.form?.controls.circonstance.setValue(
                circonstancesMock()[0].key
            )
            component.form?.controls.circonstance.markAsTouched()
            component.removeCirconstance()
            fixture.detectChanges()
            expect(component.form?.controls.circonstance.value).toBeUndefined()
            expect(component.form?.controls.circonstance.dirty).toBeTruthy()
        })
    })

    describe('testing around circonstance', () => {
        it('is invalid when circonstance is missing', () => {
            component.form?.controls.circonstance.setValue(undefined)
            component.form?.controls.circonstance.markAsTouched()
            fixture.detectChanges()
            expect(component.form?.valid).toBeFalsy()
            expect(
                component.form?.controls.circonstance.errors &&
                    component.form?.controls.circonstance.errors['required']
            ).toBeTruthy()
        })
    })
})
