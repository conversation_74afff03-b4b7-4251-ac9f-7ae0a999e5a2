import {
    AfterViewInit,
    ChangeDetectionStrategy,
    Component,
    ElementRef,
    Input,
    Renderer2,
    ViewChild,
} from '@angular/core'
import { NgClass } from '@angular/common'

@Component({
    selector: 'data-tile',
    templateUrl: './data-tile.component.html',
    styleUrls: ['./data-tile.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [NgClass],
})
export class DataTileComponent implements AfterViewInit {
    @Input()
    isTxtTruncated = true
    @Input()
    isSubDataTile = false

    @ViewChild('labelElement', { read: ElementRef })
    labelElement!: ElementRef
    @ViewChild('valueElement', { read: ElementRef })
    valueElement!: ElementRef

    constructor(private readonly renderer: Renderer2) {}

    ngAfterViewInit() {
        const labelWidth = this.labelElement.nativeElement.offsetWidth
        const valueMaxWidth = `calc(90% - ${labelWidth}px)`
        this.renderer.setStyle(
            this.valueElement.nativeElement,
            'max-width',
            valueMaxWidth
        )
    }
}
