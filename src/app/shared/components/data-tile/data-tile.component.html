<div class="DataTile is-inline">
    <div class="DataTile-content" [ngClass]="{ 'sub-data-tile': isSubDataTile }">
        <div class="DataTile-label" #labelElement>
            <ng-content select="[label]"></ng-content>
        </div>
        <div class="DataTile-value" [ngClass]="{ 'u-is-txt-truncated': isTxtTruncated }" #valueElement>
            <ng-content select="[value]"></ng-content>
        </div>
    </div>
</div>
