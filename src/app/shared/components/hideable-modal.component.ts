import { ChangeDetectorRef, EventEmitter } from '@angular/core'
import { LoadingComponent } from './loading.component'

export abstract class HideableModalComponent<R, S> extends LoadingComponent {
    readonly closedWithResult: EventEmitter<R> = new EventEmitter<R>()
    readonly closed: EventEmitter<void> = new EventEmitter<void>()
    readonly modalHidden: EventEmitter<void> = new EventEmitter<void>()

    config!: S

    private isVisible = false

    protected constructor(
        protected readonly changeDetectorRef: ChangeDetectorRef
    ) {
        super(changeDetectorRef)
    }

    get isModalVisible(): boolean {
        return this.isVisible
    }

    private set isModalVisible(isVisible: boolean) {
        this.isVisible = isVisible
        this.cdRef.markForCheck()
    }

    onInjectInputs(inputs: S): void {
        this.config = inputs
    }

    show(): void {
        setTimeout(() => {
            this.isModalVisible = true
        }, 300)
    }

    onCancelClicked(): void {
        this.closed.emit()
    }

    hide(): void {
        this.isModalVisible = false
        this.modalHidden.emit()
    }
}
