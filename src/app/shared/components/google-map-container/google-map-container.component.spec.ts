import { ComponentFixture, TestBed } from '@angular/core/testing'

import { GoogleMapContainerComponent } from './google-map-container.component'
import {
    GoogleMap,
    MapAdvancedMarker,
    MapCircle,
    MapInfoWindow,
} from '@angular/google-maps'
import {
    FakeGoogleMapComponent,
    FakeMapAdvancedMarkerComponent,
    FakeMapCircleComponent,
    FakeMapInfoWindowComponent,
} from '../../mocks/google-map.mock'
import { RechercheGaragesForm } from '../../../auto/components/garage/forms/recherche-garages.form'
import { Marker } from '../../models/marker'
import { MAP_MARKER, MAP_MARKER_ACTIVE } from '../../utils/icone.utils'
import { localiteSearchResultItemMock } from '../../data-sources/recherche-garages/localite-or-garage-data-source.service.mock'
import { DistanceKm } from '../../enums/distance-km.enum'

describe('GoogleMapContainerComponent', () => {
    let component: GoogleMapContainerComponent
    let fixture: ComponentFixture<GoogleMapContainerComponent>
    const markers: Marker[] = [
        {
            id: '1',
            isActive: false,
            nom: 'Chez Lucien',
            lat: 10,
            lng: 20,
        },
        {
            id: '2',
            isActive: true,
            nom: 'Repar-touf',
            lat: 30,
            lng: 40,
        },
    ]

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [GoogleMapContainerComponent],
        })
            .overrideComponent(GoogleMapContainerComponent, {
                remove: {
                    imports: [
                        GoogleMap,
                        MapAdvancedMarker,
                        MapInfoWindow,
                        MapCircle,
                    ],
                },
                add: {
                    imports: [
                        FakeGoogleMapComponent,
                        FakeMapAdvancedMarkerComponent,
                        FakeMapInfoWindowComponent,
                        FakeMapCircleComponent,
                    ],
                },
            })
            .compileComponents()

        fixture = TestBed.createComponent(GoogleMapContainerComponent)
        component = fixture.componentInstance
        fixture.componentRef.setInput(
            'rechercheGaragesForm',
            new RechercheGaragesForm()
        )
        fixture.componentRef.setInput('foundMarkers', markers)
    })

    describe('inputs and computed properties', () => {
        it('should compute markers with correct content images', () => {
            fixture.detectChanges()

            const computedMarkers = component.markers()
            expect(computedMarkers.length).toBe(2)
            expect(computedMarkers[0].content.tagName).toBe('IMG')
            expect(computedMarkers[0].content.src).toContain(MAP_MARKER)
            expect(computedMarkers[1].content.src).toContain(MAP_MARKER_ACTIVE)
        })

        it('should compute options with center and radius from signals', () => {
            expect(component.options().center).toEqual({ lat: 50, lng: 6 })
            expect(component.options().radius).toBe(0)

            component.rechercheGaragesForm().setValue({
                localiteOrGarage: localiteSearchResultItemMock(),
                distance: DistanceKm.FIFTEEN_KM,
            })
            fixture.detectChanges()
            expect(component.options().center).toEqual({
                lat: 51.2194,
                lng: 4.4024,
            })
            expect(component.options().radius).toBe(15000)
        })
    })

    describe('openInfoWindow', () => {
        it('should emit selectedMarker and open infoWindow when marker matches', () => {
            fixture.detectChanges()

            const fakeAdvancedMarker = {
                advancedMarker: {
                    position: {
                        lat: 10,
                        lng: 20,
                    },
                },
            } as unknown as MapAdvancedMarker
            component.infoWindow = {
                open: jasmine.createSpy('open'),
            } as unknown as MapInfoWindow
            spyOn(component.selectedMarker, 'emit').and.callThrough()

            component.openInfoWindow(fakeAdvancedMarker)

            expect(component.selectedMarker.emit).toHaveBeenCalledWith(
                jasmine.objectContaining(markers[0])
            )
            expect(component.infoWindow.open).toHaveBeenCalledWith(
                fakeAdvancedMarker
            )
        })

        it('should do nothing if no matching marker found', () => {
            fixture.detectChanges()

            const fakeAdvancedMarker = {
                advancedMarker: {
                    position: {
                        lat: 999,
                        lng: 999,
                    },
                },
            } as unknown as MapAdvancedMarker
            component.infoWindow = {
                open: jasmine.createSpy('open'),
            } as unknown as MapInfoWindow
            spyOn(component.selectedMarker, 'emit')

            component.openInfoWindow(fakeAdvancedMarker)

            expect(component.selectedMarker.emit).not.toHaveBeenCalled()
            expect(component.infoWindow.open).not.toHaveBeenCalled()
        })
    })

    describe('onWindowClosed', () => {
        it('should emit undefined on selectedMarker output', () => {
            const emitSpy = spyOn(component.selectedMarker, 'emit')
            component.onWindowClosed()
            expect(emitSpy).toHaveBeenCalledWith(undefined)
        })
    })
})
