import {
    ChangeDetectionStrategy,
    Component,
    computed,
    effect,
    input,
    output,
    signal,
    ViewChild,
} from '@angular/core'
import {
    GoogleMap,
    MapAdvancedMarker,
    MapCircle,
    MapInfoWindow,
} from '@angular/google-maps'
import { MAP_MARKER, MAP_MARKER_ACTIVE } from '../../utils/icone.utils'
import { RechercheGaragesForm } from '../../../auto/components/garage/forms/recherche-garages.form'
import { Marker } from '../../models/marker'

@Component({
    selector: 'google-map-container',
    standalone: true,
    imports: [GoogleMap, MapAdvancedMarker, MapInfoWindow, MapCircle],
    templateUrl: './google-map-container.component.html',
    styleUrl: './google-map-container.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class GoogleMapContainerComponent {
    foundMarkers = input<Marker[]>([])
    rechercheGaragesForm = input.required<RechercheGaragesForm>()

    selectedMarker = output<Marker | undefined>()

    @ViewChild(MapInfoWindow)
    infoWindow!: MapInfoWindow

    options = computed<google.maps.CircleOptions>(() => ({
        mapId: 'MAP_ID',
        center: this.centerSignal(),
        radius: this.distanceSignal() * 1000,
        fillColor: '#2957C8',
        fillOpacity: 0.2,
        strokeColor: '#4285F4',
        strokeOpacity: 0.9,
        strokeWeight: 2,
        zoom: 10,
        streetViewControl: false,
        mapTypeControl: false,
        fullscreenControl: false,
        zoomControl: false,
        scrollwheel: true,
        scaleControl: false,
        rotateControl: false,
        clickableIcons: false,
    }))
    markers = computed(() =>
        this.foundMarkers().map((marker) => ({
            ...marker,
            content: this.setImg(marker.isActive),
        }))
    )

    selected?: Marker

    private readonly centerSignal = signal<{ lat: number; lng: number }>({
        lat: 50,
        lng: 6,
    })
    private readonly distanceSignal = signal<number>(0)

    constructor() {
        effect(
            () => {
                const recherche = this.rechercheGaragesForm().getValue()
                if (
                    recherche.localiteOrGarage?.latitude &&
                    recherche.localiteOrGarage?.longitude
                ) {
                    this.centerSignal.set({
                        lat: recherche.localiteOrGarage.latitude,
                        lng: recherche.localiteOrGarage.longitude,
                    })
                }

                if (recherche.distance) {
                    const distanceKm = parseInt(recherche.distance, 10) || 0
                    this.distanceSignal.set(distanceKm)
                }
            },
            { allowSignalWrites: true }
        )
    }

    openInfoWindow(marker: MapAdvancedMarker): void {
        this.selected = this.markers().find(
            (m) =>
                m.lat === marker.advancedMarker?.position?.lat &&
                m.lng === marker.advancedMarker?.position?.lng
        )

        if (!this.selected) {
            return
        }

        this.selectedMarker.emit(this.selected)
        this.infoWindow.open(marker)
    }

    onWindowClosed(): void {
        this.selectedMarker.emit(undefined)
    }

    private setImg(active = false): HTMLImageElement {
        return Object.assign(document.createElement('img'), {
            src: active ? MAP_MARKER_ACTIVE : MAP_MARKER,
            alt: 'Map icon',
        })
    }
}
