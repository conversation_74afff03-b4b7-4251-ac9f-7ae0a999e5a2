<google-map height="400px" width="100%" [options]="options()">
    @for (marker of markers(); track marker.id) {
        <map-advanced-marker
            #markerElem="mapAdvancedMarker"
            [position]="{ lat: marker.lat, lng: marker.lng }"
            [content]="marker.content"
            (mapClick)="openInfoWindow(markerElem)" />

        <map-circle [options]="options()"></map-circle>

        <map-info-window (closeclick)="onWindowClosed()">
            <h5>{{ selected?.nom }}</h5>
        </map-info-window>
    }
</google-map>
