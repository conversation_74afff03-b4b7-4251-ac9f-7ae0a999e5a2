import {
    ChangeDetectionStrategy,
    Component,
    EventEmitter,
    Input,
    Output,
} from '@angular/core'
import { TranslationReference } from '../../wrappers/translation/models/translation-reference'
import { BadgeColorStatutRisquePipe } from '../../pipes/style/badge-color/badge-color-statut-risque.pipe'
import { FoyerDatePipe } from '../../pipes/foyer/foyer-date.pipe'
import { ReferentielTranslationPipe } from '../../pipes/referentiel-translation/referentiel-translation.pipe'
import { NumeroRisqueToAdressePipe } from '../../pipes/declaration/numero-risque-to-adresse.pipe'

import { AsyncPipe, NgClass } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { Risque } from '../../models/risque'
import { VehicleBrandToLogoPipe } from '../../pipes/vehicle-brand-to-logo.pipe'

@Component({
    selector: 'risque-panel',
    templateUrl: './risque-panel.component.html',
    styleUrls: ['./risque-panel.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        NgClass,
        AsyncPipe,
        I18nPipe,
        ReferentielTranslationPipe,
        FoyerDatePipe,
        BadgeColorStatutRisquePipe,
        NumeroRisqueToAdressePipe,
        VehicleBrandToLogoPipe,
    ],
})
export class RisquePanelComponent {
    @Input()
    risque?: Risque

    @Input()
    isDisabled = false

    @Output()
    readonly selectRisque = new EventEmitter<Risque>()

    readonly TranslationReference = TranslationReference

    onSelectRisque(risque: Risque) {
        this.selectRisque.emit(risque)
    }
}
