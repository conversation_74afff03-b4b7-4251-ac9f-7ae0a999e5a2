import { ComponentFixture, TestBed } from '@angular/core/testing'

import { RisquePanelComponent } from './risque-panel.component'
import { By } from '@angular/platform-browser'
import { TranslationWrapper } from '../../wrappers/translation/translation.wrapper'
import { TranslationWrapperMock } from '../../wrappers/translation/translation.wrapper.mock'
import { risquesMock } from '../../mocks/risques.mock'
import { expectElementToExist } from '../../utils/test.utils'
import { RisquesService } from '../../services/risques/risques.service'
import { RisquesServiceMock } from '../../services/risques/risques.service.mock'

describe('RisquePanelComponent', () => {
    let component: RisquePanelComponent
    let fixture: ComponentFixture<RisquePanelComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [RisquePanelComponent],
            providers: [
                {
                    provide: TranslationWrapper,
                    useClass: TranslationWrapperMock,
                },
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                }
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(RisquePanelComponent)
        component = fixture.componentInstance
        component.risque = risquesMock()[0]
        fixture.detectChanges()
    })

    describe('When the user click anywhere on the panel', () => {
        beforeEach(() => {
            component.risque = risquesMock()[0]
            fixture.detectChanges()
        })

        it('should emit the selected risque event', () => {
            spyOn(component.selectRisque, 'emit')
            const panelElement = fixture.debugElement.query(By.css('.DataTile'))

            panelElement.nativeElement.click()
            expect(component.selectRisque.emit).toHaveBeenCalledWith(
                risquesMock()[0]
            )
        })

        it('should not emit the selected risque event when isDisabled is true', () => {
            spyOn(component.selectRisque, 'emit')
            const panelElement = fixture.debugElement.query(By.css('.DataTile'))

            component.isDisabled = true
            fixture.detectChanges()

            panelElement.nativeElement.click()
            expect(component.selectRisque.emit).not.toHaveBeenCalled()
        })

        it('should display plate if the plaque is defined', () => {
            fixture.detectChanges()
            component.risque = {
                ...risquesMock()[0],
            }
            fixture.detectChanges()

            const plaqueElement = fixture.debugElement.query(
                By.css('.LicensePlate')
            )
            expectElementToExist(fixture, '.LicensePlate')
            expect(plaqueElement.nativeElement.textContent).toContain('A112BC2')
        })
    })
})
