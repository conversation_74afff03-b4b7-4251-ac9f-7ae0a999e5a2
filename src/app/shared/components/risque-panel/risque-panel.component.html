@if (risque) {
    <div class="DataTile u-has-padding-16" [ngClass]="{ 'is-disabled u-has-cursor-auto': isDisabled }" (click)="!isDisabled && onSelectRisque(risque)">
        <div class="DataTile-icon is-centered">
            @if (risque.familleProduit === 'ABEL') {
                <ng-container logo>
                    <img [src]="risque.marque | vehicleBrandToLogo" class="code-produit" alt="Logo auto" />
                </ng-container>
            } @else {
                <img src="https://static.foyer.lu/images/static/udb-products/maison.svg" alt="Logo maison" />
            }
        </div>
        <div class="DataTile-content u-has-margin-left-8 u-has-padding-0">
            <div class="DataTile-label-light u-is-txt-10 u-is-txt-bold u-is-txt-feather-grey-700 u-is-txt-monospace">
                {{ 'common.risque-panel.numero' | translate: { numeroRisque: risque.numeroRisque } }}
                <span class="Badge is-small is-inverted u-is-txt-bold u-has-margin-left-8" [ngClass]="risque.status.value | badgeColorStatutRisque">
                    {{ risque.status.label }}
                </span>
            </div>
            <div class="DataTile-label-medium u-is-txt-monospace">
                {{ risque.numeroRisque | numeroRisqueToAdresse | async }}
            </div>
            @if (risque.marque && risque.modele) {
                <div>{{ risque.marque }} - {{ risque.modele }}</div>
            }
            <div class="DataTile-value u-is-txt-monospace">
                {{
                    'common.risque-panel.depuis-le'
                        | translate
                            : {
                                  codeProduit: risque.codeProduit | referentielTranslation: TranslationReference.PRODUIT | async,
                                  typeRisque: risque.typeRisque,
                                  dateEffet: risque.dateEffet | foyerDate,
                              }
                }}
            </div>
            @if (risque.plaque) {
                <div>
                    <div class="LicensePlate" aria-label="license plate">{{ risque.plaque }}</div>
                </div>
            }
        </div>
        <ng-content></ng-content>
    </div>
}
