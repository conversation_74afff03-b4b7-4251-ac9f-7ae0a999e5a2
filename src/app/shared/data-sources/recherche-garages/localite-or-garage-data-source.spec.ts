import { TestBed } from '@angular/core/testing'
import { LocaliteOrGarageDataSourceService } from './localite-or-garage-data-source.service'
import { PrestataireService } from '../../services/prestataire/prestataire.service'
import { AddressGeocodingApiService } from '../../services/address-geocoding-api/address-geocoding-api.service'
import { PrestataireServiceMock } from '../../services/prestataire/prestataire.service.mock'
import { AddressGeocodingApiServiceMock } from '../../services/address-geocoding-api/address-geocoding-api.service.mock'
import { of } from 'rxjs'
import { TypeResultat } from '../../../auto/enums/type-resultat.enum'
import {
    garagePageItemMock,
    garagePrestataireMock,
    prestataireIdMock,
} from '../../mocks/prestataires.mock'
import {
    locationArlonMock,
    locationBruxellesMock,
} from '../../mocks/address-geocoding-api.mock'
import Spy = jasmine.Spy

describe('LocaliteOrGarageDataSource', () => {
    let service: LocaliteOrGarageDataSourceService
    let prestataireService: PrestataireService
    let addressGeocodingApiService: AddressGeocodingApiService

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [
                LocaliteOrGarageDataSourceService,
                {
                    provide: PrestataireService,
                    useClass: PrestataireServiceMock,
                },
                {
                    provide: AddressGeocodingApiService,
                    useClass: AddressGeocodingApiServiceMock,
                },
            ],
        })
        service = TestBed.inject(LocaliteOrGarageDataSourceService)
        prestataireService = TestBed.inject(PrestataireService)
        addressGeocodingApiService = TestBed.inject(AddressGeocodingApiService)
    })

    describe('When search method is called with empty string', () => {
        let searchPlacesSpy: Spy
        let findAllGaragesByNomPrenomOrNumeroPersonneSpy: Spy

        beforeEach(() => {
            searchPlacesSpy = spyOn(
                addressGeocodingApiService,
                'searchPlaces'
            ).and.callThrough()

            findAllGaragesByNomPrenomOrNumeroPersonneSpy = spyOn(
                prestataireService,
                'findAllGaragesByNomPrenomOrNumeroPersonne'
            ).and.callThrough()

            service.search('')
        })

        it('should return empty array without calling services', (done) => {
            service.data.subscribe((result) => {
                expect(searchPlacesSpy).not.toHaveBeenCalled()
                expect(
                    findAllGaragesByNomPrenomOrNumeroPersonneSpy
                ).not.toHaveBeenCalled()
                expect(result).toEqual([])
                done()
            })
        })
    })

    describe('When search method is called with string less than 3 characters', () => {
        let searchPlacesSpy: Spy
        let findAllGaragesByNomPrenomOrNumeroPersonneSpy: Spy

        beforeEach(() => {
            searchPlacesSpy = spyOn(
                addressGeocodingApiService,
                'searchPlaces'
            ).and.callThrough()

            findAllGaragesByNomPrenomOrNumeroPersonneSpy = spyOn(
                prestataireService,
                'findAllGaragesByNomPrenomOrNumeroPersonne'
            ).and.callThrough()

            service.search('ab')
        })

        it('should return empty array without calling services', (done) => {
            service.data.subscribe((result) => {
                expect(searchPlacesSpy).not.toHaveBeenCalled()
                expect(
                    findAllGaragesByNomPrenomOrNumeroPersonneSpy
                ).not.toHaveBeenCalled()
                expect(result).toEqual([])
                done()
            })
        })
    })

    describe('When search method is called with valid string', () => {
        let searchPlacesSpy: Spy
        let findAllGaragesByNomPrenomOrNumeroPersonneSpy: Spy

        beforeEach(() => {
            searchPlacesSpy = spyOn(
                addressGeocodingApiService,
                'searchPlaces'
            ).and.returnValue(of([locationBruxellesMock()]))

            findAllGaragesByNomPrenomOrNumeroPersonneSpy = spyOn(
                prestataireService,
                'findAllGaragesByNomPrenomOrNumeroPersonne'
            ).and.returnValue(of([garagePageItemMock()]))
        })

        it('should return both locality and garage results', (done) => {
            service.search('bruxelles')

            service.data.subscribe((result) => {
                expect(searchPlacesSpy).toHaveBeenCalledWith('bruxelles')
                expect(
                    findAllGaragesByNomPrenomOrNumeroPersonneSpy
                ).toHaveBeenCalledWith('bruxelles')

                expect(result.length).toBe(2)
                expect(result[0].typeResult).toBe(TypeResultat.LOCALITE)
                expect(result[0].label).toBe('Bruxelles (1000)')
                expect(result[0].id).toBe(locationBruxellesMock().place_id)
                expect(result[0].latitude).toBe(
                    locationBruxellesMock().geometry.location.lat
                )
                expect(result[0].longitude).toBe(
                    locationBruxellesMock().geometry.location.lng
                )

                expect(result[1].typeResult).toBe(TypeResultat.GARAGE)
                expect(result[1].label).toBe(
                    garagePrestataireMock().nomPrenom +
                        ' (' +
                        garagePrestataireMock().numeroPersonne +
                        ')'
                )
                expect(result[1].id).toBe(prestataireIdMock)

                done()
            })
        })
    })

    describe('When search method is called with a postal code', () => {
        let searchPlacesSpy: Spy
        let findAllGaragesByNomPrenomOrNumeroPersonneSpy: Spy

        beforeEach(() => {
            searchPlacesSpy = spyOn(
                addressGeocodingApiService,
                'searchPlaces'
            ).and.returnValue(of([locationArlonMock()]))

            findAllGaragesByNomPrenomOrNumeroPersonneSpy = spyOn(
                prestataireService,
                'findAllGaragesByNomPrenomOrNumeroPersonne'
            ).and.returnValue(of([]))
        })

        it('should extract locality name correctly from address components', (done) => {
            service.search('6700')

            service.data.subscribe((result) => {
                expect(searchPlacesSpy).toHaveBeenCalledWith('6700')
                expect(
                    findAllGaragesByNomPrenomOrNumeroPersonneSpy
                ).toHaveBeenCalledWith('6700')

                expect(result.length).toBe(1)

                expect(result[0].typeResult).toBe(TypeResultat.LOCALITE)
                expect(result[0].label).toBe('Arlon (6700)')

                done()
            })
        })
    })

    describe('When address geocoding API returns no results', () => {
        let searchPlacesSpy: Spy
        let findAllGaragesByNomPrenomOrNumeroPersonneSpy: Spy

        beforeEach(() => {
            searchPlacesSpy = spyOn(
                addressGeocodingApiService,
                'searchPlaces'
            ).and.returnValue(of([]))

            findAllGaragesByNomPrenomOrNumeroPersonneSpy = spyOn(
                prestataireService,
                'findAllGaragesByNomPrenomOrNumeroPersonne'
            ).and.returnValue(of([garagePageItemMock()]))
        })

        it('should return only garage results', (done) => {
            service.search('garage')

            service.data.subscribe((result) => {
                expect(searchPlacesSpy).toHaveBeenCalledWith('garage')
                expect(
                    findAllGaragesByNomPrenomOrNumeroPersonneSpy
                ).toHaveBeenCalledWith('garage')

                expect(result.length).toBe(1)
                expect(result[0].typeResult).toBe(TypeResultat.GARAGE)

                done()
            })
        })
    })

    describe('When Prestataire service returns no results', () => {
        let searchPlacesSpy: Spy
        let findAllGaragesByNomPrenomOrNumeroPersonneSpy: Spy

        beforeEach(() => {
            searchPlacesSpy = spyOn(
                addressGeocodingApiService,
                'searchPlaces'
            ).and.returnValue(of([locationBruxellesMock()]))

            findAllGaragesByNomPrenomOrNumeroPersonneSpy = spyOn(
                prestataireService,
                'findAllGaragesByNomPrenomOrNumeroPersonne'
            ).and.returnValue(of([]))
        })

        it('should return only locality results', (done) => {
            service.search('bruxelles')

            service.data.subscribe((result) => {
                expect(searchPlacesSpy).toHaveBeenCalledWith('bruxelles')
                expect(
                    findAllGaragesByNomPrenomOrNumeroPersonneSpy
                ).toHaveBeenCalledWith('bruxelles')

                expect(result.length).toBe(1)
                expect(result[0].typeResult).toBe(TypeResultat.LOCALITE)

                done()
            })
        })
    })
})
