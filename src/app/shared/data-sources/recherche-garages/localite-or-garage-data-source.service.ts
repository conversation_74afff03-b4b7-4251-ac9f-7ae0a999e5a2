import { BehaviorSubject, forkJoin, map, mergeMap, Observable, of } from 'rxjs'

import { inject, Injectable } from '@angular/core'
import { TypeResultat } from '../../../auto/enums/type-resultat.enum'
import { PrestataireService } from '../../services/prestataire/prestataire.service'
import { SelectDataSource, SelectSearch } from '@foyer/ng-select'
import { AddressGeocodingApiService } from '../../services/address-geocoding-api/address-geocoding-api.service'
import { AdressGeocodingLocation } from '../../models/adress-geocoding-location'
import { TypeAddressComponent } from '../../../auto/enums/type-address-component.enum'
import {
    GarageSearchResultItem,
    LocaliteSearchResultItem,
    SearchResultItem,
} from '../../../auto/models/search-result-item'

@Injectable({
    providedIn: 'root',
})
export class LocaliteOrGarageDataSourceService
    implements SelectDataSource<SearchResultItem[]>, SelectSearch
{
    private readonly prestataireService = inject(PrestataireService)
    private readonly addressGeocodingApiService = inject(
        AddressGeocodingApiService
    )
    private readonly search$: BehaviorSubject<string>

    data: Observable<SearchResultItem[]>

    constructor() {
        this.search$ = new BehaviorSubject('')
        this.data = this.search$.pipe(
            mergeMap((criteria) => this.rechercherLocalitesEtGarages(criteria))
        )
    }

    search(search: string): void {
        this.search$.next(search)
    }

    private rechercherLocalitesEtGarages(
        query: string
    ): Observable<SearchResultItem[]> {
        if (!query || query.trim().length < 3) {
            return of([])
        }

        return forkJoin({
            localites: this.addressGeocodingApiService.searchPlaces(query),
            garages:
                this.prestataireService.findAllGaragesByNomPrenomOrNumeroPersonne(
                    query
                ),
        }).pipe(
            map(({ localites, garages }): SearchResultItem[] => [
                ...(
                    localites?.map(
                        (localite): LocaliteSearchResultItem => ({
                            id: localite.place_id,
                            typeResult: TypeResultat.LOCALITE,
                            longitude: localite.geometry.location.lng,
                            latitude: localite.geometry.location.lat,
                            label: this.getLabel(localite),
                        })
                    ) || []
                ).filter((localite) => localite.label !== ''),
                ...(garages?.map(
                    (garage): GarageSearchResultItem => ({
                        id: garage.id,
                        numeroPersonne: garage.numeroPersonne,
                        typeResult: TypeResultat.GARAGE,
                        longitude: garage.location?.coordinates[0],
                        latitude: garage.location?.coordinates[1],
                        label: `${garage.nomPrenom} (${garage.numeroPersonne})`,
                    })
                ) ?? []),
            ])
        )
    }

    private getLabel(localite: AdressGeocodingLocation): string {
        const localityName = this.getLocalityOrPostalCodeFromResult(
            localite,
            TypeAddressComponent.LOCALITY
        )

        if (!localityName) {
            return ''
        }

        const postalCode = this.getLocalityOrPostalCodeFromResult(
            localite,
            TypeAddressComponent.POSTAL_CODE
        )

        return postalCode
            ? localityName + ' (' + postalCode + ')'
            : localityName
    }

    private getLocalityOrPostalCodeFromResult(
        result: AdressGeocodingLocation,
        type: TypeAddressComponent
    ): string | null {
        if (!result?.address_components?.length) {
            return null
        }

        const addressDetail = result.address_components.find((ad) =>
            ad.types?.includes(type)
        )

        return addressDetail?.long_name ?? null
    }
}
