import { TypeResultat } from '../../../auto/enums/type-resultat.enum'
import { Observable, of } from 'rxjs'
import {
    LocaliteSearchResultItem,
    SearchResultItem,
} from '../../../auto/models/search-result-item'

export const localiteSearchResultItemMock = (): LocaliteSearchResultItem => ({
    id: '123456',
    typeResult: TypeResultat.LOCALITE,
    longitude: 4.4024,
    latitude: 51.2194,
    label: 'Arlon(6700)',
})

export class LocaliteOrGarageDataSourceServiceMock {
    data: Observable<SearchResultItem[]> = of([localiteSearchResultItemMock()])

    search(search: string): void {}
}
