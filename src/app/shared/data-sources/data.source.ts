import { BehaviorSubject, Observable } from 'rxjs'
import { map } from 'rxjs/operators'
import { DataSourceItem } from './data-source.item'
import { SelectDataSource, SelectSearch } from '@foyer/ng-select'

export class DataSource
    implements SelectDataSource<DataSourceItem[]>, SelectSearch
{
    data: Observable<DataSourceItem[]>
    private readonly search$: BehaviorSubject<string>

    constructor(dataSourceItems: DataSourceItem[]) {
        this.search$ = new BehaviorSubject('')

        this.data = this.search$.pipe(
            map((search) => {
                if (search) {
                    const regex = new RegExp(
                        search.replace(/[-\\^$*+?.()|[\]{}]/g, '\\$&'),
                        'i'
                    )

                    return dataSourceItems.filter(
                        (option) => regex.exec(option.displayValue) !== null
                    )
                }
                return dataSourceItems
            })
        )
    }

    search(search: string) {
        this.search$.next(search)
    }
}
