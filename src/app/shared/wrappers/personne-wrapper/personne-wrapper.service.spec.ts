import { TestBed } from '@angular/core/testing'
import { PersonneWrapper } from './personne-wrapper.service'
import {
    HttpClientTestingModule,
    HttpTestingController,
} from '@angular/common/http/testing'
import {
    personneMoraleMock,
    personnePhysiqueMock,
} from '../../mocks/personne.mock'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { environment } from '../../../../environments/environment'
import { take } from 'rxjs'
import { Personne, REFERENTIEL_PERSONNE_KIND } from './models/personne/personne'

describe('PersonneWrapper', () => {
    let service: PersonneWrapper
    let httpMock: HttpTestingController

    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
        })
        service = TestBed.inject(PersonneWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    describe('When it is a personne physique', () => {
        it('should make a call to API with the right parameters for numeroPersonne', (done) => {
            const expectedDataFromApi: JsonApiResponseInterface<Personne> = {
                data: {
                    type: REFERENTIEL_PERSONNE_KIND,
                    id: '2062d908-0122-40a5-0000-0000484538d4',
                    attributes: personnePhysiqueMock(),
                    relationships: {},
                },
            }
            const numPersonne = '123456'
            service
                .findOne(numPersonne)
                .pipe(take(1))
                .subscribe((result) => {
                    expect(result).toEqual(expectedDataFromApi)
                    done()
                })

            const testRequest = httpMock.expectOne(
                `${environment.refPersonnesApiUrl}personnes/123456?include=adresseCorrespondance.referenceAdresse`
            )
            expect(testRequest.request.method).toBe('GET')
            testRequest.flush(expectedDataFromApi)
            httpMock.verify()
        })
    })

    describe('When it is a personne morale', () => {
        it('should make a call to API with the right parameters for numeroPersonne', (done) => {
            const expectedDataFromApi: JsonApiResponseInterface<Personne> = {
                data: {
                    type: REFERENTIEL_PERSONNE_KIND,
                    id: '2062d908-0122-40a5-0000-0000484538d4',
                    attributes: personneMoraleMock(),
                    relationships: {},
                },
            }
            const numPersonne = '654321'
            service
                .findOne(numPersonne)
                .pipe(take(1))
                .subscribe((result) => {
                    expect(result).toEqual(expectedDataFromApi)
                    done()
                })

            const testRequest = httpMock.expectOne(
                `${environment.refPersonnesApiUrl}personnes/654321?include=adresseCorrespondance.referenceAdresse`
            )
            expect(testRequest.request.method).toBe('GET')
            testRequest.flush(expectedDataFromApi)
            httpMock.verify()
        })
    })
})
