import { Observable, of } from 'rxjs'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import {
    personnePhysiqueMock,
    personnePhysiqueReponseApiMock,
} from '../../mocks/personne.mock'
import { Personne } from './models/personne/personne'

export class PersonneWrapperMock {
    getPersonne(numeroPersonne: string): Observable<Personne> {
        return of(personnePhysiqueMock())
    }

    findOne(
        numeroPersonne: string
    ): Observable<JsonApiResponseInterface<Personne>> {
        return of(personnePhysiqueReponseApiMock())
    }
}
