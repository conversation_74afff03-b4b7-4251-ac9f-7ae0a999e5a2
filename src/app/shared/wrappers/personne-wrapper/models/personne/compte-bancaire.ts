import { IndicateurFraicheur } from './indicateur-fraicheur'
import { UtilisationCompte } from './utilisation-compte'
import { CodeBic } from '../../../../enums/code-bic.enum'

export type CodeBicOrString = CodeBic | string

export interface ValiditeCompteBancaire {
    dateDebut: string
    dateFin?: string
}

export interface CompteBancaire {
    codeBic: CodeBicOrString
    indicateur: IndicateurFraicheur
    numeroCompteBancaire: string
    utilisations: UtilisationCompte[]
    validite: ValiditeCompteBancaire
}
