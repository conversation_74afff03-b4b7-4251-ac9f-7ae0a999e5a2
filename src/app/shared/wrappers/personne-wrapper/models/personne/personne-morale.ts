import { PersonneKind } from './personne-kind.enum'
import { Personne } from './personne'
import { ReferentielAdresse } from './referentiel-adresse'
import { EmailPersonne } from './email-personne'
import { TelephonePersonne } from './telephone-personne'
import { CompteBancaire } from './compte-bancaire'
import { CodeLabel } from '../../../../models/code-label'
import { DEFAULT_GENERIC_PERSONNE_MORALE_ICONE } from '../../../../utils/icone.utils'

export class PersonneMorale extends Personne {
    nom: string

    constructor(
        langueCorrespondance: CodeLabel,
        numeroPersonne: string,
        typePersonne: CodeLabel,
        nom: string,
        adresseCorrespondance: ReferentielAdresse | undefined,
        emails: EmailPersonne[],
        telephones: TelephonePersonne[],
        comptesBancaires: CompteBancaire[],
        etatClient: CodeLabel,
        contentieux: CodeLabel
    ) {
        super(
            PersonneKind.MORALE,
            langueCorrespondance,
            numeroPersonne,
            typePersonne,
            adresseCorrespondance,
            emails,
            telephones,
            comptesBancaires,
            etatClient,
            contentieux
        )
        this.nom = nom
    }

    override get avatar(): string {
        return DEFAULT_GENERIC_PERSONNE_MORALE_ICONE
    }
}
