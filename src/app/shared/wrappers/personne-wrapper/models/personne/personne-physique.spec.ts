import { PersonneTitre } from '../../../../enums/personne-titre.enum'
import { personnePhysiqueTitreDateNaissanceAndContentieuxMock } from '../../../../mocks/personne.mock'
import {
    ICON_BY_TITRE_PERSONNE_PHYSIQUE,
    YOUNG_MAN_ICON,
    YOUNG_WOMAN_ICON,
} from '../../../../utils/icone.utils'

export const generateDateNaissanceForAge = (age: number): string => {
    const now = new Date()
    const month = now.getMonth() + 1
    const day = now.getDate()
    const year = now.getFullYear() - age

    return `${year}-${month.toString().padStart(2, '0')}-${day
        .toString()
        .padStart(2, '0')}`
}
describe('PersonnePhysique', () => {
    it('should consider the person as adult and return madame icon when datedenaissance is not provided', () => {
        const icon = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: '2', label: '' },
            undefined
        ).avatar
        expect(icon).toBe(
            ICON_BY_TITRE_PERSONNE_PHYSIQUE.get(PersonneTitre.MADAME) as string
        )
    })

    it('should consider the person as adult and return monsieur icon when datedenaissance is not provided', () => {
        const icon = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: '1', label: '' },
            undefined
        ).avatar
        expect(icon).toBe(
            ICON_BY_TITRE_PERSONNE_PHYSIQUE.get(
                PersonneTitre.MONSIEUR
            ) as string
        )
    })
    it('return madame icon', () => {
        const icon = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: '2', label: '' },
            generateDateNaissanceForAge(25)
        ).avatar
        expect(icon).toBe(
            ICON_BY_TITRE_PERSONNE_PHYSIQUE.get(PersonneTitre.MADAME) as string
        )
    })

    it('return monsieur icon', () => {
        const icon = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: '1', label: '' },
            generateDateNaissanceForAge(25)
        ).avatar

        expect(icon).toBe(
            ICON_BY_TITRE_PERSONNE_PHYSIQUE.get(
                PersonneTitre.MONSIEUR
            ) as string
        )
    })

    it('return young man icon', () => {
        const icon = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: '1', label: '' },
            generateDateNaissanceForAge(10)
        ).avatar
        expect(icon).toBe(YOUNG_MAN_ICON)
    })

    it('return young woman icon', () => {
        const icon = personnePhysiqueTitreDateNaissanceAndContentieuxMock(
            { code: '2', label: '' },
            generateDateNaissanceForAge(10)
        ).avatar
        expect(icon).toBe(YOUNG_WOMAN_ICON)
    })
})
