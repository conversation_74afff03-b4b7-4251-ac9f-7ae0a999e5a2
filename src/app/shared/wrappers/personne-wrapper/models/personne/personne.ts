import { PersonneKind } from './personne-kind.enum'
import { CodeLabel } from '../../../../models/code-label'
import { ReferentielAdresse } from './referentiel-adresse'
import { EmailPersonne } from './email-personne'
import { TelephonePersonne } from './telephone-personne'
import { CompteBancaire } from './compte-bancaire'

const ETAT_CLIENT_ANNULE = '9'
export const REFERENTIEL_PERSONNE_KIND =
    'urn:api:referentiel:personnes:personne'

export abstract class Personne {
    _kind: PersonneKind
    langueCorrespondance: CodeLabel
    numeroPersonne: string
    typePersonne: CodeLabel
    adresseCorrespondance?: ReferentielAdresse
    emails: EmailPersonne[]
    telephones: TelephonePersonne[]
    comptesBancaires: CompteBancaire[]
    etatClient: CodeLabel
    contentieux: CodeLabel

    protected constructor(
        kind: PersonneKind,
        langueCorrespondance: CodeLabel,
        numeroPersonne: string,
        typePersonne: CodeLabel,
        adresseCorrespondance: ReferentielAdresse | undefined,
        emails: EmailPersonne[],
        telephones: TelephonePersonne[],
        comptesBancaires: CompteBancaire[],
        etatClient: CodeLabel,
        contentieux: CodeLabel
    ) {
        this._kind = kind
        this.langueCorrespondance = langueCorrespondance
        this.numeroPersonne = numeroPersonne
        this.typePersonne = typePersonne
        this.adresseCorrespondance = adresseCorrespondance
        this.emails = emails
        this.telephones = telephones
        this.comptesBancaires = comptesBancaires
        this.etatClient = etatClient
        this.contentieux = contentieux
    }

    abstract get avatar(): string

    isAnnule(): boolean {
        return this.etatClient.code === ETAT_CLIENT_ANNULE
    }
}
