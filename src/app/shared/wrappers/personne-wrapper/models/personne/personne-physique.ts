import { PersonneKind } from './personne-kind.enum'
import { Personne } from './personne'
import { ReferentielAdresse } from './referentiel-adresse'
import { EmailPersonne } from './email-personne'
import { TelephonePersonne } from './telephone-personne'
import { CompteBancaire } from './compte-bancaire'
import { PersonneTitre } from '../../../../enums/personne-titre.enum'
import { PersonneTitreResponse } from '../../../../models/personne-titre-response'
import { CodeLabel } from '../../../../models/code-label'
import { birthdayToAge } from '../../../../utils/date.utils'
import {
    DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE,
    ICON_BY_TITRE_PERSONNE_PHYSIQUE,
    YOUNG_MAN_ICON,
    YOUNG_WOMAN_ICON,
} from '../../../../utils/icone.utils'

export class PersonnePhysique extends Personne {
    static readonly ADULT_MINIMUM_AGE = 18
    nomUsuel: string
    prenom: string
    titre: PersonneTitreResponse
    dateDeNaissance?: string

    constructor(
        langueCorrespondance: CodeLabel,
        numeroPersonne: string,
        typePersonne: CodeLabel,
        nomUsuel: string,
        prenom: string,
        titre: PersonneTitreResponse,
        adresseCorrespondance: ReferentielAdresse | undefined,
        emails: EmailPersonne[],
        telephones: TelephonePersonne[],
        comptesBancaires: CompteBancaire[],
        etatClient: CodeLabel,
        contentieux: CodeLabel,
        dateDeNaissance?: string
    ) {
        super(
            PersonneKind.PHYSIQUE,
            langueCorrespondance,
            numeroPersonne,
            typePersonne,
            adresseCorrespondance,
            emails,
            telephones,
            comptesBancaires,
            etatClient,
            contentieux
        )
        this.nomUsuel = nomUsuel
        this.prenom = prenom
        this.titre = titre
        this.dateDeNaissance = dateDeNaissance
    }

    override get avatar(): string {
        if (this.dateDeNaissance) {
            const isAdult =
                birthdayToAge(this.dateDeNaissance) >=
                PersonnePhysique.ADULT_MINIMUM_AGE
            if (isAdult) {
                return (
                    ICON_BY_TITRE_PERSONNE_PHYSIQUE.get(this.titre.code) ??
                    DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE
                )
            } else {
                return this.titre.code === PersonneTitre.MADAME
                    ? YOUNG_WOMAN_ICON
                    : YOUNG_MAN_ICON
            }
        } else {
            return (
                ICON_BY_TITRE_PERSONNE_PHYSIQUE.get(this.titre.code) ??
                DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE
            )
        }
    }
}
