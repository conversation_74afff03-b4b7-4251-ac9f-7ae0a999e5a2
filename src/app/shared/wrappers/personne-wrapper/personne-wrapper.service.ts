import { Injectable } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { environment } from '../../../../environments/environment'
import { map, Observable, shareReplay } from 'rxjs'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { mapJsonApiResponseToPersonne } from '../../utils/personne.utils'
import { Personne } from './models/personne/personne'

@Injectable({
    providedIn: 'root',
})
export class PersonneWrapper {
    private readonly apiUrl: string
    private readonly cache: Map<string, Observable<Personne>> = new Map()

    constructor(private readonly http: HttpClient) {
        this.apiUrl = `${environment.refPersonnesApiUrl}`
    }

    getPersonne(numeroPersonne: string): Observable<Personne> {
        if (!this.cache.has(numeroPersonne)) {
            const personneObservable = this.findOne(numeroPersonne).pipe(
                map((personne) => mapJsonApiResponseToPersonne(personne)),
                shareReplay(1)
            )

            this.cache.set(numeroPersonne, personneObservable)
        }
        return this.cache.get(numeroPersonne)!
    }

    findOne(
        numeroPersonne: string
    ): Observable<JsonApiResponseInterface<Personne>> {
        const url = `${this.apiUrl}personnes/${numeroPersonne}?include=adresseCorrespondance.referenceAdresse`
        return this.http.get<JsonApiResponseInterface<Personne>>(url)
    }
}
