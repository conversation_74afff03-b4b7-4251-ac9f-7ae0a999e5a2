import { Injectable } from '@angular/core'
import { HttpClient, HttpParams } from '@angular/common/http'
import { environment } from '../../../../environments/environment'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { Observable } from 'rxjs'
import { PaysApiModel } from './models/adresse-api.model'

@Injectable({
    providedIn: 'root',
})
export class AdresseWrapper {
    private readonly apiUrl: string

    constructor(private readonly http: HttpClient) {
        this.apiUrl = `${environment.refAdressesApiUrl}`
    }

    getPays(
        params: HttpParams
    ): Observable<JsonApiResponseInterface<PaysApiModel[]>> {
        const url = `${this.apiUrl}pays`
        return this.http.get<JsonApiResponseInterface<PaysApiModel[]>>(url, {
            params,
        })
    }
}
