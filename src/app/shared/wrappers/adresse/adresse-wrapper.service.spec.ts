import { TestBed } from '@angular/core/testing'
import { AdresseWrapper } from './adresse-wrapper.service'
import { provideHttpClient } from '@angular/common/http'
import {
    HttpTestingController,
    provideHttpClientTesting,
} from '@angular/common/http/testing'
import { environment } from '../../../../environments/environment'
import { paysMock } from '../../mocks/refAdresseMock'
import { PageSize } from '../../enums/page-size.enum'
import { AdresseWrapperMock } from './adresse-wrapper.service.spec.mock'
import { ParamsBuilder } from '../../builders/params-builder'

describe('AdresseWrapper', () => {
    let service: AdresseWrapper
    let httpMock: HttpTestingController

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [provideHttpClient(), provideHttpClientTesting()],
        })

        service = TestBed.inject(AdresseWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    describe('When getPays is called', () => {
        it('should call the API with the right parameters', (done) => {
            const params = new ParamsBuilder()
                .addParam('page[size]', PageSize.TWO_HUNDRED_FIFTY_ELEMENTS)
                .build()
            service.getPays(params).subscribe((result) => {
                expect(result).toEqual(
                    AdresseWrapperMock.toPaysApiResultArray(paysMock())
                )
                done()
            })

            const testRequest = httpMock.expectOne(
                `${environment.refAdressesApiUrl}pays?page%5Bsize%5D=250`
            )
            expect(testRequest.request.method).toBe('GET')
            testRequest.flush(
                AdresseWrapperMock.toPaysApiResultArray(paysMock())
            )
            httpMock.verify()
        })
    })
})
