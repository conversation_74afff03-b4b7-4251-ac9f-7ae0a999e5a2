import { HttpParams } from '@angular/common/http'
import { Observable, of } from 'rxjs'
import { PaysApiModel } from './models/adresse-api.model'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { JsonApiData } from '../../models/api/json-api-data'
import { JsonApiDataAttributes } from '../../models/api/json-api-data-attributes'
import { PageSize } from '../../enums/page-size.enum'
import { paysMock } from '../../mocks/refAdresseMock'

export class AdresseWrapperMock {
    static toPaysApiResultArray(
        pays: PaysApiModel[]
    ): JsonApiResponseInterface<PaysApiModel[]> {
        const data: JsonApiData<PaysApiModel & JsonApiDataAttributes>[] =
            pays.map((pays: PaysApiModel) => ({
                attributes: pays,
                type: 'urn:api:referentiel:adresses:pays:pays-state',
                id: 'ID',
            })) as unknown as JsonApiData<
                PaysApiModel & JsonApiDataAttributes
            >[]
        return {
            data,
            meta: {
                totalPages: 1,
                totalRecords: 250,
                page: {
                    number: 0,
                    size: PageSize.TWO_HUNDRED_FIFTY_ELEMENTS,
                },
            },
        }
    }

    getPays(
        params: HttpParams
    ): Observable<JsonApiResponseInterface<PaysApiModel[]>> {
        return of(AdresseWrapperMock.toPaysApiResultArray(paysMock()))
    }
}
