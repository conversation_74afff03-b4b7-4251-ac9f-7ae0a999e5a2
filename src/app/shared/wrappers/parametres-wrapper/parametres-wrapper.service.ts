import { Injectable } from '@angular/core'
import { HttpClient, HttpParams } from '@angular/common/http'
import { Observable, shareReplay } from 'rxjs'
import { environment } from '../../../../environments/environment'
import { CirconstancesList } from '../../models/circonstance-parametre'
import { getOrFail } from '../../utils/objects.utils'
import { ClefParametres } from '../../enums/clef-parametres.enum'
import { Parametres } from '../../models/parametres'

@Injectable({
    providedIn: 'root',
})
export class ParametresWrapper {
    private readonly apiUrl: string

    private readonly parametresCache = new Map<
        ClefParametres,
        Observable<Parametres<any>>
    >()

    constructor(private readonly http: HttpClient) {
        this.apiUrl = `${environment.sinistreMarcheBelgeExternalApiUrl}`
    }

    findOneByClef<T>(clef: ClefParametres): Observable<Parametres<T>> {
        const url = `${this.apiUrl}miscellaneous/parametres/${clef}`

        if (!this.parametresCache.has(clef)) {
            const parametreCallObservable = this.http
                .get<Parametres<T>>(url)
                .pipe(shareReplay(1))

            this.parametresCache.set(clef, parametreCallObservable)
        }

        return getOrFail(this.parametresCache.get(clef), clef)
    }

    findCirconstancesWithParams(
        params: HttpParams
    ): Observable<CirconstancesList> {
        const url = `${this.apiUrl}miscellaneous/parametres/circonstances/`
        return this.http.get<CirconstancesList>(url, {
            params,
        })
    }
}
