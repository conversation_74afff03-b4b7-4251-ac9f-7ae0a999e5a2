import { TestBed } from '@angular/core/testing'

import {
    HttpTestingController,
    provideHttpClientTesting,
} from '@angular/common/http/testing'
import { take } from 'rxjs'
import { ParametresWrapper } from './parametres-wrapper.service'
import { circonstancesParamMock } from '../../mocks/circonstances.mock'
import { FiltersBuilder } from '../../builders/filters-builder'
import { FilterType } from '../../enums/filter-type.enum'
import { ParamsBuilder } from '../../builders/params-builder'
import { provideHttpClient } from '@angular/common/http'
import { TypeCirconstance } from '../../enums/type-circonstance.enum'
import { environment } from '../../../../environments/environment'
import { CirconstancesList } from '../../models/circonstance-parametre'
import { Parametres } from '../../models/parametres'
import { FeatureFlagsParametres } from '../../models/feature-flags-parametres'
import { featureFlagsParametresMock } from './parametres-wrapper.service.spec.mock'
import { ClefParametres } from '../../enums/clef-parametres.enum'

describe('ParametresWrapper', () => {
    let service: ParametresWrapper
    let httpMock: HttpTestingController

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [provideHttpClient(), provideHttpClientTesting()],
        })
        service = TestBed.inject(ParametresWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    it('should make a call to API with the right parameters for findOne with feature flags key', (done) => {
        const expectedDataFromApi: Parametres<FeatureFlagsParametres> =
            featureFlagsParametresMock(true)
        const clef = ClefParametres.FEATURE_FLAGS
        service
            .findOneByClef(clef)
            .pipe(take(1))
            .subscribe((result) => {
                expect(result).toEqual(expectedDataFromApi)
                done()
            })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}miscellaneous/parametres/${clef}`
        )
        expect(testRequest.request.method).toBe('GET')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })

    it('should make a call to API with the right parameters for findCirconstancesWithParams', (done) => {
        const expectedDataFromApi: CirconstancesList = circonstancesParamMock()
        const filters = new FiltersBuilder()
            .addFilter(FilterType.CIRCONSTANCES_PRINCIPALES, true)
            .addFilter(FilterType.CIRCONSTANCES_VALEURS, TypeCirconstance.DAB)
        const params = new ParamsBuilder()
            .addParamFilters(filters.build())
            .build()

        service
            .findCirconstancesWithParams(params)
            .pipe(take(1))
            .subscribe((result) => {
                expect(result).toEqual(expectedDataFromApi)
                done()
            })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}miscellaneous/parametres/circonstances/?filter%5Bcirconstances.principale%5D=true&filter%5Bcirconstances.valeurs%5D=DAB`
        )
        expect(testRequest.request.method).toBe('GET')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })
})
