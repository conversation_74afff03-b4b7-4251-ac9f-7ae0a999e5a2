import { Injectable } from '@angular/core'
import { Observable, of } from 'rxjs'
import { HttpParams } from '@angular/common/http'
import { circonstancesMock } from '../../mocks/circonstances.mock'
import { CirconstancesList } from '../../models/circonstance-parametre'
import { FeatureFlagsParametres } from '../../models/feature-flags-parametres'
import { ParametresKind } from '../../enums/parametres-kind.enum'
import { ClefParametres } from '../../enums/clef-parametres.enum'
import { Parametres } from '../../models/parametres'

export const featureFlagsParametresMock = (
    withAuto = true
): Parametres<FeatureFlagsParametres> => ({
    _kind: ParametresKind.FEATURE_FLAGS_PARAMETRE,
    clef: ClefParametres.FEATURE_FLAGS,
    libelle: '',
    valeur: {
        withDossiersAuto: false,
        withPaiementDirect: false,
        withDeclarationAuto: withAuto,
    },
})

@Injectable({
    providedIn: 'root',
})
export class ParametresWrapperMock {
    findOneByClef<T>(clef: ClefParametres): Observable<Parametres<T>> {
        return of(featureFlagsParametresMock()) as unknown as Observable<
            Parametres<T>
        >
    }

    findCirconstancesWithParams(
        params: HttpParams
    ): Observable<CirconstancesList> {
        return of({ circonstances: circonstancesMock() })
    }
}
