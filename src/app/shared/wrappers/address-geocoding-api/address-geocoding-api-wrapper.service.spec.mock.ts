import { Injectable } from '@angular/core'
import { Observable, of } from 'rxjs'
import { addressGeocodingResultMock } from '../../mocks/address-geocoding-api.mock'
import { AdressGeocodingLocation } from '../../models/adress-geocoding-location'

@Injectable({
    providedIn: 'root',
})
export class AddressGeocodingApiWrapperMock {
    geocoder(
        localiteOrCodePostal: string,
        pays: string = 'BE'
    ): Observable<AdressGeocodingLocation[]> {
        return of(addressGeocodingResultMock())
    }
}
