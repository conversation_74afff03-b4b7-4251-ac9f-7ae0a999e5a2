import { Injectable } from '@angular/core'
import { HttpClient, HttpParams } from '@angular/common/http'
import { catchError, Observable, of } from 'rxjs'
import { environment } from '../../../../environments/environment'
import { AdressGeocodingLocation } from '../../models/adress-geocoding-location'

@Injectable({
    providedIn: 'root',
})
export class AddressGeocodingApiWrapper {
    private readonly apiBaseUrl: string

    constructor(private readonly http: HttpClient) {
        this.apiBaseUrl = environment.sinistreMarcheBelgeExternalApiUrl
    }

    geocoder(params: HttpParams): Observable<AdressGeocodingLocation[]> {
        const apiUrl = `${this.apiBaseUrl}miscellaneous/adresse/geocoder`

        return this.http
            .get<AdressGeocodingLocation[]>(apiUrl, { params })
            .pipe(catchError((error) => of([])))
    }
}
