import { TestBed } from '@angular/core/testing'
import {
    HttpTestingController,
    provideHttpClientTesting,
} from '@angular/common/http/testing'
import { environment } from '../../../../environments/environment'
import { AddressGeocodingApiWrapper } from './address-geocoding-api-wrapper.service'
import { addressGeocodingResultMock } from '../../mocks/address-geocoding-api.mock'
import { provideHttpClient } from '@angular/common/http'
import { ParamsBuilder } from '../../builders/params-builder'

describe('AddressGeocodingApiWrapper', () => {
    let service: AddressGeocodingApiWrapper
    let httpMock: HttpTestingController
    const pays = 'BE'
    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [provideHttpClient(), provideHttpClientTesting()],
        })
        service = TestBed.inject(AddressGeocodingApiWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    afterEach(() => {
        httpMock.verify()
    })

    it('should make a call to API with the right parameters for geocoder', (done) => {
        const expectedDataFromApi = addressGeocodingResultMock()
        const localiteOrCodePostal = 'Bruxelles'
        const params = new ParamsBuilder()
            .addParam('filter[localiteOrCodePostal]', localiteOrCodePostal)
            .addParam('filter[pays]', pays)
            .build()

        service.geocoder(params).subscribe((result) => {
            expect(result).toEqual(expectedDataFromApi)
            done()
        })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}miscellaneous/adresse/geocoder?filter%5BlocaliteOrCodePostal%5D=${localiteOrCodePostal}&filter%5Bpays%5D=${pays}`
        )
        expect(testRequest.request.method).toBe('GET')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })
})
