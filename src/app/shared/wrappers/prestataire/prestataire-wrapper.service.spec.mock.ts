import { Observable, of } from 'rxjs'
import { HttpParams } from '@angular/common/http'
import { Prestataire } from '../../models/prestataire.model'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { JsonApiData } from '../../models/api/json-api-data'
import { JsonApiDataAttributes } from '../../models/api/json-api-data-attributes'
import { PageSize } from '../../enums/page-size.enum'
import { PrestataireKind } from '../../enums/prestataire-kind.enum'
import { prestataireExpertHabitationMock } from '../../mocks/prestataires.mock'

export class PrestataireWrapperMock {
    static toPrestataireApiResultArray(
        prestataires: Prestataire[]
    ): JsonApiResponseInterface<Prestataire[]> {
        const data: JsonApiData<Prestataire & JsonApiDataAttributes>[] =
            prestataires.map((prestataire: Prestataire) => ({
                attributes: {
                    _kind: PrestataireKind.ACTIF,
                    prestataire,
                },
                type: 'urn:api:sinistres:prestataire:states:prestataire-states',
                id: 'test',
                relationships: {},
            })) as unknown as JsonApiData<Prestataire & JsonApiDataAttributes>[]

        return {
            data,
            meta: {
                totalPages: 2,
                totalRecords: 11,
                page: {
                    number: 0,
                    size: PageSize.TEN_ELEMENTS,
                },
            },
        }
    }

    static toPrestataireApiResult(
        prestataire: Prestataire
    ): JsonApiResponseInterface<Prestataire> {
        return {
            data: {
                attributes: {
                    _kind: PrestataireKind.ACTIF,
                    prestataire,
                },
                type: 'urn:api:sinistres:prestataire:states:prestataire-states',
                id: 'test',
                relationships: {},
            } as unknown as JsonApiData<Prestataire & JsonApiDataAttributes>,
        }
    }

    find(
        params: HttpParams
    ): Observable<JsonApiResponseInterface<Prestataire[]>> {
        return of(
            PrestataireWrapperMock.toPrestataireApiResultArray([
                prestataireExpertHabitationMock(),
            ])
        )
    }

    findOne(
        idPrestataire: string
    ): Observable<JsonApiResponseInterface<Prestataire>> {
        return of(
            PrestataireWrapperMock.toPrestataireApiResult(
                prestataireExpertHabitationMock()
            )
        )
    }
}
