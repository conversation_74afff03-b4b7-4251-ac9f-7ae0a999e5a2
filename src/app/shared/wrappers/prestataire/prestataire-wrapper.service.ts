import { Injectable } from '@angular/core'
import { HttpClient, HttpParams } from '@angular/common/http'
import { Observable } from 'rxjs'
import { environment } from '../../../../environments/environment'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { Prestataire } from '../../models/prestataire.model'

@Injectable({
    providedIn: 'root',
})
export class PrestataireWrapper {
    private readonly apiUrl: string

    constructor(private readonly http: HttpClient) {
        this.apiUrl = `${environment.sinistreMarcheBelgeExternalApiUrl}prestataire`
    }

    find(
        params: HttpParams
    ): Observable<JsonApiResponseInterface<Prestataire[]>> {
        const url = `${this.apiUrl}`
        return this.http.get<JsonApiResponseInterface<Prestataire[]>>(url, {
            params,
        })
    }

    findOne(
        idPrestataire: string
    ): Observable<JsonApiResponseInterface<Prestataire>> {
        return this.http.get<JsonApiResponseInterface<Prestataire>>(
            `${this.apiUrl}/${idPrestataire}`
        )
    }
}
