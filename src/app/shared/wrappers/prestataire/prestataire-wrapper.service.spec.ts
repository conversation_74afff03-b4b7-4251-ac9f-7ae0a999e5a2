import { TestBed } from '@angular/core/testing'

import { PrestataireWrapper } from './prestataire-wrapper.service'
import { PrestataireWrapperMock } from './prestataire-wrapper.service.spec.mock'
import { HttpParams } from '@angular/common/http'
import {
    HttpClientTestingModule,
    HttpTestingController,
} from '@angular/common/http/testing'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { Prestataire } from '../../models/prestataire.model'
import { prestataireExpertHabitationMock } from '../../mocks/prestataires.mock'
import { PageSize } from '../../enums/page-size.enum'
import { environment } from '../../../../environments/environment'

describe('PrestataireWrapperService', () => {
    let service: PrestataireWrapper
    let httpMock: HttpTestingController
    beforeEach(() => {
        TestBed.configureTestingModule({
            imports: [HttpClientTestingModule],
        })
        service = TestBed.inject(PrestataireWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    it('should make a call to find API with the right query parameters', (done) => {
        const expectedDataFromApi: JsonApiResponseInterface<Prestataire[]> =
            PrestataireWrapperMock.toPrestataireApiResultArray([
                prestataireExpertHabitationMock(),
            ])
        const params = new HttpParams()
            .set('page[number]', 1)
            .set('page[size]', PageSize.FIVE_ELEMENTS)
            .set('sort', '-numeroPersonne')
        service.find(params).subscribe((result) => {
            expect(result).toEqual(expectedDataFromApi)
            done()
        })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}prestataire?page%5Bnumber%5D=1&page%5Bsize%5D=5&sort=-numeroPersonne`
        )
        expect(testRequest.request.method).toBe('GET')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })

    it('should make a call to findOne API with the right parameters', (done) => {
        const idPrestataire = '123456'
        const expectedDataFromApi: JsonApiResponseInterface<Prestataire> =
            PrestataireWrapperMock.toPrestataireApiResult(
                prestataireExpertHabitationMock()
            )

        service.findOne(idPrestataire).subscribe((result) => {
            expect(result).toEqual(expectedDataFromApi)
            done()
        })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}prestataire/${idPrestataire}`
        )
        expect(testRequest.request.method).toBe('GET')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })
})
