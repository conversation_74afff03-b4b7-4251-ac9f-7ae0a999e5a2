import { Observable, of } from 'rxjs'
import { HttpParams } from '@angular/common/http'
import { PageSize } from '../../enums/page-size.enum'
import { DeclarationStateKind } from '../../enums/declaration-state-kind.enum'
import { TypeGaranties } from '../../enums/type-garanties.enum'
import { DeclarationKind } from '../../enums/declaration-kind.enum'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { JsonApiData } from '../../models/api/json-api-data'
import { JsonApiDataAttributes } from '../../models/api/json-api-data-attributes'
import { DeclarationResult } from '../../models/declaration-result'
import { DeclarationSoumiseResult } from '../../models/declaration-soumise-result'
import { Declaration } from '../../models/declaration'
import { DeclarationHabitation } from '../../../habitation/models/declaration-habitation'
import { DeclarationAuto } from '../../../auto/models/declaration-auto'
import { CauseSinistre } from '../../../auto/enums/cause-sinistre.enum'
import { garageManuelMock } from '../../mocks/prestataires.mock'
import { Circonstance } from '../../enums/circonstance.enum'

export const declarationHabitationMock: DeclarationHabitation = {
    _kind: DeclarationKind.HABITATION,
    numeroRisque: '213546',
    survenance: {
        dateDeSurvenance: new Date('2023-10-26T06:00:00Z'),
    },
    garanties: [TypeGaranties.CNN, TypeGaranties.ACC],
    circonstance:{
        circonstance: Circonstance.GRELE
    }
}

export const declarationAutoMock: DeclarationAuto = {
    _kind: DeclarationKind.AUTO,
    numeroRisque: '213546',
    survenance: {
        dateDeSurvenance: new Date('2024-10-26T06:00:00Z'),
    },
    causeSinistre: CauseSinistre.VANDALISME,
    garage: garageManuelMock(),
    circonstance:{
        circonstance: Circonstance.GRELE
    }
}

export const declarationWithId: Declaration = {
    ...declarationHabitationMock,
    id: 'coconut',
}

export class DeclarationWrapperMock {
    static toDeclarationApiResultArray(
        declarations: Declaration[]
    ): JsonApiResponseInterface<Declaration[]> {
        const data: JsonApiData<Declaration & JsonApiDataAttributes>[] =
            declarations.map((decla: Declaration) => ({
                attributes: {
                    _kind: DeclarationStateKind.CREE,
                    declaration: decla,
                    updatedAt: '2023-10-16T05:26:52.101Z',
                },
                type: 'urn:api:sinistres:declaration:states:declaration-states',
                id: 'test',
                relationships: {},
            })) as unknown as JsonApiData<Declaration & JsonApiDataAttributes>[]

        return {
            data,
            meta: {
                totalPages: 2,
                totalRecords: 11,
                page: {
                    number: 0,
                    size: PageSize.TEN_ELEMENTS,
                },
            },
        }
    }

    static toDeclarationApiResult(
        declaration: Declaration
    ): JsonApiResponseInterface<DeclarationResult> {
        return {
            data: {
                attributes: {
                    _kind: 'urn:api:sinistres:declaration:events:declaration-cree-event',
                    declaration,
                    _subject: 'usr:deu',
                    at: '2023-10-16T05:26:52.101596200Z',
                    by: 'usr:deu',
                    _ts: '2023-10-16T05:26:52.103578100Z',
                },
                id: '7875ead9-37cd-4aa7-ae29-a1e65f1e76f8',
                type: 'urn:api:sinistres:declaration:events:declaration-events',
                relationships: {
                    _entity: {
                        data: {
                            type: 'urn:api:sinistres:declaration:states:declaration-states',
                            id: 'ab384a9c-c420-4a8a-b2b4-6bd9aebd5ede',
                        },
                    },
                },
            },
        }
    }

    static toDeclarationSoumiseApiResult(
        declaration: Declaration
    ): JsonApiResponseInterface<DeclarationSoumiseResult> {
        return {
            data: {
                attributes: {
                    _kind: 'urn:api:sinistres:declaration:events:declaration-soumise-event',
                    declaration,
                    numeroDossierSinistre: '2308000',
                    _subject: 'usr:tio',
                    at: '2023-11-10T12:56:14.039584600Z',
                    by: 'usr:deu',
                    _ts: '2023-11-10T12:56:14.039584600Z',
                },
                id: 'a9be2802-f16c-4bf7-87a7-f841d7b618ef',
                type: 'urn:api:sinistres:declaration:events:declaration-events',
                relationships: {
                    _entity: {
                        data: {
                            type: 'urn:api:sinistres:declaration:states:declaration-states',
                            id: 'ab384a9c-c420-4a8a-b2b4-6bd9aebd5ede',
                        },
                    },
                },
            },
        }
    }

    creer(
        declaration: Declaration
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        return of(DeclarationWrapperMock.toDeclarationApiResult(declaration))
    }

    modifier(
        declaration: Declaration
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        return of(DeclarationWrapperMock.toDeclarationApiResult(declaration))
    }

    find(
        params: HttpParams
    ): Observable<JsonApiResponseInterface<Declaration[]>> {
        return of(
            DeclarationWrapperMock.toDeclarationApiResultArray([
                declarationHabitationMock,
            ])
        )
    }

    abandonnerDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        return of(
            DeclarationWrapperMock.toDeclarationApiResult(
                declarationHabitationMock
            )
        )
    }

    soumettreDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        const declarationSoumiseMock = {
            ...declarationHabitationMock,
            numeroDossierSinistre: '23080001',
        }
        return of(
            DeclarationWrapperMock.toDeclarationApiResult(
                declarationSoumiseMock
            )
        )
    }
}
