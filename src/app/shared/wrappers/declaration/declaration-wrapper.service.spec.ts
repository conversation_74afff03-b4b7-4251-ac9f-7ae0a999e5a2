import { TestBed } from '@angular/core/testing'

import { DeclarationWrapper } from './declaration-wrapper.service'
import {
    HttpTestingController,
    provideHttpClientTesting,
} from '@angular/common/http/testing'
import { provideHttpClient } from '@angular/common/http'
import {
    declarationWithId,
    DeclarationWrapperMock,
} from './declaration-wrapper.service.mock'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { DeclarationResult } from '../../models/declaration-result'
import { environment } from '../../../../environments/environment'
import { DeclarationSoumiseResult } from '../../models/declaration-soumise-result'

describe('DeclarationWrapperService', () => {
    let service: DeclarationWrapper
    let httpMock: HttpTestingController

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [provideHttpClient(), provideHttpClientTesting()],
        })
        service = TestBed.inject(DeclarationWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    it('should make a call to creer API with the right parameters', (done) => {
        const expectedDataFromApi: JsonApiResponseInterface<DeclarationResult> =
            DeclarationWrapperMock.toDeclarationApiResult(declarationWithId)

        service.creer(declarationWithId).subscribe((result) => {
            expect(result).toEqual(expectedDataFromApi)
            done()
        })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}declaration/commands/creer`
        )
        expect(testRequest.request.method).toBe('POST')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })

    it('should make a call to modifier API with the right parameters', (done) => {
        const expectedDataFromApi: JsonApiResponseInterface<DeclarationResult> =
            DeclarationWrapperMock.toDeclarationApiResult(declarationWithId)

        service.modifier(declarationWithId).subscribe((result) => {
            expect(result).toEqual(expectedDataFromApi)
            done()
        })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}declaration/coconut/commands/modifier`
        )
        expect(testRequest.request.method).toBe('PUT')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })

    it('should make a call to soumettre API with the right parameters', (done) => {
        const expectedDataFromApi: JsonApiResponseInterface<DeclarationSoumiseResult> =
            DeclarationWrapperMock.toDeclarationSoumiseApiResult(
                declarationWithId
            )

        service.soumettreDeclaration('coconut').subscribe((result) => {
            expect(result).toEqual(expectedDataFromApi)
            done()
        })

        const testRequest = httpMock.expectOne(
            `${environment.sinistreMarcheBelgeExternalApiUrl}declaration/coconut/commands/soumettre`
        )
        expect(testRequest.request.method).toBe('PUT')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })
})
