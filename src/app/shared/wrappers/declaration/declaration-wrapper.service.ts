import { Injectable } from '@angular/core'
import { Observable } from 'rxjs'
import { HttpClient, HttpParams } from '@angular/common/http'
import { environment } from 'src/environments/environment'
import { Declaration } from '../../models/declaration'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { DeclarationResult } from '../../models/declaration-result'
import { DeclarationSoumiseResult } from '../../models/declaration-soumise-result'

@Injectable({
    providedIn: 'root',
})
export class DeclarationWrapper {
    private readonly apiUrl: string

    constructor(private readonly http: HttpClient) {
        this.apiUrl = environment.sinistreMarcheBelgeExternalApiUrl
    }

    creer(
        declaration: Declaration
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        const url = `${this.apiUrl}declaration/commands/creer`
        return this.http.post<JsonApiResponseInterface<DeclarationResult>>(
            url,
            { declaration }
        )
    }

    modifier(
        declaration: Declaration
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        const url = `${this.apiUrl}declaration/${declaration.id}/commands/modifier`
        return this.http.put<JsonApiResponseInterface<DeclarationResult>>(url, {
            declaration,
        })
    }

    find(
        params: HttpParams
    ): Observable<JsonApiResponseInterface<Declaration[]>> {
        const url = `${this.apiUrl}declaration/`
        return this.http.get<JsonApiResponseInterface<Declaration[]>>(url, {
            params,
        })
    }

    abandonnerDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationResult>> {
        const url = `${this.apiUrl}declaration/${id}/commands/abandonner`
        return this.http.put<JsonApiResponseInterface<DeclarationResult>>(
            url,
            {}
        )
    }

    soumettreDeclaration(
        id: string
    ): Observable<JsonApiResponseInterface<DeclarationSoumiseResult>> {
        const url = `${this.apiUrl}declaration/${id}/commands/soumettre`
        return this.http.put<
            JsonApiResponseInterface<DeclarationSoumiseResult>
        >(url, {})
    }
}
