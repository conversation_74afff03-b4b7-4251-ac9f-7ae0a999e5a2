import { Observable, of } from 'rxjs'
import { SituationContratRisque } from './models/situation-contrat.risque'
import { EtatGarantie } from '../../enums/etat-garantie.enum'
import {
    REFERENTIEL_ADRESSES_KIND,
    ReferentielAdresse,
} from '../personne-wrapper/models/personne/referentiel-adresse'
import { personnePhysiqueDataMock } from '../../mocks/personne.mock'
import { TypeRisque } from '../../enums/type-risque.enum'
import { FamilleProduit } from '../../enums/famille-produit.enum'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'

const DATE_EFFET_STRING = '2003-06-01T22:00:00Z'
const adresseId = '2b822b18-0122-40a5-0000-000048185b24'

export const SITUATION_CONTRANT_RISQUE_ADRESSE_MOCK_ATTRIBUTES: ReferentielAdresse =
    {
        _kind: 'urn:api:referentiel:adresses:lu',
        identifiantAdresse: '2b822b18-0122-40a5-0000-000048185b2',
        rue: "ROUTE D'ECH",
        numeroRue: '237',
        localite: 'LUXEMBOURG',
        label: '',
        adresseFormatee: '',
        codePostal: '1471',
        pays: {
            code: 'LU',
            label: 'Luxembourg',
        },
    }

export const SITUATION_CONTRAT_RISQUE_SITUATION_MOCK = {
    attributes: {
        conditionsGenerales: {
            id: 'CGRHB0314F',
            href: 'http://basedoc-b.lefoyer.lu/cg.php?application=api-contrat&ref=CGRHB0314F',
        },
    },
    id: '10001237-17',
    type: 'urn:api:contrats:situation:vue',
    relationships: {},
    links: {
        self: 'http://foyer-api-situations-contrats-b.foyer.cloud/situations-contrats/20270875-13',
    },
}

export const SITUATION_CONTRAT_RISQUE_ADRESSE_MOCK = {
    type: REFERENTIEL_ADRESSES_KIND,
    id: adresseId,
    attributes: SITUATION_CONTRANT_RISQUE_ADRESSE_MOCK_ATTRIBUTES,
    relationships: {
        referenceAdresseFoyer: {
            links: {
                related:
                    'http://foyer-api-adresse-u.foyer.cloud/adresse-foyer/2b822b18-0122-40a5-0000-000048185b24',
            },
            data: {
                type: 'urn:api:referentiel:adresses:foyer',
                id: adresseId,
            },
        },
        referenceAdresseLu: {
            links: {
                related:
                    'http://foyer-api-adresse-u.foyer.cloud/referentiel-adresse-lu/T22113',
            },
            data: {
                type: 'urn:api:referentiel:adresses:lu',
                id: 'T22113',
            },
        },
    },
    links: {
        self: 'http://foyer-api-adresse-u.foyer.cloud/adresses/2b822b18-0122-40a5-0000-000048185b24',
    },
}

export const SITUATION_CONTRANT_RISQUE_ATTRIBUTES: SituationContratRisque = {
    id: '',
    hasPertesIndirectes: false,
    _kind: 'urn:api:contrats:risque-habitation:vue-gestionnaire',
    clauses: [
        {
            clause: {
                code: 'BN056',
                label: '',
            },
        },
        {
            clause: {
                code: 'BN010',
                label: '',
            },
        },
        {
            clause: {
                code: 'BN033',
                label: '',
            },
        },
    ],
    etatRisque: {
        code: 'V',
        label: 'En Vigueur',
    },
    typeRisque: {
        code: TypeRisque.HABOC,
        label: 'Habitation occupant',
    },
    typeImmeuble: {
        code: 'M',
        label: 'Maison',
    },
    adresse: adresseId,
    valeursAssurees: [],
    biensAssures: {
        code: 'B',
        label: 'Bâtiment seul',
    },
    preneur: '1270757',
    idSituation: '10001237-17',
    typeResidence: {
        code: 'P',
        label: 'Principale',
    },
    numeroPolice: 10001237,
    qualitePreneur: {
        code: 'P',
        label: '',
    },
    produit: 'RB00',
    produitLabel: {
        code: 'RB00',
        label: 'Enjoy home insurance',
    },
    datePremierEffet: '2003-06-01T22:00:00Z',
    garanties: [
        {
            numeroOrdre: 1,
            etatGarantie: {
                code: 'V',
                label: EtatGarantie.EN_VIGUEUR,
            },
            dateEffet: DATE_EFFET_STRING,
            franchises: [],
            garantie: {
                code: 'INCN',
                label: 'Incendie et risques connexes',
            },
        },
        {
            dateEffet: DATE_EFFET_STRING,

            numeroOrdre: 4,
            etatGarantie: {
                code: 'V',
                label: EtatGarantie.EN_VIGUEUR,
            },
            franchises: [],
            garantie: {
                code: 'TGN',
                label: 'Tempête, grêle, neige',
            },
        },
        {
            dateEffet: DATE_EFFET_STRING,
            numeroOrdre: 7,
            etatGarantie: {
                code: 'V',
                label: EtatGarantie.EN_VIGUEUR,
            },
            franchises: [],
            garantie: {
                code: 'CNN',
                label: 'Catastrophes naturelles',
            },
        },
        {
            dateEffet: DATE_EFFET_STRING,

            numeroOrdre: 10,
            etatGarantie: {
                code: 'V',
                label: EtatGarantie.EN_VIGUEUR,
            },
            franchises: [],
            garantie: {
                code: 'ATTN',
                label: 'Attentats et conflits du travail',
            },
        },
        {
            numeroOrdre: 13,
            etatGarantie: {
                code: 'V',
                label: EtatGarantie.EN_VIGUEUR,
            },
            dateEffet: DATE_EFFET_STRING,
            franchises: [],
            garantie: {
                code: 'DGEN',
                label: 'Dégâts des eaux',
            },
        },
    ],
    franchises: [],
    familleProduit: FamilleProduit.RBBE,
}
export const SITUATION_CONTRAT_RISQUE_MOCK = {
    type: 'urn:api:contrats:risque',
    id: '10001237-17-1',
    attributes: SITUATION_CONTRANT_RISQUE_ATTRIBUTES,
    relationships: {
        idSituation: {
            links: {
                related:
                    'http://foyer-api-situations-contrats-b.foyer.cloud/situations-contrats/10001237-17',
            },
            data: {
                type: 'urn:api:contrats',
                id: '10001237-17',
            },
        },
        adresse: {
            links: {
                related:
                    'http://foyer-api-adresse-b.foyer.cloud/adresses/2b822b18-0122-40a5-0000-000048185b24',
            },
            data: {
                type: 'urn:api:referentiel:adresse',
                id: adresseId,
            },
        },
        intermediairePrincipal: {
            links: {
                related:
                    'http://foyer-api-referentiel-intermediaires-b.foyer.cloud/intermediaires/2668',
            },
            data: {
                type: 'urn:api:referentiel:intermediaires:intermediaire',
                id: '2668',
            },
        },
    },
}
export const SITUATION_CONTRAT_RISQUE_ARRAY_JSON_API_MOCK = {
    data: [SITUATION_CONTRAT_RISQUE_MOCK],
    included: [
        SITUATION_CONTRAT_RISQUE_ADRESSE_MOCK,
        personnePhysiqueDataMock(),
    ],
} as unknown as JsonApiResponseInterface<SituationContratRisque[]>

export const SITUATION_CONTRANT_RISQUE_JSON_API_MOCK = {
    data: SITUATION_CONTRAT_RISQUE_MOCK,
    included: [
        SITUATION_CONTRAT_RISQUE_ADRESSE_MOCK,
        SITUATION_CONTRAT_RISQUE_SITUATION_MOCK,
    ],
} as unknown as JsonApiResponseInterface<SituationContratRisque>
export class SituationsContratsWrapperServiceSpecMock {
    findSituationsContratsRisques(
        contrat: string,
        dateRecherche: Date
    ): Observable<JsonApiResponseInterface<SituationContratRisque[]>> {
        return of(SITUATION_CONTRAT_RISQUE_ARRAY_JSON_API_MOCK)
    }

    getSituationContratRisque(
        idRisque: string
    ): Observable<JsonApiResponseInterface<SituationContratRisque>> {
        return of(SITUATION_CONTRANT_RISQUE_JSON_API_MOCK)
    }
}
