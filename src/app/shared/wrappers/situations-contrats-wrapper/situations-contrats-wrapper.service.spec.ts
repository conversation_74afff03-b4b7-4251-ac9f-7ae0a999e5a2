import { TestBed } from '@angular/core/testing'

import { SituationsContratsWrapper } from './situations-contrats-wrapper.service'
import {
    HttpTestingController,
    provideHttpClientTesting,
} from '@angular/common/http/testing'
import { SituationContratRisque } from './models/situation-contrat.risque'
import { provideHttpClient } from '@angular/common/http'
import { environment } from '../../../../environments/environment'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'

describe('SituationsContratsWrapper', () => {
    let service: SituationsContratsWrapper
    let httpMock: HttpTestingController

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [provideHttpClient(), provideHttpClientTesting()],
        })
        service = TestBed.inject(SituationsContratsWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    it('should make a call to API with the right parameters for date and contrat', (done) => {
        const expectedDataFromApi: JsonApiResponseInterface<
            SituationContratRisque[]
        > = { data: [] }
        const dateRecherche = new Date(2022, 8, 15)
        const numeroPolice = '123456'
        service
            .findSituationsContratsRisques(numeroPolice, dateRecherche)
            .subscribe((result) => {
                expect(result).toEqual(expectedDataFromApi)
                done()
            })

        const testRequest = httpMock.expectOne(
            `${environment.situationContratApiUrl}risques?filter[dateRecherche]=2022-09-15&filter[contrat]=123456&include=idSituation,adresse`
        )
        expect(testRequest.request.method).toBe('GET')
        testRequest.flush(expectedDataFromApi)
        httpMock.verify()
    })
})
