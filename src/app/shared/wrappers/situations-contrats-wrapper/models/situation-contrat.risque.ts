import { SituationContratGarantie } from './situation-contrat.garantie'
import { SituationContratValeurAssuree } from './situation-contrat.valeur-assuree'
import { SituationContratFranchise } from './situation-contrat.franchise'
import { SituationContratClause } from './situation-contrat.clause'
import { Personne } from '../../personne-wrapper/models/personne/personne'
import { ReferentielAdresse } from '../../personne-wrapper/models/personne/referentiel-adresse'
import { CodeLabel } from '../../../models/code-label'

export interface SituationContratRisque {
    _kind: string
    preneur: string
    id: string
    produit: string
    produitLabel: CodeLabel
    biensAssures: CodeLabel // Batiment et contenu
    etatRisque: CodeLabel // status
    typeRisque: CodeLabel
    typeImmeuble: CodeLabel // typeImmeuble pour l'icone
    typeResidence: CodeLabel
    qualitePreneur: CodeLabel
    idSituation: string //numeroRisque
    numeroPolice: number
    familleProduit: string //code -> nomProduit
    datePremierEffet: string
    adresse?: string //id de l'adresse dans l'api adresse
    garanties: SituationContratGarantie[]
    franchises: SituationContratFranchise[]
    hasPertesIndirectes: boolean
    valeursAssurees: SituationContratValeurAssuree[]
    clauses: SituationContratClause[]
    fullAdresse?: ReferentielAdresse
    fullPreneur?: Personne
    nombreDePieces?: number
    clausesLibres?: SituationContratClause[]
    formule?: CodeLabel
    idConditionsGenerales?: string
    plaque?: string
    conducteurPrincipal?: number
    conducteursSecondaires?: number[]
    marque?: string
    modele?: string
}
