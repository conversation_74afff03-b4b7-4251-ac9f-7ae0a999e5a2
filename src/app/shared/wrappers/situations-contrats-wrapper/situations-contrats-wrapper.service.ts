import { Injectable } from '@angular/core'
import { HttpClient } from '@angular/common/http'
import { Observable, shareReplay } from 'rxjs'
import { SituationContratRisque } from './models/situation-contrat.risque'
import { Language } from '../../enums/language.enum'
import { JsonApiResponseInterface } from '../../models/api/json-api-response-interface'
import { environment } from '../../../../environments/environment'
import { formatDateToDashedYYYYMMDD } from '../../utils/date.utils'
import { getOrFail } from '../../utils/objects.utils'

@Injectable({
    providedIn: 'root',
})
export class SituationsContratsWrapper {
    private readonly apiUrl: string
    private readonly cache: Map<
        string,
        Observable<JsonApiResponseInterface<SituationContratRisque>>
    > = new Map()

    constructor(private readonly http: HttpClient) {
        this.apiUrl = `${environment.situationContratApiUrl}`
    }

    findSituationsContratsRisques(
        contrat: string,
        dateRecherche: Date
    ): Observable<JsonApiResponseInterface<SituationContratRisque[]>> {
        const url = `${
            this.apiUrl
        }risques?filter[dateRecherche]=${formatDateToDashedYYYYMMDD(
            dateRecherche
        )}&filter[contrat]=${contrat}&include=idSituation,adresse`
        return this.http.get<
            JsonApiResponseInterface<SituationContratRisque[]>
        >(url)
    }

    getSituationContratRisque(
        idRisque: string,
        language: Language
    ): Observable<JsonApiResponseInterface<SituationContratRisque>> {
        const risqueCacheKey = SituationsContratsWrapper.getRisqueCacheKey(
            language,
            idRisque
        )

        if (!this.cache.has(risqueCacheKey)) {
            const url = `${this.apiUrl}risques/${idRisque}?include=adresse,idSituation,preneur`
            const situationObservable = this.http
                .get<JsonApiResponseInterface<SituationContratRisque>>(url)
                .pipe(shareReplay(1))
            this.cache.set(risqueCacheKey, situationObservable)
        }
        return getOrFail(
            this.cache.get(risqueCacheKey),
            `missing risque cache record ${risqueCacheKey}`
        )
    }

    private static getRisqueCacheKey(language: Language, idRisque: string) {
        return `${language}${idRisque}`
    }
}
