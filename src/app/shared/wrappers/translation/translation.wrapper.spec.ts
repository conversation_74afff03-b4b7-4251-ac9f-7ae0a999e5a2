import { TestBed } from '@angular/core/testing'
import {
    HttpTestingController,
    provideHttpClientTesting,
} from '@angular/common/http/testing'
import { timer } from 'rxjs'
import { TranslationWrapper } from './translation.wrapper'
import { TranslationData } from './models/translation-data.model'
import { Genre } from './models/genre.enum'
import { TranslationReference } from './models/translation-reference'
import { provideHttpClient } from '@angular/common/http'
import { environment } from '../../../../environments/environment'

describe('TranslationWrapper', () => {
    let httpMock: HttpTestingController
    let service: TranslationWrapper

    beforeEach(() => {
        TestBed.configureTestingModule({
            providers: [provideHttpClient(), provideHttpClientTesting()],
        })
        service = TestBed.inject(TranslationWrapper)
        httpMock = TestBed.inject(HttpTestingController)
    })

    it('should return the same result twice for getListe Produit calls, without executing new requests', (done) => {
        const expectedResult: TranslationData = {
            name: TranslationReference.PRODUIT,
            translations: [
                {
                    label: 'Mon super produit',
                    code: 'MSP',
                    genre: Genre.NEUTRE,
                },
            ],
        }

        service.getList(TranslationReference.PRODUIT).subscribe((result) => {
            expect(result).toEqual(expectedResult)
            timer(10).subscribe(() => {
                service
                    .getList(TranslationReference.PRODUIT)
                    .subscribe((result2) => {
                        expect(result2).toEqual(expectedResult)
                        done()
                    })
            })
        })

        const testRequest = httpMock.expectOne(
            `${environment.translationApiUrl}alex/CPRODUIT`
        )
        expect(testRequest.request.method).toBe('GET')
        testRequest.flush({ data: { attributes: expectedResult } })
        httpMock.verify()
    })
})
