import { Injectable } from '@angular/core'
import { map, Observable, shareReplay } from 'rxjs'
import { HttpClient, HttpHeaders } from '@angular/common/http'
import { TranslationData } from './models/translation-data.model'
import { TranslationReference } from './models/translation-reference'
import { environment } from 'src/environments/environment'
import { getOrFail } from '../../utils/objects.utils'

@Injectable({
    providedIn: 'root',
})
export class TranslationWrapper {
    private readonly translationApiUrl: string
    private readonly translationObservableMap: Map<
        TranslationReference,
        Observable<TranslationData>
    > = new Map()

    constructor(private readonly http: HttpClient) {
        this.translationApiUrl = `${environment.translationApiUrl}`
    }

    getList(
        translationReference: TranslationReference
    ): Observable<TranslationData> {
        if (!this.translationObservableMap.has(translationReference)) {
            const translationObservable =
                this.getTranslation(translationReference)
            this.translationObservableMap.set(
                translationReference,
                translationObservable
            )
        }
        return getOrFail(
            this.translationObservableMap.get(translationReference),
            'translationObservableMap'
        )
    }

    private getTranslation(
        id: TranslationReference
    ): Observable<TranslationData> {
        const headers: HttpHeaders = new HttpHeaders().set(
            'Accept-Language',
            'fr'
        )
        return this.http
            .get<any>(`${this.translationApiUrl}alex/${id}`, { headers })
            .pipe(
                map((result) => result.data.attributes as TranslationData),
                shareReplay(1)
            )
    }
}
