import { Observable, of } from 'rxjs'
import { Translation } from './models/translation.model'
import { Genre } from './models/genre.enum'
import { TranslationData } from './models/translation-data.model'
import { TranslationReference } from './models/translation-reference'

export class TranslationWrapperMock {
    produitTranslations: Translation[] = [
        {
            label: 'MOBILE REGIO 2008',
            code: 'B08',
            genre: Genre.NEUTRE,
        },
    ]

    getList(
        translationReference: TranslationReference
    ): Observable<TranslationData> {
        return of({
            name: TranslationReference.PRODUIT,
            translations: this.produitTranslations,
        })
    }
}
