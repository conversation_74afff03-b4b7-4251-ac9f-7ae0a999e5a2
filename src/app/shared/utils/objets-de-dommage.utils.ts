import { TypeGaranties } from '../enums/type-garanties.enum'
import { ObjetsDeDommage } from '../../habitation/enums/objets-de-dommage.enum'

export const OBJETS_DOMMAGE_BY_GARANTIE: Map<TypeGaranties, ObjetsDeDommage[]> =
    new Map<TypeGaranties, ObjetsDeDommage[]>([
        [
            TypeGaranties.VOLN,
            [
                ObjetsDeDommage.BIJOUX,
                ObjetsDeDommage.DEGRADATION_IMMOBILIERE,
                ObjetsDeDommage.MOBILIER,
                ObjetsDeDommage.OBJETS_DE_VALEUR,
            ],
        ],
        [
            TypeGaranties.DELN,
            [
                ObjetsDeDommage.MACHINE_A_LAVER,
                ObjetsDeDommage.CHAUDIERE,
                ObjetsDeDommage.LAVE_VAISSELLE,
                ObjetsDeDommage.SECHE_LINGE,
                ObjetsDeDommage.PLAQUE_DE_CUISSON,
                ObjetsDeDommage.REFRIGERATEUR,
                ObjetsDeDommage.TELEVISION,
            ],
        ],
        [
            TypeGaranties.BDGN,
            [
                ObjetsDeDommage.VITRES,
                ObjetsDeDommage.PORTES_VITREE,
                ObjetsDeDommage.PLAQUE_VITROCERAMIQUE_OU_INDUCTION,
            ],
        ],
    ])
