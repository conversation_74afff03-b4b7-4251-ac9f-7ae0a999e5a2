import { calculerDistanceInKm } from './distance.utils'

describe('DistanceUtils', () => {
    describe('calculateDistanceInKm', () => {
        it('should return correct distance between two distant points', () => {
            const distance = calculerDistanceInKm(
                2.3522,
                48.8566,
                -0.1276,
                51.5074
            )
            expect(distance).toEqual(343.55)
        })

        it('should return zero when points are identical', () => {
            const distance = calculerDistanceInKm(
                2.3522,
                48.8566,
                2.3522,
                48.8566
            )
            expect(distance).toEqual(0)
        })

        it('should calculate maximum possible distance on Earth', () => {
            const distance = calculerDistanceInKm(0, 0, 180, 0)
            expect(distance).toEqual(20015.09)
        })
    })
})
