import { PersonneTitre } from '../enums/personne-titre.enum'
import { ObjetsDeDommage } from '../../habitation/enums/objets-de-dommage.enum'
import { CauseSinistre } from '../../auto/enums/cause-sinistre.enum'
import { TypeResultat } from '../../auto/enums/type-resultat.enum'

export const DEFAULT_GENERIC_PERSONNE_PHYSIQUE_ICONE =
    'https://static.foyer.lu/images/30/independant.309bbebb392231da9ecd46211038eafac96eac12.svg'

export const DEFAULT_GENERIC_PERSONNE_MORALE_ICONE =
    'https://static.foyer.lu/images/45/moralcompany.45288beda832e4c4ae19a53d87666288042871d6.svg'

export const YOUNG_MAN_ICON =
    'https://static.foyer.lu/images/99/avatarboy.99f29e601c1808f89af891fa7481a64b1ca24e9b.svg'

export const YOUNG_WOMAN_ICON =
    'https://static.foyer.lu/images/8a/womantannedskin.8a35773c2292ef79c0e560f9d151fc870ec0e26c.svg'

export const ICON_BY_TITRE_PERSONNE_PHYSIQUE: Map<PersonneTitre, string> =
    new Map<PersonneTitre, string>([
        [
            PersonneTitre.MONSIEUR,
            'https://static.foyer.lu/images/af/manlightskin1.af2d72b5b7bbag1357a146208629fffb6971d67c.svg',
        ],
        [
            PersonneTitre.MESSIEURS,
            'https://static.foyer.lu/images/a5/duomen.a5746fb773469ba8b128ed7c4940db9a17088dd4.svg',
        ],
        [
            PersonneTitre.MADAME,
            'https://static.foyer.lu/images/06/womanlightskin2.060e716f2cf49f6a677a0328fe922178cc84d5d5.svg',
        ],
        [
            PersonneTitre.MESDAMES,
            'https://static.foyer.lu/images/9f/twowomen.9f70c5ebaa84f712997c974c029f6166f6e2a0ca.svg',
        ],
        [
            PersonneTitre.MONSIEUR_ET_MADAME,
            'https://static.foyer.lu/images/24/duopeoples.244cf558040d090be343731d571cd7712e03a347.svg',
        ],
        [
            PersonneTitre.VIDE,
            'https://static.foyer.lu/images/ba/independantunknow.ba1e7745bc58bcd17c2149716fd6921063821255.svg',
        ],
    ])

export const ICON_BY_OBJET_DOMMAGE: Map<ObjetsDeDommage, string> = new Map<
    ObjetsDeDommage,
    string
>([
    [ObjetsDeDommage.BIJOUX, 'gi-jewel'],
    [ObjetsDeDommage.DEGRADATION_IMMOBILIERE, 'gi-housing-wear-tear'],
    [ObjetsDeDommage.MOBILIER, 'gi-furnitures'],
    [ObjetsDeDommage.OBJETS_DE_VALEUR, 'gi-valuable-object'],
    [ObjetsDeDommage.MACHINE_A_LAVER, 'gi-washer'],
    [ObjetsDeDommage.CHAUDIERE, 'gi-furnace'],
    [ObjetsDeDommage.LAVE_VAISSELLE, 'gi-dishwasher'],
    [ObjetsDeDommage.SECHE_LINGE, 'gi-dryer'],
    [ObjetsDeDommage.PLAQUE_DE_CUISSON, 'gi-hob'],
    [ObjetsDeDommage.REFRIGERATEUR, 'gi-fridge'],
    [ObjetsDeDommage.TELEVISION, 'gi-television'],
    [ObjetsDeDommage.VITRES, 'gi-window'],
    [ObjetsDeDommage.PORTES_VITREE, 'gi-glassdoor'],
    [ObjetsDeDommage.PLAQUE_VITROCERAMIQUE_OU_INDUCTION, 'gi-hotplate'],
])

export const ICON_BY_GARAGE_SERACH_RESULT: Map<TypeResultat, string> = new Map<
    TypeResultat,
    string
>([
    [TypeResultat.GARAGE, 'gi-garage'],
    [TypeResultat.LOCALITE, 'mi-room'],
])

export const ICON_BY_CAUSE_SINISTRE: Map<CauseSinistre, string> = new Map<
    CauseSinistre,
    string
>([
    [
        CauseSinistre.COLLISION_ENTRE_VEHICULES,
        'https://static.foyer.lu/images/3E/collisionentrevehicules.3E3E4BB1424DD773B681FA23AAAE9098C7BDB920.svg',
    ],
    [
        CauseSinistre.COLLISION_AVEC_UN_OBJET_BATIMENT,
        'https://static.foyer.lu/images/9D/collisionavecunobstacleouunobjet.9D7DC5BAFD098893D8CB8F85503D2B4D2F146C25.svg',
    ],
    [
        CauseSinistre.BRIS_DE_VITRE,
        'https://static.foyer.lu/images/a8/brisdeglace.a87b8052c1abf70d474a05b49262ec7a04e735e2.svg',
    ],
    [
        CauseSinistre.VOL,
        'https://static.foyer.lu/images/46/voleteffraction.46B713D41961B633188E204C9E02CE4C01953182.svg',
    ],
    [
        CauseSinistre.VANDALISME,
        'https://static.foyer.lu/images/2B/vandalisme.2bd1cf21d2bb300940a08e7c593e8d1fafe0ef28.svg',
    ],
    [
        CauseSinistre.INCENDIE,
        'https://static.foyer.lu/images/0A/incendie.0A95FBF368C5D9CBC80549105446BDC641FD7176.svg',
    ],
    [
        CauseSinistre.DEGATS_CAUSES_PAR_UN_ANIMAL,
        'https://static.foyer.lu/images/32/collisionavecunanimal.32D890A81346C819229A02D160B5E89FEB7D82F8.svg',
    ],
    [
        CauseSinistre.COLLISION_AVEC_UNE_PERSONNE_OU_UN_VELO,
        'https://static.foyer.lu/images/92/collisionavecunpietonouuncycliste.9263DE39309AA9751B05EA4ECCE63208DF074D22.svg',
    ],
    [
        CauseSinistre.FORCE_DE_LA_NATURE,
        'https://static.foyer.lu/images/B0/forcedelanature.B000318BAB9FAC904E625F5779C7CD1B986CC049.svg',
    ],
])

export const MAP_MARKER =
    'https://static.foyer.lu/images/EA/icon-map-foyer.EA21A9D19732A98A4A642FCAG2F9C290376916C4.svg'
export const MAP_MARKER_ACTIVE =
    'https://static.foyer.lu/images/D1/icon-map-foyerblue.D1930C3F1C7DCC1140368B411F06FD50DC1996B1.svg'
