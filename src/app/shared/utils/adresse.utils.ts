import { filterUndefinedAndJoinValues, titleCase } from './string.utils'
import { ReferentielAdresse } from '../wrappers/personne-wrapper/models/personne/referentiel-adresse'
import { Adresse } from '../models/adresse'

const COMMA_GROUP_SEPARATOR = ', '
const SPACE_SEPARATOR = ' '

export const formatAdress = (
    rue: string | undefined,
    numeroRue: string | undefined,
    codePostal: string | undefined,
    localite: string | undefined,
    pays: string,
    groupSeparator: string
): string =>
    filterUndefinedAndJoinValues(
        [
            titleCase(
                filterUndefinedAndJoinValues([rue, numeroRue], SPACE_SEPARATOR)
            ),
            filterUndefinedAndJoinValues(
                [
                    filterUndefinedAndJoinValues(
                        [codePostal, titleCase(localite)],
                        SPACE_SEPARATOR
                    ),
                    pays.toUpperCase(),
                ],
                COMMA_GROUP_SEPARATOR
            ),
        ],
        groupSeparator
    )

export const formatAdresseFromAdresse = (
    adresse: Adresse,
    groupSeparator: string = ', '
): string => {
    const rue = adresse.nomRue
    const numeroRue = adresse.numeroRue
    const codePostal = adresse.codePostal
    const localite = adresse.localite
    const pays = adresse.pays ?? 'BE'
    return formatAdress(
        rue,
        numeroRue,
        codePostal,
        localite,
        pays,
        groupSeparator
    )
}

export const formatAdresseFromReferentiel = (
    adresse: ReferentielAdresse,
    groupSeparator: string = ', '
): string => {
    const rue = adresse.rue
    const numeroRue = adresse.numeroRue
    const codePostal = adresse.codePostal
    const localite = adresse.localite
    const pays = adresse.pays.label
    return formatAdress(
        rue,
        numeroRue,
        codePostal,
        localite,
        pays,
        groupSeparator
    )
}
