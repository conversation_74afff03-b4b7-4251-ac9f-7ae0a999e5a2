import {
    birthdayToAge,
    formatDateToDashedYYYYMMDD,
    formatDateToDottedDDMMYYYY,
    maxDateFilter,
    minDateFilter,
} from './date.utils'
import { addDays } from 'date-fns'

describe('DateUtils', () => {
    const now = new Date()
    const dateInFuture = addDays(now, 10)
    const dateInPast = addDays(now, -20)

    describe('formatDateToDashedYYYYMMDD', () => {
        it('should print date as yyyy-MM-dd', () => {
            const date = new Date(2020, 6, 11, 9, 15, 0)
            expect(formatDateToDashedYYYYMMDD(date)).toEqual('2020-07-11')
        })
    })

    describe('formatDateToDottedDDMMYYYY', () => {
        it('should print date as dd.MM.yyyy', () => {
            const date = new Date(2020, 6, 11, 9, 15, 0)
            expect(formatDateToDottedDDMMYYYY(date)).toEqual('11.07.2020')
        })
    })

    describe('maxDateFilter', () => {
        it('should return true when selected date is now', () => {
            expect(maxDateFilter(now)).toBeTrue()
        })

        it('should return true when selected date isBefore now', () => {
            expect(maxDateFilter(dateInPast)).toBeTrue()
        })

        it('should return false when selected date is after now', () => {
            expect(maxDateFilter(dateInFuture)).toBeFalse()
        })
    })

    describe('minDateFilter', () => {
        it('should return true when selected date is now', () => {
            expect(minDateFilter(now)).toBeFalse()
        })

        it('should return false when selected date isBefore now', () => {
            expect(minDateFilter(dateInPast)).toBeFalse()
        })

        it('should return true when selected date is after now', () => {
            expect(minDateFilter(dateInFuture)).toBeTrue()
        })
    })

    describe('birthdayToAge', () => {
        it('should return the age', () => {
            const month = now.getMonth() + 1
            const day = now.getDate()
            const year = now.getFullYear() - 10
            expect(
                birthdayToAge(
                    `${year}-${month.toString().padStart(2, '0')}-${day
                        .toString()
                        .padStart(2, '0')}`
                )
            ).toEqual(10)
        })

        it('should fail if a wrong format is given', () => {
            expect(() => {
                birthdayToAge(`01-01-1981`)
            }).toThrow(
                new Error(
                    'Wrong input for date of birth, YYYY-mm-dd format is expected.'
                )
            )
        })

        it('should fail if a date in the future is given', () => {
            const month = (now.getMonth() + 1).toString().padStart(2, '0')
            const day = now.getDate().toString().padStart(2, '0')
            const year = now.getFullYear() + 10
            expect(() => {
                birthdayToAge(`${year}-${month}-${day}`)
            }).toThrow(
                new Error('Cannot detect the age from a date in the future')
            )
        })
    })
})
