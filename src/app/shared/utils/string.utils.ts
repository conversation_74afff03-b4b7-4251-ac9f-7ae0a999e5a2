export const filterUndefinedAndJoinValues = (
    stringValuesToJoin: (string | undefined)[],
    separator: string
): string =>
    Object.values(stringValuesToJoin)
        .filter((value) => !!value)
        .join(separator)

export const titleCase = (input: string | undefined) => {
    if (input) {
        const lowerCasedString = input.toLowerCase()
        return lowerCasedString.replace(
            lowerCasedString[0],
            lowerCasedString[0].toUpperCase()
        )
    } else {
        return input
    }
}
