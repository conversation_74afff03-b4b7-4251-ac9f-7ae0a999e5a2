import { createPopupMessage } from './error-display.utils'
import { ErrorMessage } from '../models/error-message.model'
import { ErrorDisplay } from '../enums/error-display.enum'
import { ErrorLevel } from '../enums/error-type.enum'

describe('ErrorDisplayUtils', () => {
    describe('createPopupMessage', () => {
        it('should create error message object', () => {
            const errorMessage = createPopupMessage(
                'error-title',
                'error-content'
            )
            expect(errorMessage).toEqual({
                title: 'error-title',
                content: 'error-content',
                level: ErrorLevel.DANGER,
                display: ErrorDisplay.POPUP,
                actionCallback: undefined,
                actionLabel: 'Fermer',
            } as ErrorMessage)
        })
    })
})
