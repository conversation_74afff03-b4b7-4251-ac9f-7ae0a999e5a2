import {
    jsonAPIToPageable,
    MappingJsonAPIObjectToPageItemFunction,
    toSortQueryParam,
} from './pagination-utils'
import { JsonApiResponseInterface } from '../models/api/json-api-response-interface'
import { PageSize } from '../enums/page-size.enum'
import { SortDirection } from '../enums/sort-direction.enum'

interface SourceType {
    a: string
    b: string
}

interface TargetType {
    t: string
}

describe('PaginationUtils', () => {
    const mapper: MappingJsonAPIObjectToPageItemFunction<
        SourceType,
        TargetType
    > = (data) =>
        data.map((m) => ({ t: m.attributes.a + ' ' + m.attributes.b }))

    describe('jsonToPageable', () => {
        it('should transform the json api result into Pageable object', () => {
            const pageItemMapper: MappingJsonAPIObjectToPageItemFunction<
                SourceType,
                TargetType
            > = (data) =>
                data.map((m) => ({
                    t: m.attributes.a + ' ' + m.attributes.b,
                }))
            const jsonAPI: JsonApiResponseInterface<SourceType[]> = {
                data: [
                    {
                        type: 'sometype',
                        id: 'someid',
                        _kind: 'somekind',
                        attributes: {
                            a: 'Saul',
                            b: 'Goodman',
                        },
                        relationships: {},
                    },
                ],
                meta: {
                    totalPages: 1,
                    totalRecords: 1,
                    page: {
                        // eslint-disable-next-line id-blacklist
                        number: 0,
                        size: 5,
                    },
                    commands: {},
                },
            } as unknown as JsonApiResponseInterface<SourceType[]>
            const result = jsonAPIToPageable<SourceType, TargetType>(
                jsonAPI,
                pageItemMapper
            )

            expect(result.pageSize).toBe(PageSize.FIVE_ELEMENTS)
            expect(result.totalPages).toBe(1)
            expect(result.totalItems).toBe(1)
            expect(result.currentPage).toBe(0)
            expect(result.content.length).toBe(1)
            expect(result.content[0].t).toBe('Saul Goodman')
        })

        it('should return empty content when data is empty', () => {
            const jsonAPI = {
                data: [],
                meta: {
                    totalPages: 0,
                    totalRecords: 0,
                    page: { number: 0, size: 5 },
                    commands: {},
                },
            } as unknown as JsonApiResponseInterface<SourceType[]>

            const result = jsonAPIToPageable<SourceType, TargetType>(
                jsonAPI,
                mapper
            )
            expect(result.content).toEqual([])
            expect(result.totalItems).toBe(0)
            expect(result.currentPage).toBe(0)
            expect(result.totalPages).toBe(0)
        })

        it('should use defaults when meta is undefined', () => {
            const jsonAPI = {
                data: [
                    {
                        type: 'sometype',
                        id: '1',
                        _kind: 'k',
                        attributes: { a: 'X', b: 'Y' },
                        relationships: {},
                    },
                ],
            } as unknown as JsonApiResponseInterface<SourceType[]>

            const result = jsonAPIToPageable(jsonAPI, mapper)
            expect(result.totalItems).toBe(0)
            expect(result.currentPage).toBe(0)
            expect(result.totalPages).toBe(0)
            expect(result.pageSize).toBe('5')
        })

        it('should use default pageSize when meta.page.size is undefined', () => {
            const jsonAPI = {
                data: [
                    {
                        type: 'sometype',
                        id: '1',
                        _kind: 'k',
                        attributes: { a: 'X', b: 'Y' },
                        relationships: {},
                    },
                ],
                meta: {
                    totalPages: 1,
                    totalRecords: 1,
                    page: { number: 0 }, // size missing
                    commands: {},
                },
            } as unknown as JsonApiResponseInterface<SourceType[]>

            const result = jsonAPIToPageable(jsonAPI, mapper)
            expect(result.pageSize).toBe(PageSize.FIVE_ELEMENTS)
        })
    })

    describe('toSortQueryParam', () => {
        it('should return empty string when map is empty', () => {
            const sort = new Map<string, SortDirection>()
            expect(toSortQueryParam(sort)).toBe('')
        })

        it('should return key for ascending sort', () => {
            const sort = new Map([['name', SortDirection.ASCENDING]])
            expect(toSortQueryParam(sort)).toBe('name')
        })

        it('should return key with "-" prefix for descending sort', () => {
            const sort = new Map([['date', 'desc' as SortDirection]])
            expect(toSortQueryParam(sort)).toBe('date')
        })

        it('should handle multiple sort keys with mixed directions', () => {
            const sort = new Map([
                ['name', SortDirection.ASCENDING],
                ['date', SortDirection.DESCENDING],
                ['priority', SortDirection.ASCENDING],
            ])
            expect(toSortQueryParam(sort)).toBe('name,-date,priority')
        })

        it('should ignore empty keys gracefully (if applicable)', () => {
            const sort = new Map([['', SortDirection.ASCENDING]])
            expect(toSortQueryParam(sort)).toBe('')
        })
    })
})
