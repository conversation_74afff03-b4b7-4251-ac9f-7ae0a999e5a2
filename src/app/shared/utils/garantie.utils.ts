import { Garantie } from '../models/garantie'
import { TypeGaranties } from '../enums/type-garanties.enum'
import { CODE_FRANCHISE_ANGLAISE } from '../constants/garanties.constants'

export const trackByGarantieCode = (
    index: number,
    item: Garantie
): TypeGaranties => item.code

export const hasFranchiseAnglaise = (garantie: Garantie): boolean =>
    garantie.franchises.some(
        (franchise) => franchise.franchise.code === CODE_FRANCHISE_ANGLAISE
    )
