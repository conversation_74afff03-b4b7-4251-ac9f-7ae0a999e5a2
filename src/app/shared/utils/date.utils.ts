import { fr } from 'date-fns/locale'
import {
    endOfDay,
    endOfMonth,
    endOfToday,
    endOfWeek,
    format,
    isAfter,
    isBefore,
    isEqual,
    parse,
    startOfDay,
    startOfMonth,
    startOfWeek,
} from 'date-fns'
import { FilterDate } from '../enums/filter-date.enum'

export enum DateFormat {
    DASHED_YYYY_MM_DD = 'yyyy-MM-dd',
    DOTTED_DD_MM_YYYY = 'dd.MM.yyyy',
    DOTTED_DD_MM_YYYY_HH_MM = 'dd.MM.yyyy - hh:mm',
}

export interface DatePeriod {
    filterDate: FilterDate
    beginPeriod: {
        operator: string
        value: string
    }
    endPeriod: {
        operator: string
        value: string
    } | null
}

const DEFAULT_LOCALE = fr
const TODAY = new Date()
const FOYER_DATE_REGEX = /^\d{4}-\d{2}-\d{2}$/
const EXTRACT_OPERATOR_AND_DATE_REGEX = /^([<>]=?|=<|=>)\s*([\d-TZ:.]+)$/

export const parseDashedDDMMYYYYtoDate = (yyyymmddDate: string): Date =>
    parse(yyyymmddDate, DateFormat.DASHED_YYYY_MM_DD, new Date())

export const formatDateToDottedDDMMYYYY = (date: Date): string =>
    format(date, DateFormat.DOTTED_DD_MM_YYYY, {
        locale: DEFAULT_LOCALE,
    })

export const formatDateToDashedYYYYMMDD = (date: Date): string =>
    format(date, DateFormat.DASHED_YYYY_MM_DD, {
        locale: DEFAULT_LOCALE,
    })

const getMonthDates = (): DatePeriod => {
    const firstDayOfMonth = startOfMonth(TODAY)
    const lastDayOfMonth = endOfMonth(TODAY)
    return {
        filterDate: FilterDate.MOIS,
        beginPeriod: {
            operator: '>=',
            value: firstDayOfMonth.toISOString(),
        },
        endPeriod: {
            operator: '<',
            value: lastDayOfMonth.toISOString(),
        },
    }
}

const getWeekDates = () => {
    const mondayOfCurrentWeek = startOfWeek(TODAY, { locale: DEFAULT_LOCALE })
    const sundayOfCurrentWeek = endOfWeek(TODAY, { locale: DEFAULT_LOCALE })
    return {
        filterDate: FilterDate.SEMAINE,
        beginPeriod: {
            operator: '>=',
            value: mondayOfCurrentWeek.toISOString(),
        },
        endPeriod: {
            operator: '<',
            value: sundayOfCurrentWeek.toISOString(),
        },
    }
}

const getTodayDates = (): DatePeriod => ({
    filterDate: FilterDate.AUJOURDHUI,
    beginPeriod: {
        operator: '>=',
        value: startOfDay(TODAY).toISOString(),
    },
    endPeriod: {
        operator: '<',
        value: endOfDay(TODAY).toISOString(),
    },
})

export const birthdayToAge = (brithdayDateString: string): number => {
    if (!FOYER_DATE_REGEX.test(brithdayDateString)) {
        throw new Error(
            'Wrong input for date of birth, YYYY-mm-dd format is expected.'
        )
    }
    const brithdayDate = parseDashedDDMMYYYYtoDate(brithdayDateString)
    const ageDifMs = Date.now() - brithdayDate.getTime()
    if (ageDifMs >= 0) {
        const ageDate = new Date(ageDifMs)
        return Math.abs(ageDate.getUTCFullYear() - 1970)
    } else {
        throw new Error('Cannot detect the age from a date in the future')
    }
}

export const getDateBoundaries = (date: FilterDate): DatePeriod => {
    switch (date) {
        case FilterDate.AUJOURDHUI:
            return getTodayDates()
        case FilterDate.SEMAINE:
            return getWeekDates()
        case FilterDate.MOIS:
            return getMonthDates()
        default:
            throw new Error(`Invalid FilterDate: ${date}`)
    }
}

const getAllDatesBoundaries = (): DatePeriod[] => [
    getTodayDates(),
    getWeekDates(),
    getMonthDates(),
]

export const getPeriodFromDates = (
    datesFromFilter: string[]
): FilterDate | undefined => {
    const allDatesPeriod = getAllDatesBoundaries()
    const datesFromFilterAsDatePeriod = datesFromFilter
        .map((date) => {
            const match = EXTRACT_OPERATOR_AND_DATE_REGEX.exec(date)
            return match ? { operator: match[1], value: match[2] } : null
        })
        .filter(Boolean)

    return allDatesPeriod.find((period) => {
        const periods = [period.beginPeriod, period.endPeriod].filter(
            (el) => el !== null
        )
        return (
            JSON.stringify(periods) ===
            JSON.stringify(datesFromFilterAsDatePeriod)
        )
    })?.filterDate
}

export const maxDateFilter = (date: Date | null): boolean => {
    const now = new Date()
    return date ? isBefore(date, now) || isEqual(date, now) : false
}

export const minDateFilter = (date: Date | null): boolean =>
    date ? isAfter(date, endOfToday()) : false
