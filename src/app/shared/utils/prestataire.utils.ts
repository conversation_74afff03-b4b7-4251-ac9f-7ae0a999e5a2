import { MappingJsonAPIObjectToPageItemFunction } from './pagination-utils'
import { Prestataire } from '../models/prestataire.model'
import { PrestatairePageItem } from '../models/prestataire-page-item.model'
import { JsonApiResponseInterface } from '../models/api/json-api-response-interface'
import { Marker } from '../models/marker'
import { getOrFail } from './objects.utils'

export const mapToListePrestatairePageItem: MappingJsonAPIObjectToPageItemFunction<
    Prestataire,
    PrestatairePageItem
> = (data): PrestatairePageItem[] =>
    data.map(({ id, attributes }) => {
        const { _kind, prestataire } = attributes
        return {
            ...prestataire,
            id,
            _kind,
        }
    })

export const mapJsonApiResponseToPrestataire = (
    response: JsonApiResponseInterface<Prestataire>
): Prestataire => {
    const {
        data: {
            attributes: { prestataire },
        },
    } = response
    return prestataire
}

export const mapPrestataireToGarageMarker = (
    prestataire: PrestatairePageItem,
    isActive: boolean = false
): Marker => {
    const location = getOrFail(prestataire.location, 'location', 'prestataire')
    return {
        id: prestataire.id,
        isActive,
        nom: prestataire.nomPrenom,
        lat: location.coordinates[1],
        lng: location.coordinates[0],
    }
}
