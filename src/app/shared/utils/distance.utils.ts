export const calculerDistanceInKm = (
    longitudePointDepart: number,
    latitudePointDepart: number,
    longitudePointArrivee: number,
    latitudePointArrivee: number
): number => {
    const RAYON_MOYEN_TERRE_IN_KM = 6371

    const differenceDeLatitudeEnRadians = angleToRadians(
        latitudePointArrivee - latitudePointDepart
    )
    const differenceDeLongitudeEnRadians = angleToRadians(
        longitudePointArrivee - longitudePointDepart
    )

    const sinusCarreDeLaMoitieDeLaDifferenceDeLatitude =
        Math.sin(differenceDeLatitudeEnRadians / 2) ** 2
    const sinusCarreDeLaMoitieDeLaDifferenceDeLongitude =
        Math.sin(differenceDeLongitudeEnRadians / 2) ** 2

    const cosinusDeLatitudePremierPoint = Math.cos(
        angleToRadians(latitudePointDepart)
    )
    const cosinusDeLatitudeDeuxiemePoint = Math.cos(
        angleToRadians(latitudePointArrivee)
    )

    const coefficientDeHaversine =
        sinusCarreDeLaMoitieDeLaDifferenceDeLatitude +
        cosinusDeLatitudePremierPoint *
            cosinusDeLatitudeDeuxiemePoint *
            sinusCarreDeLaMoitieDeLaDifferenceDeLongitude

    const angleCentralEntreLesDeuxPoints =
        2 *
        Math.atan2(
            Math.sqrt(coefficientDeHaversine),
            Math.sqrt(1 - coefficientDeHaversine)
        )

    const distanceCalculeeEnKilometres =
        RAYON_MOYEN_TERRE_IN_KM * angleCentralEntreLesDeuxPoints
    return Number(distanceCalculeeEnKilometres.toFixed(2))
}

const angleToRadians = (angleEnDegres: number): number =>
    angleEnDegres * (Math.PI / 180)
