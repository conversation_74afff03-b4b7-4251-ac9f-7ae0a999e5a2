import { By } from '@angular/platform-browser'
import { ComponentFixture } from '@angular/core/testing'

export const checkTextContentForElement = <T>(
    fixture: ComponentFixture<T>,
    selector: string,
    textToCheck: string
): void => {
    const element = fixture.debugElement.query(By.css(selector))
    const elementText = element.nativeElement.innerText
    expect(elementText).toContain(textToCheck)
}

export const checkElementToContainClass = <T>(
    fixture: ComponentFixture<T>,
    selector: string,
    textToCheck: string
): void => {
    const element = fixture.debugElement.query(By.css(selector))
    const elementClassList = element.nativeElement.classList
    expect(elementClassList).toContain(textToCheck)
}

export const expectElementToExist = <T>(
    fixture: ComponentFixture<T>,
    selector: string
): void => {
    const nativeElement = fixture.debugElement.query(
        By.css(selector)
    ).nativeElement
    expect(nativeElement).not.toBeNull()
}

export const expectElementToNotExist = <T>(
    fixture: ComponentFixture<T>,
    selector: string
): void => {
    const element = fixture.debugElement.query(By.css(selector))
    expect(element).toBeNull()
}
