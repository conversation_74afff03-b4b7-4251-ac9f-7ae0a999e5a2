import { JsonApiData } from '../models/api/json-api-data'
import { JsonApiDataAttributes } from '../models/api/json-api-data-attributes'
import { Risque } from '../models/risque'
import { JsonApiResponseInterface } from '../models/api/json-api-response-interface'
import { Garantie } from '../models/garantie'
import { CodeLabel } from '../models/code-label'
import { toPerson } from './personne.utils'
import { Labeled } from '../models/labeled'
import { SituationContratGarantie } from '../wrappers/situations-contrats-wrapper/models/situation-contrat.garantie'
import { EtatGarantie } from '../enums/etat-garantie.enum'
import { TypeGaranties } from '../enums/type-garanties.enum'
import { StatutRisque } from '../enums/statut-risque.enum'
import { SituationAdresseRisque } from '../wrappers/situations-contrats-wrapper/models/situation-adresse.risque'
import {
    Personne,
    REFERENTIEL_PERSONNE_KIND,
} from '../wrappers/personne-wrapper/models/personne/personne'
import { SituationContratRisque } from '../wrappers/situations-contrats-wrapper/models/situation-contrat.risque'
import { REFERENTIEL_ADRESSES_KIND } from '../wrappers/personne-wrapper/models/personne/referentiel-adresse'

const mapToGaranties = (garanties: SituationContratGarantie[]): Garantie[] =>
    garanties ? garanties.map((value) => mapToGarantie(value)) : []

const mapToGarantie = (
    situationContratGarantie: SituationContratGarantie
): Garantie => {
    const mapToEtatGarantie = (etatGarantie: CodeLabel): EtatGarantie => {
        switch (etatGarantie.code) {
            case 'V':
                return EtatGarantie.EN_VIGUEUR
            case 'A':
                return EtatGarantie.ANNULE
            default:
                throw new Error(
                    `EtatGarantie non connu : ${etatGarantie.code}.`
                )
        }
    }

    return {
        nom: situationContratGarantie.garantie.label,
        code: situationContratGarantie.garantie.code as TypeGaranties,
        dateEntreeEnVigueur: new Date(situationContratGarantie.dateEffet),
        franchise: situationContratGarantie.franchises
            .map((fran) => fran.montant)
            .reduce((sum, current) => sum + current, 0)
            .toString(),
        etat: mapToEtatGarantie(situationContratGarantie.etatGarantie),
        franchises: situationContratGarantie.franchises,
    }
}

export const mapToLabeledStatutRisque = (
    etatRisque: CodeLabel
): Labeled<StatutRisque> => ({
    value: mapToStatutRisque(etatRisque),
    label: etatRisque.label,
})

export const mapToStatutRisque = (etatRisque: CodeLabel): StatutRisque => {
    switch (etatRisque.code) {
        case 'V':
            return StatutRisque.EN_VIGUEUR
        case 'I':
            return StatutRisque.EN_INSTANCE
        case 'S':
            return StatutRisque.SUSPENDU
        case 'A':
            return StatutRisque.ANNULE
        case 'F':
            return StatutRisque.SANS_EFFET
        default:
            throw new Error(`EtatRisque non connu : ${etatRisque.code}.`)
    }
}

export const toNumeroRue = (
    situationAdresseRisque: SituationAdresseRisque
): string =>
    situationAdresseRisque.numeroRue
        ? ` ${situationAdresseRisque.numeroRue}`
        : ''

export const mapIncludedToPersonne = (
    included: JsonApiData<JsonApiDataAttributes> | undefined
): Personne | undefined =>
    included
        ? (toPerson(
              included as unknown as JsonApiData<
                  Personne & JsonApiDataAttributes
              >,
              undefined
          ) as unknown as Personne)
        : undefined

export const mapSituationRisqueToRisque = (
    data: JsonApiData<SituationContratRisque & JsonApiDataAttributes>,
    included: JsonApiData<JsonApiDataAttributes>[] | undefined
): Risque => {
    const situationContratRisque = data.attributes
    return {
        numeroRisque: data.id,
        preneur: mapIncludedToPersonne(
            included?.find(
                (value) =>
                    value.type === REFERENTIEL_PERSONNE_KIND &&
                    value.id === situationContratRisque.preneur
            )
        ),
        numeroPolice: `${situationContratRisque.numeroPolice}`,
        status: mapToLabeledStatutRisque(situationContratRisque.etatRisque),
        adresse: mapIncludedAdressToSituationAdresseRisque(situationContratRisque, included),
        codeProduit: situationContratRisque.produit,
        typeRisque: situationContratRisque.typeRisque.label,
        dateEffet: new Date(situationContratRisque.datePremierEffet),
        familleProduit: situationContratRisque.familleProduit,
        garanties: mapToGaranties(situationContratRisque.garanties),
        plaque: situationContratRisque.plaque,
        conducteurPrincipal: situationContratRisque.conducteurPrincipal,
        conducteursSecondaires: situationContratRisque.conducteursSecondaires,
        marque: situationContratRisque.marque,
        modele: situationContratRisque.modele,
    }
}
export const mapAllSituationsRisquesToRisques = (
    risques: JsonApiResponseInterface<SituationContratRisque[]>
): Risque[] => {
    const result: Risque[] = []
    risques.data?.forEach((data) => {
        const risqueResult: Risque = mapSituationRisqueToRisque(
            data,
            risques.included
        )
        if (risqueResult) {
            result.push(risqueResult)
        }
    })
    return result
}

export const mapSituationContratRisqueToRisque = (
    input: JsonApiResponseInterface<SituationContratRisque>
): Risque => mapSituationRisqueToRisque(input.data, input.included)

export const extractNumeroPoliceFromNumeroSituationRisque = (
    numeroSituationRisque: string
) => numeroSituationRisque.split('-')[0]

const mapIncludedAdressToSituationAdresseRisque = (
    situationContratRisque: SituationContratRisque,
    included?: JsonApiData<JsonApiDataAttributes>[]
): SituationAdresseRisque => {
    const addressIncluded = included?.find(
        (value) =>
            value.type === REFERENTIEL_ADRESSES_KIND &&
            value.id === situationContratRisque.adresse
    )
    return addressIncluded?.attributes as unknown as SituationAdresseRisque
}
