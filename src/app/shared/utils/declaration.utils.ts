import { MappingJsonAPIObjectToPageItemFunction } from './pagination-utils'
import { Declaration } from '../models/declaration'
import { ListeDeclarationPageItem } from '../models/liste-declaration-page-item'
import { getOrFail } from './objects.utils'

export const mapToListeDeclarationPageItem: MappingJsonAPIObjectToPageItemFunction<
    Declaration,
    ListeDeclarationPageItem
> = (data) =>
    data.map((m) => {
        const attributes = m.attributes
        const declarationAttributes = attributes['declaration']
        return {
            ...declarationAttributes,
            id: m.id,
            updatedAt: new Date(getOrFail(attributes.updatedAt, 'updatedAt')),
            dateDeSurvenance: new Date(
                getOrFail(
                    declarationAttributes.survenance.dateDeSurvenance,
                    'dateDeSurvenance'
                )
            ),
            stateKind: attributes._kind,
            numeroDossierSinistre: attributes.numeroDossierSinistre,
        }
    })
