import { ErrorMessage } from '../models/error-message.model'
import { ElementRef } from '@angular/core'
import { ErrorLevel } from '../enums/error-type.enum'
import { ErrorDisplay } from '../enums/error-display.enum'

export const createPopupMessage = (
    title: string,
    content: string,
    level: ErrorLevel = ErrorLevel.DANGER,
    actionLabel = 'Fermer',
    actionCallback?: () => void
): ErrorMessage => ({
    title,
    content,
    level,
    display: ErrorDisplay.POPUP,
    actionCallback,
    actionLabel,
})

export const scrollToFirstErrorInForm = (elementRef: ElementRef): void => {
    const form = elementRef.nativeElement.querySelector('form')
    if (form) {
        const firstInvalidField =
            form.querySelector('.Message.is-banner.is-danger') ??
            form.querySelector('.ng-invalid')

        if (firstInvalidField) {
            if (firstInvalidField.tagName !== 'INPUT') {
                firstInvalidField.scrollIntoView({
                    block: 'start',
                    behavior: 'smooth',
                })
            } else {
                firstInvalidField.focus()
            }
        }
    }
}
