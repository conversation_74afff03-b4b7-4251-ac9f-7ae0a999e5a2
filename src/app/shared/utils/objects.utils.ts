export const getOrFail = <T>(
    input: T | undefined | null,
    fieldName: string,
    object = 'declaration'
): T => {
    if (input === undefined || input === null) {
        const message = `Missing ${fieldName} in ${object}`
        console.error(message) // eslint-disable-line
        throw new Error(message)
    }
    return input
}

export const hasNonNullValues = (
    object: object | null | undefined,
    ignoreFields: string[] = [],
    currentPath: string = ''
): boolean => {
    if (object === null || object === undefined) {
        return false
    }

    const entries = Object.entries(object)

    return entries.some(([key, value]) => {
        const fullPath = currentPath ? `${currentPath}.${key}` : key

        if (shouldIgnorePath(fullPath, ignoreFields)) {
            return false
        }

        if (value === null || value === undefined) {
            return false
        } else if (typeof value === 'string') {
            return value.trim() !== ''
        } else if (typeof value === 'object') {
            return hasNonNullValues(value, ignoreFields, fullPath)
        } else {
            return true
        }
    })
}

const shouldIgnorePath = (
    currentPath: string,
    ignoreFields: string[]
): boolean =>
    ignoreFields.some((ignorePath) => {
        if (ignorePath === currentPath) {
            return true
        }
        return currentPath.startsWith(ignorePath + '.')
    })
