export const getOrFail = <T>(
    input: T | undefined | null,
    fieldName: string,
    object = 'declaration'
): T => {
    if (input === undefined || input === null) {
        const message = `Missing ${fieldName} in ${object}`
        console.error(message) // eslint-disable-line
        throw new Error(message)
    }
    return input
}

export const hasNonNullValues = (
    object: object | null | undefined,
    ignoreFields: string[] = []
): boolean => {
    if (object === null || object === undefined) {
        return false
    }

    const entries = Object.entries(object)

    return entries.some(([key, value]) => {
        // Vérifier si cette clé doit être ignorée complètement
        if (ignoreFields.includes(key)) {
            return false
        }

        if (value === null || value === undefined) {
            return false
        } else if (typeof value === 'string') {
            return value.trim() !== ''
        } else if (typeof value === 'object') {
            // Pour la récursivité, on filtre les ignoreFields pour ne garder que ceux
            // qui concernent les propriétés imbriquées de cet objet
            const nestedIgnoreFields = ignoreFields
                .filter(field => field.startsWith(key + '.'))
                .map(field => field.substring(key.length + 1))

            return hasNonNullValues(value, nestedIgnoreFields)
        } else {
            return true
        }
    })
}
