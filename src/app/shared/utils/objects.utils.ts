export const getOrFail = <T>(
    input: T | undefined | null,
    fieldName: string,
    object = 'declaration'
): T => {
    if (input === undefined || input === null) {
        const message = `Missing ${fieldName} in ${object}`
        console.error(message) // eslint-disable-line
        throw new Error(message)
    }
    return input
}

export const hasNonNullValues = (
    object: object | null | undefined,
    ignoreFields: string[] = []
): boolean => {
    if (object === null || object === undefined) {
        return false
    }

    const entries = Object.entries(object)

    return entries.some(([key, value]) => {
        if (ignoreFields.includes(key)) {
            return false
        }

        if (value === null || value === undefined) {
            return false
        } else if (typeof value === 'string') {
            return value.trim() !== ''
        } else if (typeof value === 'object') {
            return hasNonNullValues(value, ignoreFields)
        } else {
            return true
        }
    })
}
