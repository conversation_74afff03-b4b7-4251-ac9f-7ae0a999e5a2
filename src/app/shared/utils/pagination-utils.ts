import { JsonApiDataAttributes } from '../models/api/json-api-data-attributes'
import { JsonApiData } from '../models/api/json-api-data'
import { JsonApiResponseInterface } from '../models/api/json-api-response-interface'
import { Pageable } from '../models/pageable.model'
import { PageSize } from '../enums/page-size.enum'
import { SortDirection } from '../enums/sort-direction.enum'

export type MappingJsonAPIObjectToPageItemFunction<
    JsonAttributesType,
    TargetPagedType,
> = (
    jsonApiData: JsonApiData<JsonAttributesType & JsonApiDataAttributes>[]
) => TargetPagedType[]

export const jsonAPIToPageable = <JsonAttributesType, TargetPagedType>(
    json: JsonApiResponseInterface<JsonAttributesType[]>,
    mapper: MappingJsonAPIObjectToPageItemFunction<
        JsonAttributesType,
        TargetPagedType
    >
): Pageable<TargetPagedType> =>
    ({
        content: mapper(json.data),
        totalItems: json.meta?.totalRecords ?? 0,
        currentPage: json.meta?.page?.number ?? 0,
        totalPages: json.meta?.totalPages ?? 0,
        pageSize: json.meta?.page?.size
            ? `${json.meta?.page?.size}`
            : PageSize.FIVE_ELEMENTS,
    }) as Pageable<TargetPagedType>

export const toSortQueryParam = (sort: Map<string, SortDirection>): string =>
    Array.from(sort)
        .map((sortDirection) => {
            const [key, direction] = sortDirection
            const prefix = direction === SortDirection.DESCENDING ? '-' : ''
            return prefix + key
        })
        .join(',')
