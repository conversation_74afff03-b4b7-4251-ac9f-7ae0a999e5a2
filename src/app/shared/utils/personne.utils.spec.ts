import {
    defineBankAccountToUseByPriority,
    defineBankAccountToUseForReglementFrais,
    defineEmailToUseByPriority,
    definePhoneToUseByPriority,
    isPersonneMorale,
    isPersonnePhysique,
} from './personne.utils'
import { addDays } from 'date-fns'
import { formatDateToDashedYYYYMMDD } from './date.utils'
import { CodeBic } from '../enums/code-bic.enum'
import { TypeSocieteUtilisation } from '../wrappers/personne-wrapper/models/personne/type-societe-utilisation.enum'
import { CompteBancaire } from '../wrappers/personne-wrapper/models/personne/compte-bancaire'
import { TypeValeurUtilisation } from '../wrappers/personne-wrapper/models/personne/type-valeur-utilisation.enum'
import { OrderedEmailUsageCode } from '../wrappers/personne-wrapper/models/personne/ordered-email-usage-code'
import { TypePersonne } from '../wrappers/personne-wrapper/models/personne/personne-type.enum'
import { OrderedTelephoneUsageCode } from '../wrappers/personne-wrapper/models/personne/ordered-telephone-usage-code'
import { TelephonePersonne } from '../wrappers/personne-wrapper/models/personne/telephone-personne'
import { EmailPersonne } from '../wrappers/personne-wrapper/models/personne/email-personne'

describe('PersonneUtils', () => {
    describe('defineBankAccountToUseByPriority', () => {
        const toCompteBancaire = (
            numero: string,
            codeSociete: TypeSocieteUtilisation,
            dateDebut: string = '2021-05-27',
            utilisation: TypeValeurUtilisation = TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
            dateFin: string | undefined = undefined
        ): CompteBancaire => ({
            codeBic: CodeBic.BIL,
            numeroCompteBancaire: numero,
            utilisations: [
                {
                    valeurUtilisation: {
                        code: utilisation,
                        label: 'label',
                    },
                    societeUtilisation: {
                        code: codeSociete,
                        label: 'label',
                    },
                },
            ],
            indicateur: {
                frais: false,
                dateMiseAJour: '2022-11-10T10:17:28.916Z',
            },
            validite: {
                dateDebut,
                dateFin,
            },
        })

        describe('case with no FOYER_ASSURANCE account', () => {
            it('should return undefined', () => {
                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_SANTE
                    ),
                ]
                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toBeUndefined()
            })
        })

        describe('case with one FOYER_ASSURANCE account', () => {
            it('should return the account', () => {
                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_SANTE
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_VIE
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toEqual(
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE
                    )
                )
            })
        })
        describe('case with one or more FOYER_ASSURANCE account', () => {
            it('should return the account if validity startDate is strictly before the current date (also not the same day)', () => {
                const now = new Date()
                const today = formatDateToDashedYYYYMMDD(now)
                const dateInFuture = formatDateToDashedYYYYMMDD(addDays(now, 2))

                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        today
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6263',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        dateInFuture
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toEqual(
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE
                    )
                )
            })

            it('should not return the accounts with validity enddate is strictly before current date ', () => {
                const now = new Date()
                const today = formatDateToDashedYYYYMMDD(now)
                const dateInFuture = formatDateToDashedYYYYMMDD(addDays(now, 2))
                const dateInPast = formatDateToDashedYYYYMMDD(addDays(now, -2))

                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        today,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        dateInPast
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        '2021-05-27',
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        dateInPast
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        '2021-05-27',
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        today
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6263',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        dateInFuture,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        dateInPast
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toBeUndefined()
            })

            it('should return the accounts with validity enddate is strictly after current date ', () => {
                const now = new Date()
                const today = formatDateToDashedYYYYMMDD(now)
                const dateInFuture = formatDateToDashedYYYYMMDD(addDays(now, 2))
                const dateInPast = formatDateToDashedYYYYMMDD(addDays(now, -2))

                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        today,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        dateInPast
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        '2021-05-27',
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        dateInPast
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        '2021-05-27',
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        today
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6263',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        dateInPast,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        dateInFuture
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toEqual(
                    toCompteBancaire(
                        'LU325-656-662-6263',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        dateInPast,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                        dateInFuture
                    )
                )
            })
        })
        describe('case with one or more FOYER_ASSURANCE account with valid dates', () => {
            it('should return the account with remboursement preneur first if different usage type are present', () => {
                const validDate = '2021-05-27'
                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_ASSURE
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6263',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.PRELEVEMENT
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toEqual(
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    )
                )
            })

            it('should return the account with remboursement preneur if different usage type are present', () => {
                const validDate = '2021-05-27'
                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_ASSURE
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toEqual(
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    )
                )
            })

            it('should return the account first remboursement preneur if both are remboursement preneur', () => {
                const validDate = '2021-05-27'
                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toEqual(
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    )
                )
            })

            it('should return the account with remboursement assure if no remboursement preneur are present', () => {
                const validDate = '2021-05-27'
                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.PRELEVEMENT
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_ASSURE
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6263',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.PRELEVEMENT
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toEqual(
                    toCompteBancaire(
                        'LU325-656-662-6262',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.REMBOURSEMENT_ASSURE
                    )
                )
            })

            it('should return undefined if no remboursement preneur and no remboursement assure are present', () => {
                const validDate = '2021-05-27'
                const compteBancaires = [
                    toCompteBancaire(
                        'LU325-656-662-6261',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.PRELEVEMENT
                    ),
                    toCompteBancaire(
                        'LU325-656-662-6263',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        validDate,
                        TypeValeurUtilisation.PRELEVEMENT
                    ),
                ]

                expect(
                    defineBankAccountToUseByPriority(
                        compteBancaires,
                        new Date()
                    )
                ).toBeUndefined()
            })

            it('should return the correct bank account with utilisation REMBOURSEMENT_PRESTATAIRE for reglement des frais', () => {
                const currentDate = new Date('2022-01-01')
                const compteBancaires = [
                    toCompteBancaire(
                        '***************************',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        '2020-05-27',
                        TypeValeurUtilisation.PRELEVEMENT
                    ),
                    toCompteBancaire(
                        '****************',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        '2015-06-19',
                        TypeValeurUtilisation.REMBOURSEMENT_PRESTATAIRE
                    ),
                    toCompteBancaire(
                        '**********************',
                        TypeSocieteUtilisation.FOYER_ASSURANCE,
                        '2023-01-07',
                        TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                    ),
                ]

                const expectedResult = compteBancaires[1]

                expect(
                    defineBankAccountToUseForReglementFrais(
                        compteBancaires,
                        currentDate
                    )
                ).toEqual(expectedResult)
            })
        })
    })

    describe('defineEmailToUseByPriority', () => {
        const toEmail = (
            officiel: boolean,
            adresse: string,
            usageCode: OrderedEmailUsageCode,
            dateMiseAJour: string = '2022-11-10T10:17:28.916Z'
        ): EmailPersonne => ({
            officiel,
            adresse,
            usage: {
                code: usageCode,
                label: 'label',
            },
            indicateur: {
                frais: false,
                dateMiseAJour,
            },
        })

        describe('case with one official phone number', () => {
            it('should return the official one', () => {
                const emails = [
                    toEmail(
                        false,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    ),
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    ),
                    toEmail(
                        false,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    ),
                ]
                expect(defineEmailToUseByPriority(emails)).toEqual(
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    )
                )
            })
        })
        describe('case with multiple official phone number', () => {
            it('should return the EMAIL_PRIVE, if there is only one', () => {
                const emails = [
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRO
                    ),
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    ),
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRO
                    ),
                ]
                expect(defineEmailToUseByPriority(emails)).toEqual(
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    )
                )
            })

            it('should return the freshest EMAIL_PRIVE, if there are more than one', () => {
                const emails = [
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    ),
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE,
                        '2022-11-11T10:17:28.916Z'
                    ),
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE
                    ),
                ]
                expect(defineEmailToUseByPriority(emails)).toEqual(
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRIVE,
                        '2022-11-11T10:17:28.916Z'
                    )
                )
            })

            it('should return the freshest EMAIL_PRO, if there are more than one and no EMAIL_PRIVE', () => {
                const emails = [
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRO
                    ),
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRO,
                        '2022-11-11T10:17:28.916Z'
                    ),
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRO
                    ),
                ]
                expect(defineEmailToUseByPriority(emails)).toEqual(
                    toEmail(
                        true,
                        '<EMAIL>',
                        OrderedEmailUsageCode.EMAIL_PRO,
                        '2022-11-11T10:17:28.916Z'
                    )
                )
            })
        })
    })

    describe('definePhoneToUseByPriority', () => {
        const toPhone = (
            officiel: boolean,
            numero: string,
            codeUsage: OrderedTelephoneUsageCode,
            dateMiseAJour: string = '2022-11-10T10:17:28.916Z'
        ): TelephonePersonne => ({
            officiel,
            numero,
            indicatif: '00352',
            usage: {
                code: codeUsage,
                label: 'G1',
            },
            indicateur: {
                frais: false,
                dateMiseAJour,
            },
        })

        describe('case with no phones as input', () => {
            it('should return undefined for an empty array', () => {
                const phones: TelephonePersonne[] = []
                expect(definePhoneToUseByPriority(phones)).toBeUndefined()
            })
        })

        describe('case with one official phone number', () => {
            it('should return the official phone if one is defined', () => {
                const phones = [
                    toPhone(
                        false,
                        '132456',
                        OrderedTelephoneUsageCode.GSM_PRIVE
                    ),
                    toPhone(
                        true,
                        '456712',
                        OrderedTelephoneUsageCode.GSM_PRIVE
                    ),
                    toPhone(
                        false,
                        '5165165',
                        OrderedTelephoneUsageCode.GSM_PRIVE
                    ),
                ]
                expect(definePhoneToUseByPriority(phones)).toEqual(
                    toPhone(true, '456712', OrderedTelephoneUsageCode.GSM_PRIVE)
                )
            })
        })
        describe('case with multiple official phone number', () => {
            it('should return the GSM_PRIVE phone if only one is defined', () => {
                const phones = [
                    toPhone(true, '132456', OrderedTelephoneUsageCode.GSM_PRO),
                    toPhone(
                        true,
                        '456712',
                        OrderedTelephoneUsageCode.TELEPHONE_PRIVE
                    ),
                    toPhone(
                        true,
                        '5165165',
                        OrderedTelephoneUsageCode.GSM_PRIVE
                    ),
                ]
                expect(definePhoneToUseByPriority(phones)).toEqual(
                    toPhone(
                        true,
                        '5165165',
                        OrderedTelephoneUsageCode.GSM_PRIVE
                    )
                )
            })

            it('should return the freshest GSM_PRIVE phone if multiple GSM_PRIVE are defined', () => {
                const phones = [
                    toPhone(true, '132456', OrderedTelephoneUsageCode.GSM_PRO),
                    toPhone(
                        true,
                        '456712',
                        OrderedTelephoneUsageCode.GSM_PRIVE,
                        '2022-11-11T10:17:28.916Z'
                    ),
                    toPhone(
                        true,
                        '5165165',
                        OrderedTelephoneUsageCode.GSM_PRIVE,
                        '2022-11-10T10:17:28.916Z'
                    ),
                ]
                expect(definePhoneToUseByPriority(phones)).toEqual(
                    toPhone(
                        true,
                        '456712',
                        OrderedTelephoneUsageCode.GSM_PRIVE,
                        '2022-11-11T10:17:28.916Z'
                    )
                )
            })

            it('should return the TELEPHONE_PRIVE if no GSM_PRIVE defined, if multiple GSM_PRIVE are defined', () => {
                const phones = [
                    toPhone(true, '132456', OrderedTelephoneUsageCode.GSM_PRO),
                    toPhone(
                        true,
                        '456712',
                        OrderedTelephoneUsageCode.TELEPHONE_PRIVE
                    ),
                    toPhone(
                        true,
                        '5165164',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                    toPhone(
                        true,
                        '5165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                ]
                expect(definePhoneToUseByPriority(phones)).toEqual(
                    toPhone(
                        true,
                        '456712',
                        OrderedTelephoneUsageCode.TELEPHONE_PRIVE
                    )
                )
            })

            it('should return the TELEPHONE_PRIVE if no GSM_PRIVE defined', () => {
                const phones = [
                    toPhone(true, '132456', OrderedTelephoneUsageCode.GSM_PRO),
                    toPhone(true, '456712', OrderedTelephoneUsageCode.GSM_PRO),
                    toPhone(
                        true,
                        '6165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRIVE
                    ),
                    toPhone(
                        true,
                        '5165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                ]
                expect(definePhoneToUseByPriority(phones)).toEqual(
                    toPhone(
                        true,
                        '6165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRIVE
                    )
                )
            })

            it('should return the GSM_PRO if no GSM_PRIVE or TELEPHONE_PRO are defined', () => {
                const phones = [
                    toPhone(
                        true,
                        '132456',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                    toPhone(true, '456712', OrderedTelephoneUsageCode.GSM_PRO),
                    toPhone(
                        true,
                        '6165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                    toPhone(
                        true,
                        '5165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                ]
                expect(definePhoneToUseByPriority(phones)).toEqual(
                    toPhone(true, '456712', OrderedTelephoneUsageCode.GSM_PRO)
                )
            })

            it('should return the freshest TELEPHONE_PRO if no GSM_PRIVE,TELEPHONE_PRIVE,GSM_PRO  are defined', () => {
                const phones = [
                    toPhone(
                        true,
                        '132456',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                    toPhone(
                        true,
                        '456712',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                    toPhone(
                        true,
                        '6165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO,
                        '2022-11-11T10:17:28.916Z'
                    ),
                    toPhone(
                        true,
                        '5165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO
                    ),
                ]
                expect(definePhoneToUseByPriority(phones)).toEqual(
                    toPhone(
                        true,
                        '6165165',
                        OrderedTelephoneUsageCode.TELEPHONE_PRO,
                        '2022-11-11T10:17:28.916Z'
                    )
                )
            })
        })
    })

    describe('isPersonneMorale', () => {
        it('should return true if code is 15 ', () => {
            expect(isPersonneMorale('15')).toBeTruthy()
        })
        it('should return true if code is 05 ', () => {
            expect(isPersonneMorale('05')).toBeTruthy()
        })
        it('should return true if code is 16 ', () => {
            expect(isPersonneMorale('16')).toBeTruthy()
        })
        it('sould return true ', () => {
            expect(isPersonneMorale(TypePersonne.MORALE)).toBeTruthy()
            expect(isPersonneMorale(TypePersonne.INDEPENDANT)).toBeTruthy()
            expect(
                isPersonneMorale(TypePersonne.MORALE_GOUVERNEMENTALE)
            ).toBeTruthy()
        })
    })
    describe('isPersonnePhysique', () => {
        it('should return true if code is different from 15, 05 et 16', () => {
            expect(isPersonnePhysique('04')).toBeTruthy()
        })
    })
})
