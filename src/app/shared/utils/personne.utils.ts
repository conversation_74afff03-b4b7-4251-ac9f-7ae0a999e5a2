import { TelephonePersonne } from '../wrappers/personne-wrapper/models/personne/telephone-personne'
import { OrderedTelephoneUsageCode } from '../wrappers/personne-wrapper/models/personne/ordered-telephone-usage-code'
import { EmailPersonne } from '../wrappers/personne-wrapper/models/personne/email-personne'
import { OrderedEmailUsageCode } from '../wrappers/personne-wrapper/models/personne/ordered-email-usage-code'
import { IndicateurFraicheur } from '../wrappers/personne-wrapper/models/personne/indicateur-fraicheur'
import { Usage } from '../wrappers/personne-wrapper/models/personne/usage'
import { CompteBancaire } from '../wrappers/personne-wrapper/models/personne/compte-bancaire'
import { TypeSocieteUtilisation } from '../wrappers/personne-wrapper/models/personne/type-societe-utilisation.enum'
import { parseDashedDDMMYYYYtoDate } from './date.utils'
import { isAfter, isBefore, isSameDay } from 'date-fns'
import { UtilisationCompte } from '../wrappers/personne-wrapper/models/personne/utilisation-compte'
import { TypeValeurUtilisation } from '../wrappers/personne-wrapper/models/personne/type-valeur-utilisation.enum'
import { TypePersonne } from '../wrappers/personne-wrapper/models/personne/personne-type.enum'
import { JsonApiData } from '../models/api/json-api-data'
import { Personne } from '../wrappers/personne-wrapper/models/personne/personne'
import { JsonApiDataAttributes } from '../models/api/json-api-data-attributes'
import { PersonneMorale } from '../wrappers/personne-wrapper/models/personne/personne-morale'
import { getOrFail } from './objects.utils'
import { PersonnePhysique } from '../wrappers/personne-wrapper/models/personne/personne-physique'
import { ReferentielAdresse } from '../wrappers/personne-wrapper/models/personne/referentiel-adresse'
import { JsonApiResponseInterface } from '../models/api/json-api-response-interface'

const getDateMiseAjourTime = (indicateur: IndicateurFraicheur) =>
    new Date(indicateur.dateMiseAJour).getTime()
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const getEnumTypeOrderPosition = <E>(usage: Usage<E>, e: any) =>
    Object.values(e).indexOf(usage.code)
const compareEmailType = (a: EmailPersonne, b: EmailPersonne): number =>
    getEnumTypeOrderPosition(a.usage, OrderedEmailUsageCode) -
    getEnumTypeOrderPosition(b.usage, OrderedEmailUsageCode)
const booleanToNumber = (a: boolean): number => (a ? -1 : 1)

const compareBoolean = (a: boolean, b: boolean): number =>
    a === b ? 0 : booleanToNumber(a)

const compareByDateFraicheur = (
    a: IndicateurFraicheur,
    b: IndicateurFraicheur
): number => getDateMiseAjourTime(b) - getDateMiseAjourTime(a)

const comparePhoneType = (a: TelephonePersonne, b: TelephonePersonne): number =>
    getEnumTypeOrderPosition(a.usage, OrderedTelephoneUsageCode) -
    getEnumTypeOrderPosition(b.usage, OrderedTelephoneUsageCode)

const doTypeValeurUtilisationExists = (
    utilisations: UtilisationCompte[],
    typeValeurUtilisation: TypeValeurUtilisation
): boolean =>
    !!utilisations.find(
        (utilisation) =>
            utilisation.valeurUtilisation.code === typeValeurUtilisation
    )

const compareUtilisation = (
    utilisations: UtilisationCompte[],
    utilisations2: UtilisationCompte[],
    typeUtilisation: TypeValeurUtilisation
): number => {
    const remboursementPreneurExistsInUtilisations =
        doTypeValeurUtilisationExists(utilisations, typeUtilisation)
    const remboursementPreneurExistsInUtilisations2 =
        doTypeValeurUtilisationExists(utilisations2, typeUtilisation)

    if (
        remboursementPreneurExistsInUtilisations &&
        remboursementPreneurExistsInUtilisations2
    ) {
        return 0
    } else if (
        remboursementPreneurExistsInUtilisations &&
        !remboursementPreneurExistsInUtilisations2
    ) {
        return -1
    } else {
        return 1
    }
}

const isPartOfUtilisationTypes = (
    compteBancaire: CompteBancaire,
    ...types: TypeValeurUtilisation[]
): boolean =>
    !!compteBancaire.utilisations.find((utilisation) =>
        types.includes(utilisation.valeurUtilisation.code)
    )

const isBankAccountNotExpired = (
    compte: CompteBancaire,
    currentDate: Date
): boolean => {
    if (compte.validite.dateFin) {
        const dateFinDeValidite = parseDashedDDMMYYYYtoDate(
            compte.validite.dateFin
        )

        return (
            isAfter(dateFinDeValidite, currentDate) &&
            !isSameDay(dateFinDeValidite, currentDate)
        )
    } else {
        return true
    }
}

const isDateValiditeInFuture = (
    compteBancaire: CompteBancaire,
    currentDate: Date
): boolean => {
    const dateDebut = parseDashedDDMMYYYYtoDate(
        compteBancaire.validite.dateDebut
    )

    return (
        isBefore(dateDebut, currentDate) && !isSameDay(dateDebut, currentDate)
    )
}

const isSocieteUtilisationFoyerAssurance = (
    compteBancaire: CompteBancaire
): boolean =>
    !!compteBancaire.utilisations.find(
        (utilisation) =>
            utilisation.societeUtilisation.code ===
            TypeSocieteUtilisation.FOYER_ASSURANCE
    )

export const defineBankAccountToUseForReglementFrais = (
    compteBancaires: CompteBancaire[],
    currentDate: Date
): CompteBancaire | undefined =>
    compteBancaires.filter(
        (compte) =>
            isSocieteUtilisationFoyerAssurance(compte) &&
            isDateValiditeInFuture(compte, currentDate) &&
            isBankAccountNotExpired(compte, currentDate) &&
            isPartOfUtilisationTypes(
                compte,
                TypeValeurUtilisation.REMBOURSEMENT_PRESTATAIRE
            )
    )[0]

export const defineBankAccountToUseByPriority = (
    compteBancaires: CompteBancaire[],
    currentDate: Date
): CompteBancaire | undefined =>
    compteBancaires
        .filter(
            (compte) =>
                isSocieteUtilisationFoyerAssurance(compte) &&
                isDateValiditeInFuture(compte, currentDate) &&
                isBankAccountNotExpired(compte, currentDate) &&
                isPartOfUtilisationTypes(
                    compte,
                    TypeValeurUtilisation.REMBOURSEMENT_PRENEUR,
                    TypeValeurUtilisation.REMBOURSEMENT_ASSURE
                )
        )
        .sort(
            (a, b) =>
                compareUtilisation(
                    a.utilisations,
                    b.utilisations,
                    TypeValeurUtilisation.REMBOURSEMENT_PRENEUR
                ) ||
                compareUtilisation(
                    a.utilisations,
                    b.utilisations,
                    TypeValeurUtilisation.REMBOURSEMENT_ASSURE
                )
        )[0]

const sortEmailsByOrder = (emails: EmailPersonne[]): EmailPersonne[] =>
    [...emails].sort(
        (a, b) =>
            compareBoolean(a.officiel, b.officiel) ||
            compareEmailType(a, b) ||
            compareByDateFraicheur(a.indicateur, b.indicateur)
    )

const sortPhonesByOrder = (
    telephones: TelephonePersonne[]
): TelephonePersonne[] =>
    [...telephones].sort(
        (a, b) =>
            compareBoolean(a.officiel, b.officiel) ||
            comparePhoneType(a, b) ||
            compareByDateFraicheur(a.indicateur, b.indicateur)
    )

/**
 * si un des emails est indiqué en "Coordonnée officielle" => prendre cet email
 * Si plusieurs emails sont flagués coordonnées officielles => suivre l'ordre ci dessous
 * si "emails privés" renseigné => prendre cet email
 * si "emails pro" renseigné => prendre cet email
 * si 2 emails de même type et tout deux officiel, prendre celui qui a la date de mise à jour la plus récente
 *
 */
export const defineEmailToUseByPriority = (
    emails: EmailPersonne[]
): EmailPersonne | undefined => sortEmailsByOrder(emails)[0]

/**
 * si un des numéro est indiqué en "Coordonnée officielle" => prendre ce numéro
 * Si plusieurs numéros sont flagués coordonnées officielles => suivre l'ordre ci dessous
 * si "numéros GSM privé" renseigné => prendre ce numéro   G1
 * si "numéros tél privé" renseigné => prendre ce numéro   T1
 * si "numéros GSM pros" renseigné => prendre ce numéro    G2
 * si "numéros tel pros" renseigné => prendre ce numéro    T2
 * si 2 numéros de même type et tout deux officiel, prendre celui qui a la date de mise à jour la plus récente
 *
 * @param telephones
 */
export const definePhoneToUseByPriority = (
    telephones: TelephonePersonne[]
): TelephonePersonne | undefined => sortPhonesByOrder(telephones)[0]
export const isPersonneMorale = (code: string): boolean =>
    code === TypePersonne.MORALE ||
    code === TypePersonne.MORALE_GOUVERNEMENTALE ||
    code === TypePersonne.INDEPENDANT
export const isPersonnePhysique = (code: string): boolean =>
    !isPersonneMorale(code)

const extractAdresseFromAttributesAndIncluded = (
    json: any,
    included: any
): ReferentielAdresse | undefined =>
    included &&
    json.adresseCorrespondance &&
    json.adresseCorrespondance.referenceAdresse
        ? included.find(
              (includedData: any) =>
                  includedData.id ===
                  json.adresseCorrespondance.referenceAdresse
          ).attributes
        : undefined

export const toPerson = (
    data: JsonApiData<Personne & JsonApiDataAttributes>,
    included: JsonApiData<JsonApiDataAttributes>[] | undefined
): Personne => {
    const adresseCorrespondance = extractAdresseFromAttributesAndIncluded(
        data.attributes,
        included
    )

    if (isPersonneMorale(data.attributes.typePersonne.code)) {
        const attributes = data.attributes as PersonneMorale

        attributes.numeroPersonne = getOrFail(
            data.attributes.numeroPersonne.toString(),
            'numeroPersonne'
        )

        const nom =
            attributes.nom ??
            (data.attributes as PersonnePhysique).nomUsuel ??
            ''
        return new PersonneMorale(
            attributes.langueCorrespondance,
            attributes.numeroPersonne,
            attributes.typePersonne,
            nom,
            adresseCorrespondance,
            attributes.emails,
            attributes.telephones,
            attributes.comptesBancaires,
            attributes.etatClient,
            attributes.contentieux
        )
    } else if (isPersonnePhysique(data.attributes.typePersonne.code)) {
        const attributes = data.attributes as PersonnePhysique

        attributes.numeroPersonne = getOrFail(
            data.attributes.numeroPersonne.toString(),
            'numeroPersonne'
        )

        return new PersonnePhysique(
            attributes.langueCorrespondance,
            attributes.numeroPersonne,
            attributes.typePersonne,
            attributes.nomUsuel,
            attributes.prenom,
            attributes.titre,
            adresseCorrespondance,
            attributes.emails,
            attributes.telephones,
            attributes.comptesBancaires,
            attributes.etatClient,
            attributes.contentieux,
            attributes.dateDeNaissance
        )
    } else {
        throw new Error(
            'Unsupported kind of personne :' + JSON.stringify(data.attributes)
        )
    }
}

export const mapJsonApiResponseToPersonne = (
    response: JsonApiResponseInterface<Personne>
): Personne => toPerson(response.data, response.included)
