import { getOrFail, hasNonNullValues } from './objects.utils'

describe('ObjectsUtils', () => {
    describe('getOrFail', () => {
        it('should throw an error if it fail to get the object', () => {
            const object = { firstName: 'John', lastName: undefined }
            const result = () => {
                getOrFail(object.lastName, 'lastName', 'object')
            }
            expect(result).toThrow(new Error('Missing lastName in object'))
        })

        it('should return the field', () => {
            const object = { firstName: 'John', lastName: 'Wick' }
            const result = getOrFail(object.lastName, 'lastName', 'object')
            expect(result).toEqual('Wick')
        })
    })

    describe('hasNonNullValues', () => {
        it('should return false for null or undefined objects', () => {
            expect(hasNonNullValues(null)).toBe(false)
            expect(hasNonNullValues(undefined)).toBe(false)
        })

        it('should return true when object has non-null values', () => {
            const obj = { nom: 'Dupont', prenom: 'Jean' }
            expect(hasNonNullValues(obj)).toBe(true)
        })

        it('should return false when all values are null or empty', () => {
            const obj = { nom: null, prenom: '', autre: undefined }
            expect(hasNonNullValues(obj)).toBe(false)
        })

        it('should ignore simple fields specified in ignoreFields', () => {
            const obj = { nom: 'Dupont', prenom: 'Jean', ignore: 'value' }
            expect(hasNonNullValues(obj, ['ignore'])).toBe(true)

            const objOnlyIgnored = { ignore: 'value' }
            expect(hasNonNullValues(objOnlyIgnored, ['ignore'])).toBe(false)
        })

        it('should ignore nested objects completely when specified', () => {
            const obj = {
                nom: 'Dupont',
                adresse: {
                    rue: '123 Main St',
                    ville: 'Paris'
                }
            }

            // Sans ignorer l'adresse
            expect(hasNonNullValues(obj)).toBe(true)

            // En ignorant l'adresse complète, devrait encore retourner true car nom existe
            expect(hasNonNullValues(obj, ['adresse'])).toBe(true)

            // En ignorant nom et adresse, devrait retourner false
            expect(hasNonNullValues(obj, ['nom', 'adresse'])).toBe(false)
        })

        it('should ignore specific nested properties using dot notation', () => {
            const obj = {
                nom: null,
                prenom: null,
                adresse: {
                    rue: '123 Main St',
                    ville: 'Paris'
                }
            }

            // Sans ignorer, devrait retourner true à cause de l'adresse
            expect(hasNonNullValues(obj)).toBe(true)

            // En ignorant adresse.rue, devrait encore retourner true à cause de adresse.ville
            expect(hasNonNullValues(obj, ['adresse.rue'])).toBe(true)

            // En ignorant adresse.rue et adresse.ville, devrait retourner false
            expect(hasNonNullValues(obj, ['adresse.rue', 'adresse.ville'])).toBe(false)
        })

        it('should handle the tiers array use case', () => {
            const tiersList = [
                {
                    nom: 'Dupont',
                    prenom: 'Jean',
                    adresse: {
                        rue: '123 Main St',
                        ville: 'Paris'
                    }
                },
                {
                    // Ce tiers n'a que l'adresse
                    adresse: {
                        rue: '456 Oak St',
                        ville: 'Lyon'
                    }
                }
            ]

            // Sans ignorer l'adresse, devrait retourner true
            expect(hasNonNullValues(tiersList)).toBe(true)

            // En ignorant les adresses dans chaque élément du tableau
            // Le premier tiers a nom et prenom, donc devrait retourner true
            expect(hasNonNullValues(tiersList, ['0.adresse', '1.adresse'])).toBe(true)
        })

        it('should return false when array elements have only ignored fields', () => {
            const tiersList = [
                {
                    adresse: {
                        rue: '123 Main St',
                        ville: 'Paris'
                    }
                },
                {
                    adresse: {
                        rue: '456 Oak St',
                        ville: 'Lyon'
                    }
                }
            ]

            // En ignorant toutes les adresses, devrait retourner false
            expect(hasNonNullValues(tiersList, ['0.adresse', '1.adresse'])).toBe(false)
        })
    })
})
