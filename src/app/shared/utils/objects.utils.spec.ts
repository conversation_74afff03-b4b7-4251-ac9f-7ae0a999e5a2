import { getOrFail, hasNonNullValues } from './objects.utils'

describe('ObjectsUtils', () => {
    describe('getOrFail', () => {
        it('should throw an error if it fail to get the object', () => {
            const object = { firstName: 'John', lastName: undefined }
            const result = () => {
                getOrFail(object.lastName, 'lastName', 'object')
            }
            expect(result).toThrow(new Error('Missing lastName in object'))
        })

        it('should return the field', () => {
            const object = { firstName: 'John', lastName: 'Wick' }
            const result = getOrFail(object.lastName, 'lastName', 'object')
            expect(result).toEqual('Wick')
        })
    })

    describe('hasNonNullValues', () => {
        it('should return false for null or undefined objects', () => {
            expect(hasNonNullValues(null)).toBe(false)
            expect(hasNonNullValues(undefined)).toBe(false)
        })

        it('should return true when object has non-null values', () => {
            const obj = { nom: 'Dupont', prenom: 'Jean' }
            expect(hasNonNullValues(obj)).toBe(true)
        })

        it('should return false when all values are null or empty', () => {
            const obj = { nom: null, prenom: '', autre: undefined }
            expect(hasNonNullValues(obj)).toBe(false)
        })

        it('should ignore simple fields specified in ignoreFields', () => {
            const obj = { nom: 'Dupont', prenom: 'Jean', ignore: 'value' }
            expect(hasNonNullValues(obj, ['ignore'])).toBe(true)
            expect(hasNonNullValues(obj, ['nom', 'prenom'])).toBe(true) // ignore field still has value
        })

        it('should ignore nested fields using dot notation', () => {
            const obj = {
                nom: 'Dupont',
                adresse: {
                    rue: '123 Main St',
                    ville: 'Paris'
                }
            }

            // Sans ignorer l'adresse, devrait retourner true
            expect(hasNonNullValues(obj)).toBe(true)

            // En ignorant l'adresse complète, devrait encore retourner true car nom existe
            expect(hasNonNullValues(obj, ['adresse'])).toBe(true)

            // En ignorant nom et adresse, devrait retourner false
            expect(hasNonNullValues(obj, ['nom', 'adresse'])).toBe(false)
        })

        it('should ignore specific nested properties using dot notation', () => {
            const obj = {
                nom: null,
                prenom: null,
                adresse: {
                    rue: '123 Main St',
                    ville: 'Paris'
                }
            }

            // Sans ignorer, devrait retourner true à cause de l'adresse
            expect(hasNonNullValues(obj)).toBe(true)

            // En ignorant adresse.rue, devrait encore retourner true à cause de adresse.ville
            expect(hasNonNullValues(obj, ['adresse.rue'])).toBe(true)

            // En ignorant toute l'adresse, devrait retourner false
            expect(hasNonNullValues(obj, ['adresse'])).toBe(false)
        })

        it('should handle the tiers.adresse use case', () => {
            const tiersList = [
                {
                    nom: 'Dupont',
                    prenom: 'Jean',
                    adresse: {
                        rue: '123 Main St',
                        ville: 'Paris'
                    }
                }
            ]

            // Sans ignorer l'adresse
            expect(hasNonNullValues(tiersList)).toBe(true)

            // En ignorant les adresses des tiers (simulation du cas d'usage)
            // Note: pour un tableau, on ignore les adresses de chaque élément
            expect(hasNonNullValues(tiersList, ['0.adresse'])).toBe(true) // nom et prenom existent
        })
    })
})
