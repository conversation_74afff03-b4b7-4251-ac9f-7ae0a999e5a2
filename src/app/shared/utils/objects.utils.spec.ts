import { getOrFail } from './objects.utils'

describe('ObjectsUtils', () => {
    describe('getOrFail', () => {
        it('should throw an error if it fail to get the object', () => {
            const object = { firstName: 'John', lastName: undefined }
            const result = () => {
                getOrFail(object.lastName, 'lastName', 'object')
            }
            expect(result).toThrow(new Error('Missing lastName in object'))
        })

        it('should return the field', () => {
            const object = { firstName: 'John', lastName: 'Wick' }
            const result = getOrFail(object.lastName, 'lastName', 'object')
            expect(result).toEqual('Wick')
        })
    })
})
