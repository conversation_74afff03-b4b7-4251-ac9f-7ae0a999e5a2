import { formatAdresseFromReferentiel } from './adresse.utils'
import { refAdresseMock } from '../mocks/refAdresseMock'

describe('AdresseUtils', () => {
    describe('formatAdresse', () => {
        it('transform RefAdresse into human readable address', () => {
            expect(formatAdresseFromReferentiel(refAdresseMock())).toEqual(
                'Bullinger strasse 8p/3+4, 4770 Kolo, LUXEMBOURG'
            )
        })

        it('should handle a different group separator string', () => {
            expect(
                formatAdresseFromReferentiel(refAdresseMock(), 'SPLIT')
            ).toEqual('Bullinger strasse 8p/3+4SPLIT4770 Kolo, LUXEMBOURG')
        })
    })
})
