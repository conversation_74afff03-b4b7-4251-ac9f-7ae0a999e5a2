import { filterUndefinedAndJoinValues, titleCase } from './string.utils'

describe('StringUtils', () => {
    describe('filterUndefinedAndJoinValues', () => {
        it('should transform an array of defined values and join them', () => {
            expect(filterUndefinedAndJoinValues(['coco', 'nut'], ', ')).toBe(
                'coco, nut'
            )
        })
        it('should transform an array of defined and undefined values and join them', () => {
            expect(
                filterUndefinedAndJoinValues(['coco', undefined, 'nut'], ', ')
            ).toBe('coco, nut')
        })
    })

    describe('titleCase', () => {
        it('should transform the first character to a capital letter, and the rest to lowercase', () => {
            expect(titleCase('coconut')).toBe('Coconut')
        })
        it('should lower case the rest of the string ', () => {
            expect(titleCase('a Nice String with CAPITAL letters')).toBe(
                'A nice string with capital letters'
            )
        })
        it('should return undefined if input is undefined ', () => {
            expect(titleCase(undefined as unknown as string)).toBeUndefined()
        })
    })
})
