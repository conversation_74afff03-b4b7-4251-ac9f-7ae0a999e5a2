import {
    ChangeDetectorR<PERSON>,
    Directive,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
    ViewChild,
} from '@angular/core'
import { FormControl, FormGroup } from '@angular/forms'
import { Router } from '@angular/router'
import { WizardSteps } from '../enums/wizard-steps.enum'
import { ViewContainerHostDirective } from '../directives/view-container-host.directive'
import { DeclarationWizardStep } from '../components/declaration-wizard-step'
import { WizardService } from '../services/wizard/wizard.service'
import { DeclarationService } from '../services/declaration/declaration.service'
import { ModalService } from '../services/modal/modal.service'
import { RisquesService } from '../services/risques/risques.service'
import { StepperService } from '../services/stepper-state/stepper.service'
import { RoutingPath } from '../../routing-path.enum'
import { SubRoutingPath } from '../components/stepper-parcours-declaration/enums/sub-routing-path.enum'
import { JsonApiResponseInterface } from '../models/api/json-api-response-interface'
import { DeclarationResult } from '../models/declaration-result'
import { NotificationLevel } from '../enums/notification-level.enum'
import {
    catchError,
    distinctUntilChanged,
    lastValueFrom,
    map,
    Observable,
    of,
    startWith,
    switchMap,
    tap,
} from 'rxjs'
import { Declaration } from '../models/declaration'

@Directive()
export abstract class PiecesJointesPageComponent<
        TForm extends FormGroup,
        TDeclaration extends Declaration,
    >
    extends DeclarationWizardStep<TForm, TDeclaration>
    implements OnInit, OnDestroy
{
    @ViewChild(ViewContainerHostDirective, { static: true })
    viewContainerHost!: ViewContainerHostDirective

    override currentWizardStep = WizardSteps.PIECES_JOINTES
    override previousStep: WizardSteps = WizardSteps.MODALITES

    readonly TRANSLATION_PREFIX = 'pages.pieces-jointes.'
    protected readonly RoutingPath = RoutingPath

    form?: TForm
    declaration$?: Observable<TDeclaration>

    protected constructor(
        protected override readonly wizardService: WizardService,
        protected override readonly declarationService: DeclarationService,
        protected override readonly modalService: ModalService,
        protected override readonly changeDetectorRef: ChangeDetectorRef,
        protected override readonly router: Router,
        protected override readonly risquesService: RisquesService,
        protected readonly stepperService: StepperService
    ) {
        super(
            wizardService,
            declarationService,
            modalService,
            changeDetectorRef,
            router,
            risquesService
        )
    }

    ngOnInit(): void {
        this.subscriptions.add(this.init())
        this.initializeForm()
        this.initializeDeclaration()
        this.initObs()
        this.initFormWithExistingData()
        if (this.form) {
            this.setStepper(this.form)
        }
        this.changeDetectorRef.markForCheck()
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe()
    }

    protected abstract initializeForm(): void

    protected abstract initializeDeclaration(): void

    protected abstract getPiecesJointesControl(
        form: TForm
    ): FormControl<string[]>

    protected isFormValidInPage(): boolean {
        return !!this.form && this.getPiecesJointesControl(this.form).valid
    }

    protected savePageContent(): void {
        if (this.form) {
            const piecesJointesValue = this.getPiecesJointesControl(
                this.form
            ).value
            if (piecesJointesValue) {
                this.declarationService.updatePiecesJointes(piecesJointesValue)
            }
        }
    }

    protected override async onNext(): Promise<boolean> {
        if (this.form?.valid) {
            this.savePageContent()

            await lastValueFrom(
                this.soumettreDeclaration().pipe(
                    tap(() => this.navigateToStep(this.currentWizardStep)),
                    catchError((error) => {
                        this.showNotification({
                            notificationLevel: NotificationLevel.DANGER,
                            message:
                                'common.notification.declaration-sinistre-error',
                        })
                        return of(error)
                    })
                )
            )
            return true
        } else {
            this.showNotification({
                notificationLevel: NotificationLevel.DANGER,
                message: 'common.notification.declaration-sinistre-error',
            })
            return Promise.resolve(false)
        }
    }

    protected override setStepper(form: TForm): void {
        const piecesJointesForm = this.getPiecesJointesControl(form)

        this.subscriptions.add(
            piecesJointesForm.valueChanges
                .pipe(
                    startWith(piecesJointesForm.value),
                    distinctUntilChanged(),
                    map(() => ({
                        routingPath: RoutingPath.PIECES_JOINTES,
                        subPaths: [
                            {
                                path: SubRoutingPath.PIECES_JOINTES,
                                valid: piecesJointesForm.valid,
                            },
                        ],
                    })),
                    tap((stepper) => this.stepperService.setStepper(stepper))
                )
                .subscribe()
        )
    }

    private soumettreDeclaration(): Observable<
        JsonApiResponseInterface<DeclarationResult>
    > {
        return this.declarationService
            .upsertDeclaration()
            .pipe(
                switchMap((result) =>
                    this.declarationService.soumettreDeclaration(
                        result.data.relationships['_entity'].data.id
                    )
                )
            )
    }
}
