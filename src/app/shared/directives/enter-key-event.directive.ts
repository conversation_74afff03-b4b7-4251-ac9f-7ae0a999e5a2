import { Directive, EventEmitter, HostListener, Output } from '@angular/core'

@Directive({
    selector: '[enterKeyEvent]',
    standalone: true,
})
export class EnterKeyEventDirective {
    @Output() enterKeyEvent: EventEmitter<KeyboardEvent> = new EventEmitter()

    @HostListener('keyup.enter', ['$event'])
    onEnterKey(event: KeyboardEvent): void {
        this.enterKeyEvent.emit(event)
    }
}
