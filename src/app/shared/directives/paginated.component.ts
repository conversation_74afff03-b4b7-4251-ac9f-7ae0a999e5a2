import { Pageable } from '../models/pageable.model'
import { BehaviorSubject, Observable } from 'rxjs'
import { Directive, ViewChild } from '@angular/core'
import { PaginationConfigModel } from '../models/pagination-config.model'
import { ModalService } from '../services/modal/modal.service'
import { ViewContainerHostDirective } from './view-container-host.directive'
import { PageSize } from '../enums/page-size.enum'
import { SortDirection } from '../enums/sort-direction.enum'
import { NotificationLevel } from '../enums/notification-level.enum'

@Directive()
export abstract class PaginatedComponent<T> {
    @ViewChild(ViewContainerHostDirective, { static: true })
    viewContainerHost!: ViewContainerHostDirective

    paginationConfig$: Observable<PaginationConfigModel>
    page$: Observable<Pageable<T>> | undefined

    private readonly paginationConfigSubject =
        new BehaviorSubject<PaginationConfigModel>({
            pageIndex: 0,
            pageSize: PageSize.FIVE_ELEMENTS,
            sort: new Map(),
        })

    protected constructor(protected readonly modalService: ModalService) {
        this.paginationConfig$ = this.paginationConfigSubject.asObservable()
    }

    changePage($event: number): void {
        const paginationConfig = this.paginationConfigSubject.value
        this.paginationConfigSubject.next({
            ...paginationConfig,
            pageIndex: $event,
        })
    }

    changePageSize(pageSize: PageSize): void {
        const paginationConfig = this.paginationConfigSubject.value
        this.paginationConfigSubject.next({
            ...paginationConfig,
            pageIndex: 0,
            pageSize,
        })
    }

    changeSortDirection(sortDirections: Map<string, SortDirection>): void {
        const paginationConfig = this.paginationConfigSubject.value
        this.paginationConfigSubject.next({
            ...paginationConfig,
            sort: sortDirections,
        })
    }

    protected abstract configPaginatedParams(): void

    protected abstract configPage(): void

    protected showNotification(inputs: {
        notificationLevel: NotificationLevel
        message: string
    }): void {
        this.modalService.showNotification(
            inputs,
            this.viewContainerHost.viewContainerRef
        )
    }
}
