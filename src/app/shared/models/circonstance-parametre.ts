import { Circonstance } from '../enums/circonstance.enum'
import { TypeGaranties } from '../enums/type-garanties.enum'
import { TypeCirconstance } from '../enums/type-circonstance.enum'

export interface CirconstancesList {
    circonstances: CirconstanceParametre[]
}

export interface CirconstanceParametre {
    code: string
    principale: boolean
    key: Circonstance
    typeCirconstances: TypeCirconstance[]
    garanties?: TypeGaranties[]
}
