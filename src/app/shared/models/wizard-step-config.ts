import { WizardSteps } from '../enums/wizard-steps.enum'
import { WizardAction } from '../enums/wizard-action.enum'

export interface WizardStepConfig {
    step: WizardSteps
    title?: string
    nextStepTitle?: string
    secondaryStepTitle?: string
    secondaryStepSubTitle?: string
    secondaryStepIcon?: string
    isShowNavigation: boolean
    isShowMenuTabs: boolean
    actions?: WizardAction[]
    routePath: string
}
