import { CirconstanceSinistre } from './circonstance-sinistre'
import { Survenance } from './survenance'
import { TypeGaranties } from '../enums/type-garanties.enum'
import { DeclarationHabitation } from '../../habitation/models/declaration-habitation'
import { DeclarationAuto } from '../../auto/models/declaration-auto'
import { ConducteurSinistre } from '../../auto/models/conducteur-sinistre'

export type Declaration = DeclarationHabitation | DeclarationAuto

export interface DeclarationBase {
    id?: string
    updatedAt?: Date
    numeroRisque: string
    referenceProducteur?: string
    survenance: Survenance
    garanties?: TypeGaranties[]
    circonstance?: CirconstanceSinistre
    conducteur?: ConducteurSinistre
    piecesJointes?: string[]
    numeroDossierSinistre?: string
}
