import { TypePrestataire } from '../enums/type-prestataire.enum'
import { AddressLocation } from './address-location.model'

export interface Prestataire {
    numeroPersonne: string
    nomPrenom: string
    typePrestataire: TypePrestataire
    proposeParDefaut: boolean
    codeFSMA: string | null
    numeroAssuralia: number | null
    numeroInformex: number | null
    garagePartenaire: boolean | null
    toutesMarques: boolean | null
    marques: string[] | null
    certifieTesla: boolean | null
    urlSiteWeb: string | null
    location?: AddressLocation
}
