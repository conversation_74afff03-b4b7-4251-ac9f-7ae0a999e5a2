import { EtatGarantie } from '../enums/etat-garantie.enum'
import { TypeGaranties } from '../enums/type-garanties.enum'
import { SituationContratFranchise } from '../wrappers/situations-contrats-wrapper/models/situation-contrat.franchise'

export interface Garantie {
    nom: string
    code: TypeGaranties
    dateEntreeEnVigueur: Date
    franchise: string
    etat: EtatGarantie
    franchises: SituationContratFranchise[]
}
