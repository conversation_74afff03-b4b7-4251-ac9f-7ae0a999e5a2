import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms'
import { TiersFormStructure } from './tiers-form-structure'
import { AdresseForm } from './adresse.form'
import { Tiers } from '../tiers'

export class TiersForm extends FormArray<FormGroup<TiersFormStructure>> {
    constructor() {
        super([])
    }

    getValues(): Tiers[] {
        return this.value.filter(
            (t) => t.nom !== null && t.nom !== undefined && t.nom.trim() !== ''
        ) as Tiers[]
    }

    createArrayControl(): void {
        this.push(
            new FormGroup<TiersFormStructure>({
                nom: new FormControl(undefined, {
                    nonNullable: true,
                    validators: [Validators.required],
                }),
                prenom: new FormControl(undefined, {
                    nonNullable: true,
                }),
                numeroPlaque: new FormControl(undefined, {
                    nonNullable: true,
                }),
                marqueVehicule: new FormControl(undefined, {
                    nonNullable: true,
                }),
                compagnieAdverse: new FormControl(undefined, {
                    nonNullable: true,
                }),
                contratAssuranceChezCompagnieAdverse: new FormControl(
                    undefined,
                    {
                        nonNullable: true,
                    }
                ),
                tiersBlesse: new FormControl(undefined, {
                    nonNullable: true,
                }),
                adresse: new AdresseForm(),
            })
        )
    }
}
