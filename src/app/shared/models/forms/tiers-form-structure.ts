import { FormControl } from '@angular/forms'
import { OuiNonInconnu } from '../../enums/oui-non-inconnu.enum'
import { AdresseForm } from './adresse.form'

export interface TiersFormStructure {
    nom: FormControl<string | undefined>
    prenom: FormControl<string | undefined>
    numeroPlaque: FormControl<string | undefined>
    marqueVehicule: FormControl<string | undefined>
    compagnieAdverse: FormControl<string | undefined>
    contratAssuranceChezCompagnieAdverse: FormControl<string | undefined>
    adresse: AdresseForm
    tiersBlesse: FormControl<OuiNonInconnu | undefined>
}
