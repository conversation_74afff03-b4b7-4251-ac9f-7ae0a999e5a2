import { FormControl, FormGroup, Validators } from '@angular/forms'
import { Adresse, CODE_PAYS_BELGIQUE } from '../adresse'
import { AdresseFormStructure } from './adresse-form-structure'

export class AdresseForm extends FormGroup<AdresseFormStructure> {
    constructor(adresse?: Adresse, isRequired: boolean = false) {
        super({
            pays: new FormControl(adresse?.pays ?? CODE_PAYS_BELGIQUE, {
                nonNullable: true,
                validators: isRequired ? [Validators.required] : [],
            }),
            codePostal: new FormControl(adresse?.codePostal, {
                nonNullable: true,
            }),
            localite: new FormControl(adresse?.localite, {
                nonNullable: true,
                validators: isRequired ? [Validators.required] : [],
            }),
            nomRue: new FormControl(adresse?.nomRue, {
                nonNullable: true,
            }),
            numeroRue: new FormControl(adresse?.numeroRue, {
                nonNullable: true,
            }),
        })
    }

    getValue(): Adresse {
        return this.value as Adresse
    }
}
