/* eslint-disable no-unused-vars */
import { JsonApiLinks } from './json-api-links'
import { JsonApiMeta } from './json-api-meta'
import { JsonApiData } from './json-api-data'
import { JsonApiDataAttributes } from './json-api-data-attributes'

export interface JsonApiResponseInterface<T> {
    data: T extends Array<infer E>
        ? JsonApiData<E & JsonApiDataAttributes>[]
        : JsonApiData<T & JsonApiDataAttributes>
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    links?: T extends Array<infer E> ? JsonApiLinks : never
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    meta?: T extends Array<infer E> ? JsonApiMeta : never
    included?: JsonApiData<JsonApiDataAttributes>[]
}
