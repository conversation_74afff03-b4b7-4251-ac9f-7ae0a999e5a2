import { <PERSON>aran<PERSON> } from './garantie'
import { Personne } from '../wrappers/personne-wrapper/models/personne/personne'
import { Labeled } from './labeled'
import { StatutRisque } from '../enums/statut-risque.enum'
import { SituationAdresseRisque } from '../wrappers/situations-contrats-wrapper/models/situation-adresse.risque'

export interface Risque {
    familleProduit: string
    preneur?: Personne
    numeroPolice: string
    numeroRisque: string
    status: Labeled<StatutRisque>
    adresse: SituationAdresseRisque
    codeProduit: string
    typeRisque: string
    dateEffet: Date
    garanties: Garantie[]
    plaque?: string
    conducteurPrincipal?: number
    conducteursSecondaires?: number[]
    marque?: string
    modele?: string
}
