import { AbstractControl, ValidatorFn } from '@angular/forms'

export const nonNullableArrayValidator =
    (): ValidatorFn =>
    (control: AbstractControl): { [key: string]: any } | null => {
        const value: any[] = control.value
        if (value === undefined || value === null) {
            return { nonNullableArray: true }
        }
        if (Array.isArray(value)) {
            return null
        }
        return { nonNullableArray: true }
    }
