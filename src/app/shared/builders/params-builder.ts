import { HttpParams } from '@angular/common/http'
import { Filter } from './filter'

export class ParamsBuilder {
    private params: HttpParams = new HttpParams()

    addParam(key: string, value: string | number | boolean): this {
        this.params = this.params.append(key, value)
        return this
    }

    addParamIfDefined(key: string, value?: string | number): this {
        if (value) {
            this.params = this.params.set(key, value)
        }
        return this
    }

    addParamFilters(filters: Filter[]): this {
        for (const filter of filters) {
            this.addParam(filter.key, filter.value)
        }
        return this
    }

    build(): HttpParams {
        return this.params
    }
}
