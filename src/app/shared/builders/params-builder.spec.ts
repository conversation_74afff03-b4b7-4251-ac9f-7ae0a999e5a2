import { ParamsBuilder } from './params-builder'

describe('ParamsBuilder', () => {
    describe('addParamIfDefined', () => {
        it('should not add parameter if its not defined', () => {
            const params = new ParamsBuilder()
            params.addParamIfDefined('param1', undefined)
            params.addParamIfDefined('param2', 'somevalue')
            expect(params.build().toString()).toEqual('param2=somevalue')
        })
    })
})
