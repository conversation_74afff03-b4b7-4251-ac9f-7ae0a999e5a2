import { RSQLValueFilterExpression } from './rsql-value-filter-expression'
import { StringOrNumberOrDateOrBoolean } from './base-rsql-filter-expression'

export abstract class SingleValueRSQLFilterExpression extends RSQLValueFilterExpression {
    protected constructor(field: string, value: StringOrNumberOrDateOrBoolean) {
        super(field, value)
    }

    getValueStringAccordingToType(
        value: string | number | Date | boolean
    ): string {
        if (value instanceof Date) {
            return value.toISOString()
        } else if (value === null) {
            return 'null'
        }

        const typeOfValue: string = typeof value
        switch (typeOfValue) {
            case 'number':
                return value.toString()
            case 'boolean':
                return this.value ? 'true' : 'false'
            default: {
                const valueAsString = (value as string)
                    .replace(/\\/g, '\\\\')
                    .replace(/"/g, '\\"')
                return `"${valueAsString}"`
            }
        }
    }
}
