import { SingleValueRSQLFilterExpression } from './single-value-rsql-filter-expression'
import { StringOrNumberOrDateOrBoolean } from './base-rsql-filter-expression'

export class EqualToRSQLFilterExpression extends SingleValueRSQLFilterExpression {
    constructor(field: string, value: StringOrNumberOrDateOrBoolean) {
        super(field, value)
    }

    getFieldString(): string {
        return `${this.field}==`
    }
}
