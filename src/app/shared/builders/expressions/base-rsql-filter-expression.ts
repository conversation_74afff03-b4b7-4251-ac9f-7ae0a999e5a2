export type StringOrNumberOrBoolean = string | number | boolean
export type StringOrNumberOrDateOrBoolean = string | number | boolean | Date
export type RSQLAnyType =
    | StringOrNumberOrDateOrBoolean
    | Array<StringOrNumberOrBoolean>

export abstract class BaseRsqlFilterExpression {
    protected readonly field: string

    protected constructor(field: string) {
        this.field = field
    }

    build(): string {
        const fieldString: string = this.getFieldString()
        return `${fieldString}`
    }

    abstract getFieldString(): string
}
