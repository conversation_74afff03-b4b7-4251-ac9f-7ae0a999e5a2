import { BaseRsqlFilterExpression } from './base-rsql-filter-expression'
import { RSQLFilterList } from './rsql-filter-list'

export class GroupRSQLFilterExpression extends BaseRsqlFilterExpression {
    constructor(private readonly filterList: RSQLFilterList) {
        super('')
    }

    override build(): string {
        return this.filterList.build()
    }

    getFieldString(): string {
        return ''
    }
}
