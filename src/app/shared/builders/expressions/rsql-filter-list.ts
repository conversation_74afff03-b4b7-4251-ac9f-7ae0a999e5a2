import { BaseRsqlFilterExpression } from './base-rsql-filter-expression'

export class RSQLFilterList {
    private readonly andList: Array<BaseRsqlFilterExpression> = []
    private readonly orList: Array<BaseRsqlFilterExpression> = []

    public and(filter: BaseRsqlFilterExpression): this {
        if (filter !== undefined && filter !== null && filter.build() !== '') {
            this.andList.push(filter)
        }
        return this
    }

    public or(filter: BaseRsqlFilterExpression): this {
        if (filter !== undefined && filter !== null && filter.build() !== '') {
            this.orList.push(filter)
        }
        return this
    }

    public build(): string {
        const filterString = []

        if (this.andList.length > 0) {
            const andFilterString = this.buildList(this.andList, ' AND ')
            filterString.push(andFilterString)
        }

        if (this.orList.length > 0) {
            if (filterString.length > 0) {
                filterString.push(' OR ')
            }
            const orFilterString = this.buildList(this.orList, ' OR ')
            filterString.push(orFilterString)
        }

        return filterString.join('')
    }

    private buildList(
        list: Array<BaseRsqlFilterExpression>,
        connector: string
    ): string {
        const filterString = []
        const includeParenthesis = list.length > 1

        if (includeParenthesis) {
            filterString.push('(')
        }

        filterString.push(list.map((filter) => filter.build()).join(connector))

        if (includeParenthesis) {
            filterString.push(')')
        }

        return filterString.join('')
    }
}
