import {
    BaseRsqlFilterExpression,
    RSQLAnyType,
} from './base-rsql-filter-expression'

export abstract class RSQLValueFilterExpression extends BaseRsqlFilterExpression {
    protected readonly value: RSQLAnyType
    protected constructor(field: string, value: RSQLAnyType) {
        super(field)
        this.value = value
    }

    override build(): string {
        const fieldString: string = this.getFieldString()
        const valueString: string = this.getValueStringAccordingToType(
            this.value
        )
        return `${fieldString}${valueString}`
    }

    abstract getValueStringAccordingToType(
        value: string | number | boolean | Date | (string | number | boolean)[]
    ): string
}
