import { filterByKeyAndTypeConfig } from '../configs/filter-key-by-type.config'
import { Filter } from './filter'
import { RSQLFilterList } from './expressions/rsql-filter-list'
import { FilterType } from '../enums/filter-type.enum'

export class FiltersBuilder {
    private readonly filters: Filter[] = []

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    addFilter(type: FilterType, value: any): this {
        if (type && value) {
            const key = filterByKeyAndTypeConfig.get(type)
            if (key) {
                this.filters.push({
                    type,
                    key,
                    value,
                })
            }
        }
        return this
    }

    withRSQLCustomQuery(rsqlFilterList: RSQLFilterList): this {
        const query = rsqlFilterList.build()
        if (query && query.length > 0) {
            const type = FilterType.CUSTOM_RSQL_QUERY_TYPE
            const key = FilterType.CUSTOM_RSQL_QUERY_KEY
            if (key) {
                this.filters.push({
                    type,
                    key,
                    value: query,
                })
            }
        }
        return this
    }

    build(): Filter[] {
        return this.filters.slice()
    }
}
