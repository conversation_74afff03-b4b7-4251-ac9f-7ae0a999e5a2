import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DetailsSinistreAutoComponent } from './details-sinistre-auto.component'
import { DetailsSinistreAutoForm } from './forms/details-sinistre-auto.form'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { expectElementToNotExist } from '../../../shared/utils/test.utils'
import { CauseSinistre } from '../../enums/cause-sinistre.enum'
import { OuiNonInconnu } from '../../../shared/enums/oui-non-inconnu.enum'
import { validDetailsAutoMock } from '../../../shared/mocks/circonstances.mock'
import { AdresseService } from '../../../shared/services/adresse/adresse.service'
import { AdresseServiceMock } from '../../../shared/services/adresse/adresse.service.spec.mock'

describe('DetailsSinistreAutoComponent', () => {
    let component: DetailsSinistreAutoComponent
    let fixture: ComponentFixture<DetailsSinistreAutoComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DetailsSinistreAutoComponent],
            providers: [
                provideNoopAnimations(),
                { provide: AdresseService, useClass: AdresseServiceMock },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(DetailsSinistreAutoComponent)
        component = fixture.componentInstance
        fixture.componentRef.setInput('form', new DetailsSinistreAutoForm())
        fixture.detectChanges()
    })

    it('should be valid when all answers set to something valid for COLLISION_ENTRE_VEHICULES', () => {
        const form = new DetailsSinistreAutoForm()
        form.controls.tiers.createArrayControl()
        form.controls.tiers.createArrayControl()
        form.patchValue(validDetailsAutoMock())
        fixture.componentRef.setInput('form', form)
        fixture.componentRef.setInput(
            'causeSinistre',
            CauseSinistre.COLLISION_ENTRE_VEHICULES
        )
        fixture.detectChanges()

        expect(component.form().valid).toBeTruthy()
    })

    describe('numeroProcesVerbal', () => {
        it('should be hidden by default', () => {
            expectElementToNotExist(fixture, 'input#numeroProcesVerbal')
        })

        it('should be hidden when interventionPoliceOuPompiers is false', () => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            component
                .form()
                .controls.interventionPoliceOuPompiers.setValue(false)
            fixture.detectChanges()

            expectElementToNotExist(fixture, '#numeroProcesVerbal')
        })

        it('should be hidden when avecNumeroProcesVerbal is false', () => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            component
                .form()
                .controls.interventionPoliceOuPompiers.setValue(true)
            component.form().controls.avecNumeroProcesVerbal.setValue(false)
            fixture.detectChanges()

            expectElementToNotExist(fixture, '#numeroProcesVerbal')
        })

        it('should disable numeroProcesVerbal field when avecNumeroProcesVerbal is false', () => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            component
                .form()
                .controls.interventionPoliceOuPompiers.setValue(true)
            component.form().controls.avecNumeroProcesVerbal.setValue(false)
            fixture.detectChanges()

            expect(
                component.form().controls.numeroProcesVerbal.disabled
            ).toBeTruthy()
        })
    })

    describe('tauxAlcoolemie', () => {
        beforeEach(() => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            fixture.detectChanges()
        })

        it('should be hidden by default', () => {
            expectElementToNotExist(fixture, '#tauxAlcoolemie')
        })

        it('should be hidden when conducteurTestAlcoolemie is not OUI', () => {
            component
                .form()
                .controls.conducteurTestAlcoolemie.setValue(OuiNonInconnu.NON)
            expectElementToNotExist(fixture, '#tauxAlcoolemie')
        })

        it('should disable tauxAlcoolemie field when conducteurTestAlcoolemie is not OUI', () => {
            component
                .form()
                .controls.conducteurTestAlcoolemie.setValue(OuiNonInconnu.NON)
            expect(
                component.form().controls.tauxAlcoolemie.disabled
            ).toBeTruthy()
        })
    })

    describe('resultatStup', () => {
        beforeEach(() => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            fixture.detectChanges()
        })

        it('should be hidden by default', () => {
            expectElementToNotExist(
                fixture,
                'yes-no-choice-card[formControlName="resultatStup"]'
            )
        })

        it('should be hidden when conducteurTestStup is not OUI', () => {
            component
                .form()
                .controls.conducteurTestStup.setValue(OuiNonInconnu.NON)
            expectElementToNotExist(
                fixture,
                'yes-no-choice-card[formControlName="resultatStup"]'
            )
        })

        it('should disable resultatStup field when conducteurTestStup is not OUI', () => {
            component
                .form()
                .controls.conducteurTestStup.setValue(OuiNonInconnu.NON)
            expect(component.form().controls.resultatStup.disabled).toBeTruthy()
        })
    })

    describe('typeAnimal', () => {
        it('should be visible only for DEGATS_CAUSES_PAR_UN_ANIMAL cause', () => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            fixture.detectChanges()

            expectElementToNotExist(
                fixture,
                'select[formControlName="typeAnimal"]'
            )

            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.DEGATS_CAUSES_PAR_UN_ANIMAL
            )
            fixture.detectChanges()

            const typeAnimalSelect = fixture.nativeElement.querySelector(
                'select[formControlName="typeAnimal"]'
            )
            expect(typeAnimalSelect).not.toBeNull()
        })
    })

    describe('natureVol', () => {
        it('should be visible only for VOL cause', () => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            fixture.detectChanges()

            expectElementToNotExist(
                fixture,
                'select[formControlName="natureVol"]'
            )

            fixture.componentRef.setInput('causeSinistre', CauseSinistre.VOL)
            fixture.detectChanges()
            const natureVolSelect = fixture.nativeElement.querySelector(
                'select[formControlName="natureVol"]'
            )

            expect(natureVolSelect).not.toBeNull()
        })
    })

    describe('typeEvenement', () => {
        it('should be visible only for FORCE_DE_LA_NATURE cause', () => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            fixture.detectChanges()

            expectElementToNotExist(
                fixture,
                'select[formControlName="typeEvenement"]'
            )

            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.FORCE_DE_LA_NATURE
            )
            fixture.detectChanges()
            const typeEvenementSelect = fixture.nativeElement.querySelector(
                'select[formControlName="typeEvenement"]'
            )

            expect(typeEvenementSelect).not.toBeNull()
        })
    })

    describe('elementVehiculeEndommage', () => {
        it('should be visible only for BRIS_DE_VITRE cause', () => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            fixture.detectChanges()
            expectElementToNotExist(
                fixture,
                'select[formControlName="elementVehiculeEndommage"]'
            )

            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.BRIS_DE_VITRE
            )
            fixture.detectChanges()
            const elementVehiculeEndommageSelect =
                fixture.nativeElement.querySelector(
                    'select[formControlName="elementVehiculeEndommage"]'
                )

            expect(elementVehiculeEndommageSelect).not.toBeNull()
        })
    })

    describe('tiers', () => {
        beforeEach(() => {
            fixture.componentRef.setInput(
                'causeSinistre',
                CauseSinistre.COLLISION_ENTRE_VEHICULES
            )
            fixture.detectChanges()
        })

        it('should enable tiersIdentifie when tiersImplique is true', () => {
            component.form().controls.tiersImplique.setValue(false)
            expect(component.form().controls.tiersIdentifie.enabled).toBeFalsy()

            component.form().controls.tiersImplique.setValue(true)
            expect(
                component.form().controls.tiersIdentifie.enabled
            ).toBeTruthy()
        })

        it('should enable tiers when tiersIdentifie is true', () => {
            component.form().controls.tiersImplique.setValue(true)
            component.form().controls.tiersIdentifie.setValue(false)
            expect(component.form().controls.tiers.enabled).toBeFalsy()

            component.form().controls.tiersImplique.setValue(true)
            component.form().controls.tiersIdentifie.setValue(true)
            expect(
                component.form().controls.tiersIdentifie.enabled
            ).toBeTruthy()
        })
    })
})
