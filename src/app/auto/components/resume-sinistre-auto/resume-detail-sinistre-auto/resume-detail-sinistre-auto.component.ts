import { ChangeDetectionStrategy, Component, input } from '@angular/core'
import { ResumeCirconstanceComponent } from '../../../../shared/components/resume-sinistre/resume-circonstance/resume-circonstance.component'
import { I18nPipe } from '@foyer/ng-i18n'
import { DetailsSinistreAuto } from '../../../models/details-sinistre-auto'
import { CirconstanceSinistre } from '../../../../shared/models/circonstance-sinistre'
import { CauseSinistre } from '../../../enums/cause-sinistre.enum'
import { DataTileComponent } from '../../../../shared/components/data-tile/data-tile.component'
import { ResumeTiersComponent } from '../../../../shared/components/resume-sinistre/resume-tiers/resume-tiers.component'
import { OuiNonInconnu } from '../../../../shared/enums/oui-non-inconnu.enum'
import { ResumeConducteurComponent } from '../resume-conducteur/resume-conducteur.component'
import { ConducteurSinistre } from '../../../models/conducteur-sinistre'
import { hasNonNullValues } from '../../../../shared/utils/objects.utils'

@Component({
    selector: 'resume-detail-sinistre-auto',
    standalone: true,
    imports: [
        ResumeCirconstanceComponent,
        I18nPipe,
        DataTileComponent,
        ResumeTiersComponent,
        ResumeConducteurComponent,
    ],
    templateUrl: './resume-detail-sinistre-auto.component.html',
    styleUrl: './resume-detail-sinistre-auto.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResumeDetailSinistreAutoComponent {
    causeSinistre = input<CauseSinistre>()
    detailsSinistre = input.required<DetailsSinistreAuto>()
    circonstance = input.required<CirconstanceSinistre>()
    conducteur = input.required<ConducteurSinistre>()

    readonly TRANSLATION_PREFIX: string =
        'common.resume-sinistre-auto.resume-detail-sinistre-auto.'
    readonly DETAILS_SINISTRE_TRANSLATION_PREFIX: string =
        'common.details-sinistre-auto.'

    protected readonly OuiNonInconnu = OuiNonInconnu
    protected readonly hasNonNullValues = hasNonNullValues
}
