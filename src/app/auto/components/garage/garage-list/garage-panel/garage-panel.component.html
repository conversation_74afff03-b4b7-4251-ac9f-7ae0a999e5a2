@if (garage(); as garage) {
    <div class="DataTile garageDataTile u-has-padding-vertical-16 u-has-padding-horizontal-24 is-full-width" (click)="selectGarage(garage)">
        <div class="DataTile-content u-has-padding-0">
            @if (garage.garagePartenaire) {
                <div>
                    <span class="Badge agreeFoyerBadge is-small is-inverted u-is-txt-bold">
                        {{ TRANSLATION_PREFIX + 'badge-garage-agree-foyer' | translate }}
                    </span>
                </div>
            }

            <div class="DataTile-label-medium nomGarage u-is-txt-monospace u-has-margin-vertical-4 u-is-txt-primary-500">
                {{ garage.nomPrenom }}
            </div>

            @if (distanceInKm && distanceInKm > 0) {
                <div class="DataTile-value sub-value u-is-txt-monospace u-has-margin-vertical-4">
                    {{ TRANSLATION_PREFIX + 'distance-in-km' | translate: { distance: distanceInKm } }}
                </div>
            }

            @if (personne$ | async; as personne) {
                @if (personne.adresseCorrespondance) {
                    <div class="DataTile-value adresseGarage sub-value u-is-txt-monospace">
                        <span [innerHTML]="personne.adresseCorrespondance | prettifyReferentielAdresse"></span>
                    </div>
                }
            }
        </div>

        <ng-content></ng-content>
    </div>
}
