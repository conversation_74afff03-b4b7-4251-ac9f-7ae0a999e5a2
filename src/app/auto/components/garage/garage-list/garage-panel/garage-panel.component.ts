import {
    ChangeDetectionStrategy,
    Component,
    inject,
    input,
    OnInit,
    output,
} from '@angular/core'
import { AsyncPipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { Prestataire } from '../../../../../shared/models/prestataire.model'
import { PrettifyReferentielAdressePipe } from '../../../../../shared/pipes/adresse/prettify-refentiel-adresse.pipe'
import { Observable } from 'rxjs'
import { Personne } from '../../../../../shared/wrappers/personne-wrapper/models/personne/personne'
import { PersonneService } from '../../../../../shared/services/personne/personne.service'
import { AddressLocation } from '../../../../../shared/models/address-location.model'
import { TypeResultat } from '../../../../enums/type-resultat.enum'
import { TypeAddressLocation } from '../../../../../shared/enums/type-address-location.enum'
import { calculerDistanceInKm } from '../../../../../shared/utils/distance.utils'
import { SearchResultItem } from '../../../../models/search-result-item'

@Component({
    selector: 'garage-panel',
    templateUrl: './garage-panel.component.html',
    styleUrls: ['./garage-panel.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [I18nPipe, PrettifyReferentielAdressePipe, AsyncPipe],
})
export class GaragePanelComponent implements OnInit {
    private readonly personneService: PersonneService = inject(PersonneService)

    garage = input<Prestataire | undefined>(undefined)
    localiteOrGarageSearchResultItem = input<
        SearchResultItem | null | undefined
    >(undefined)

    selectedGarage = output<Prestataire>()

    readonly TRANSLATION_PREFIX: string =
        'common.garage.garage-list.garage-panel.'

    distanceInKm?: number
    personne$?: Observable<Personne>

    ngOnInit(): void {
        this.distanceInKm = this.calculerDistanceInKm()

        const garage = this.garage()
        if (garage?.numeroPersonne) {
            this.personne$ = this.personneService.load(garage.numeroPersonne)
        }
    }

    selectGarage(prestataire: Prestataire) {
        this.selectedGarage.emit(prestataire)
    }

    private getPointDeReference(): AddressLocation | undefined {
        const localiteOrGarageSearchResultItem =
            this.localiteOrGarageSearchResultItem()

        if (
            localiteOrGarageSearchResultItem &&
            localiteOrGarageSearchResultItem.typeResult ===
                TypeResultat.LOCALITE &&
            localiteOrGarageSearchResultItem.longitude &&
            localiteOrGarageSearchResultItem.latitude
        ) {
            return {
                type: TypeAddressLocation.POINT,
                coordinates: [
                    localiteOrGarageSearchResultItem.longitude,
                    localiteOrGarageSearchResultItem.latitude,
                ],
            }
        } else {
            return undefined
        }
    }

    private calculerDistanceInKm(): number | undefined {
        const garageCoords = this.garage()?.location?.coordinates
        const pointReferenceCoords = this.getPointDeReference()?.coordinates

        return garageCoords?.length === 2 && pointReferenceCoords?.length === 2
            ? calculerDistanceInKm(
                  garageCoords[0],
                  garageCoords[1],
                  pointReferenceCoords[0],
                  pointReferenceCoords[1]
              )
            : undefined
    }
}
