import { ComponentFixture, TestBed } from '@angular/core/testing'
import { CirconstancesAutoPageComponent } from './circonstances-auto-page.component'
import { ParametresService } from '../../../shared/services/parametres/parametres.service'
import { ParametresServiceMock } from '../../../shared/services/parametres/parametres.service.spec.mock'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { provideRouter, Router } from '@angular/router'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { RisquesServiceMock } from '../../../shared/services/risques/risques.service.mock'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { DeclarationKind } from '../../../shared/enums/declaration-kind.enum'
import { Circonstance } from '../../../shared/enums/circonstance.enum'
import { DeclarationServiceMock } from '../../../shared/services/declaration/declaration.service.mock'
import { CauseSinistre } from '../../enums/cause-sinistre.enum'
import { provideDate } from '../../../core/providers/date.provider'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { of } from 'rxjs'
import { WizardServiceMock } from '../../../shared/services/wizard/wizard-service.mock'

describe('CirconstancesAutoPageComponent', () => {
    let component: CirconstancesAutoPageComponent
    let fixture: ComponentFixture<CirconstancesAutoPageComponent>
    let wizardService: WizardService
    let router: Router
    let modalService: ModalService
    let declarationService: DeclarationService
    let stepperService: StepperService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [CirconstancesAutoPageComponent],
            providers: [
                provideRouter([]),
                provideTranslation(),
                provideDate(),
                provideNoopAnimations(),
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: ParametresService,
                    useClass: ParametresServiceMock,
                },
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
                {
                    provide: WizardService,
                    useClass: WizardServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(CirconstancesAutoPageComponent)
        component = fixture.componentInstance
        declarationService = TestBed.inject(DeclarationService)
        wizardService = TestBed.inject(WizardService)
        modalService = TestBed.inject(ModalService)
        router = TestBed.inject(Router)
        stepperService = TestBed.inject(StepperService)
        declarationService.updateDeclaration({
            _kind: DeclarationKind.AUTO,
            survenance: {
                dateDeSurvenance: new Date(),
            },
            causeSinistre: CauseSinistre.VANDALISME,
            numeroRisque: '20451495-5-1',
        })
        declarationService.initializeForm(DeclarationKind.AUTO)
    })

    describe('initial page load', () => {
        it('should load existing values for circonstances page', () => {
            spyOn(declarationService, 'getDeclarationAuto$').and.returnValue(
                of({
                    _kind: DeclarationKind.AUTO,
                    survenance: {
                        dateDeSurvenance: new Date(),
                    },
                    causeSinistre: CauseSinistre.VANDALISME,
                    numeroRisque: '20451495-5-1',
                    circonstance: {
                        circonstance: Circonstance.AUTRE,
                        complementDeCirconstance:
                            'ceci est un complément de circonstance',
                    },
                })
            )
            fixture.detectChanges()

            expect(component.form?.controls.referenceProducteur.value).toEqual(
                'coconut'
            )
            expect(
                component.form?.controls.modalites.controls.compteBancaire.value
            ).toEqual('BE50363051592118')
        })
    })
})
