import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AuthenticationService } from '@foyer/authentication'
import { AppComponent } from './app.component'
import { HttpErrorResponse } from '@angular/common/http'
import { provideRouter } from '@angular/router'
import { provideTranslation } from './core/providers/translation.provider'
import { ModalService } from './shared/services/modal/modal.service'
import { StorageService } from './shared/services/storage/storage.service'
import { ModalServiceMock } from './shared/services/modal/modal.service.mock'
import { AuthenticationServiceMock } from './shared/mocks/authentication.service.mock'
import { DeclarationServiceMock } from './shared/services/declaration/declaration.service.mock'
import { DeclarationService } from './shared/services/declaration/declaration.service'
import { StorageServiceMock } from './shared/services/storage/storage.service.mock'
import Spy = jasmine.Spy

describe('AppComponent', () => {
    let authenticationService: AuthenticationService
    let modalService: ModalService
    let fixture: ComponentFixture<AppComponent>
    let showAsComponentSpy: Spy
    let storageService: StorageService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [AppComponent],
            providers: [
                provideRouter([]),
                provideTranslation(),
                {
                    provide: ModalService,
                    useClass: ModalServiceMock,
                },
                {
                    provide: AuthenticationService,
                    useClass: AuthenticationServiceMock,
                },
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: StorageService,
                    useClass: StorageServiceMock,
                },
            ],
        }).compileComponents()
        authenticationService = TestBed.inject(AuthenticationService)
        modalService = TestBed.inject(ModalService)
        storageService = TestBed.inject(StorageService)
        fixture = TestBed.createComponent(AppComponent)
        showAsComponentSpy = spyOn(
            modalService,
            'showAsComponent'
        ).and.callThrough()
    })

    describe('When the user session expires', () => {
        beforeEach(() => {
            fixture.detectChanges()
            authenticationService.throwAuthenticationError(
                new HttpErrorResponse({ error: 'session expired', status: 401 })
            )
        })

        it('should show error modal', () => {
            expect(showAsComponentSpy).toHaveBeenCalledTimes(1)
        })
    })

    describe('When the user is not allowed to access to a specific resource', () => {
        beforeEach(() => {
            fixture.detectChanges()
            authenticationService.throwAuthenticationError(
                new HttpErrorResponse({
                    error: 'forbidden access',
                    status: 403,
                })
            )
        })

        it('should show error modal', () => {
            expect(showAsComponentSpy).toHaveBeenCalledTimes(1)
        })
    })

    describe('Session storage', () => {
        beforeEach(() => {
            spyOn(authenticationService, 'login')
        })

        it('should call login from authenticationService if PORTIMA_SESSION_STORAGE_KEY is false', () => {
            spyOn(storageService, 'retrieve').and.returnValue(false)
            fixture.detectChanges()
            expect(authenticationService.login).toHaveBeenCalledOnceWith(
                window.location.pathname + window.location.search
            )
        })

        it('should call login from authenticationService if token is expired', () => {
            spyOn(storageService, 'isTokenExpired').and.returnValue(true)
            fixture.detectChanges()
            expect(authenticationService.login).toHaveBeenCalledOnceWith(
                window.location.pathname + window.location.search
            )
        })

        it('should not call login from authenticationService if session storage params are good', () => {
            fixture.detectChanges()
            expect(authenticationService.login).not.toHaveBeenCalled()
        })
    })
})
