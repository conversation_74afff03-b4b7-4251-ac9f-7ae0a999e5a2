<div class="LAYOUT-SECTION">
    <div class="L-CONTENT">
        @if (form) {
            <form class="Form u-is-txt-monospace" [formGroup]="asFormGroup(form)">
                <div class="GridFlex">
                    <div class="GridFlex-row">
                        <div class="GridFlex-col-12 GridFlex-col-lg-6">
                            <div class="GridFlex-row Form-row">
                                <div class="GridFlex-col-12">
                                    @if (personne$ | async; as personne) {
                                        <div class="Panel person-panel">
                                            <person-datatile [personne]="personne"></person-datatile>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="GridFlex-row Form-row u-has-margin-bottom-16">
                                <div class="GridFlex-col-12">
                                    <div class="Form-field is-medium">
                                        <label for="referenceProducteur" class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'reference-producteur' | translate }}
                                            <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                type="text"
                                                id="referenceProducteur"
                                                formControlName="referenceProducteur"
                                                class="Input u-is-full-width is-medium"
                                                [placeholder]="TRANSLATION_PREFIX + 'reference-producteur' | translate" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @if (form.controls.survenance; as survenanceForm) {
                                <survenance-habitation [form]="survenanceForm"></survenance-habitation>
                            }
                            @if (situationContratRisque$ | async; as situationRisque) {
                                <div class="GridFlex-row Form-row">
                                    <div class="GridFlex-col-12 ng-invalid">
                                        @if (form.controls.garanties.invalid && form.controls.garanties.touched) {
                                            <message-banner>
                                                @if (form.controls.garanties.errors && form.controls.garanties.errors['nonNullableArray']) {
                                                    <span>
                                                        {{ TRANSLATION_PREFIX + 'garanties-required' | translate }}
                                                    </span>
                                                }
                                            </message-banner>
                                        }
                                        <garanties-concernees [garanties]="situationRisque.garanties" formControlName="garanties"></garanties-concernees>
                                    </div>
                                </div>
                                <organisme-responsable-garanties
                                    [organismeResponsableGaranties]="OrganismeResponsableGaranties.EUROP_ASSISTANCE"
                                    [garanties]="
                                        filtrerGarantiesByTypes(situationRisque.garanties, EUROP_ASSISTANCE_GARANTIES)
                                    "></organisme-responsable-garanties>
                                <organisme-responsable-garanties
                                    [organismeResponsableGaranties]="OrganismeResponsableGaranties.FOYER_ARAG_SA"
                                    [garanties]="filtrerGarantiesByTypes(situationRisque.garanties, FOYER_ARAG_SA_GARANTIES)"></organisme-responsable-garanties>
                                <organisme-responsable-garanties
                                    [organismeResponsableGaranties]="OrganismeResponsableGaranties.ARAG_BELGIUM"
                                    [garanties]="filtrerGarantiesByTypes(situationRisque.garanties, ARAG_BELGIUM_GARANTIES)"></organisme-responsable-garanties>
                            }
                        </div>
                        <div class="GridFlex-col-12 GridFlex-col-lg-4">
                            <resume-sinistre-habitation></resume-sinistre-habitation>
                            <stepper-parcours-declaration [currentPath]="RoutingPath.GARANTIES"></stepper-parcours-declaration>
                        </div>
                    </div>
                </div>
            </form>
        }
    </div>
</div>
<ng-template viewContainerHost></ng-template>
