import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnInit,
    ViewChild,
} from '@angular/core'
import { distinctUntilChanged, map, Observable, startWith, tap } from 'rxjs'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { Router } from '@angular/router'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { DeclarationHabitationForm } from '../../models/forms/declaration-habitation.form'
import { DeclarationHabitationFormStructure } from '../../models/forms/declaration-habitation-form-structure'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { ViewContainerHostDirective } from '../../../shared/directives/view-container-host.directive'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { StepperParcoursDeclarationComponent } from '../../../shared/components/stepper-parcours-declaration/stepper-parcours-declaration.component'
import { OrganismeResponsableGarantiesComponent } from '../../../shared/components/organisme-responsable-garanties/organisme-responsable-garanties.component'
import { GarantiesConcerneesComponent } from '../../../shared/components/garanties-concernees/garanties-concernees.component'
import { MessageBannerComponent } from '../../../shared/components/message-banner/message-banner.component'
import { PersonDatatileComponent } from '../../../shared/components/person-datatile/person-datatile.component'
import { AsyncPipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { DeclarationWizardStep } from '../../../shared/components/declaration-wizard-step'

import { OrganismeResponsableGaranties } from '../../../shared/enums/organisme-responsable-garanties.enum'
import { WizardSteps } from 'src/app/shared/enums/wizard-steps.enum'
import { RoutingPath } from '../../../routing-path.enum'
import { maxDateFilter } from '../../../shared/utils/date.utils'
import { Garantie } from '../../../shared/models/garantie'
import { TypeGaranties } from '../../../shared/enums/type-garanties.enum'
import { SubRoutingPath } from '../../../shared/components/stepper-parcours-declaration/enums/sub-routing-path.enum'
import { ResumeSinistreHabitationComponent } from '../../components/resume-sinistre-habitation/resume-sinistre-habitation.component'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { DeclarationHabitation } from '../../models/declaration-habitation'
import { DommagesHabitation } from '../../models/dommages-habitation'
import { SurvenanceHabitationComponent } from '../../components/survenance-habitation/survenance-habitation.component'
import {
    ARAG_BELGIUM_GARANTIES,
    EUROP_ASSISTANCE_GARANTIES,
    FOYER_ARAG_SA_GARANTIES,
} from '../../../shared/constants/garanties.constants'
import { Survenance } from '../../../shared/models/survenance'

@Component({
    selector: 'garanties-page',
    templateUrl: './garanties-page.component.html',
    styleUrls: ['./garanties-page.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ReactiveFormsModule,
        PersonDatatileComponent,
        MessageBannerComponent,
        GarantiesConcerneesComponent,
        OrganismeResponsableGarantiesComponent,
        ResumeSinistreHabitationComponent,
        StepperParcoursDeclarationComponent,
        ViewContainerHostDirective,
        AsyncPipe,
        I18nPipe,
        SurvenanceHabitationComponent,
    ],
})
export class GarantiesPageComponent
    extends DeclarationWizardStep<
        FormGroup<DeclarationHabitationFormStructure>,
        DeclarationHabitation
    >
    implements OnInit
{
    @ViewChild(ViewContainerHostDirective, { static: true })
    viewContainerHost!: ViewContainerHostDirective

    override currentWizardStep = WizardSteps.GARANTIES
    override previousStep: WizardSteps = WizardSteps.SELECTION_RISQUE

    readonly TRANSLATION_PREFIX = 'pages.garanties.'
    readonly OrganismeResponsableGaranties = OrganismeResponsableGaranties

    form?: DeclarationHabitationForm
    declaration$?: Observable<DeclarationHabitation>

    protected readonly ARAG_BELGIUM_GARANTIES = ARAG_BELGIUM_GARANTIES
    protected readonly EUROP_ASSISTANCE_GARANTIES = EUROP_ASSISTANCE_GARANTIES
    protected readonly FOYER_ARAG_SA_GARANTIES = FOYER_ARAG_SA_GARANTIES
    protected readonly RoutingPath = RoutingPath
    protected readonly maxDateFilter = maxDateFilter

    constructor(
        protected override readonly wizardService: WizardService,
        protected override readonly declarationService: DeclarationService,
        protected override readonly modalService: ModalService,
        protected override readonly changeDetectorRef: ChangeDetectorRef,
        protected override readonly router: Router,
        protected override readonly risquesService: RisquesService,
        private readonly stepperService: StepperService
    ) {
        super(
            wizardService,
            declarationService,
            modalService,
            changeDetectorRef,
            router,
            risquesService
        )
    }

    asFormGroup(
        form: DeclarationHabitationForm
    ): FormGroup<DeclarationHabitationFormStructure> {
        return form as unknown as FormGroup<DeclarationHabitationFormStructure>
    }

    ngOnInit(): void {
        this.subscriptions.add(this.init())
        this.form = this.declarationService.getDeclarationHabitationForm()
        this.declarationService.hasDeclarationOrRedirectToSelectionRisque()
        this.declaration$ = this.declarationService.getDeclarationHabitation$()
        this.initObs()
        this.initFormWithExistingData()
        this.setStepper(this.form)
        this.handleGarantiesChange(this.form)
        this.changeDetectorRef.markForCheck()
    }

    filtrerGarantiesByTypes(
        garanties: Garantie[],
        typesGaranties: TypeGaranties[]
    ): Garantie[] {
        return garanties.filter((garantie) =>
            typesGaranties.some((type) => type === garantie.code)
        )
    }

    protected isFormValidInPage(): boolean {
        return !!this.form?.controls.garanties.valid
    }

    protected savePageContent(): void {
        this.declarationService.updateGarantiesAndReferenceProducteurForHabitation(
            this.form?.controls.garanties.value,
            this.form?.controls.referenceProducteur.value
        )

        if (this.form?.controls.survenance) {
            this.declarationService.updateSurvenance(
                this.form?.controls.survenance.getRawValue() as Survenance
            )
        }
    }

    protected override setStepper(form: DeclarationHabitationForm): void {
        this.subscriptions.add(
            form.controls.garanties.valueChanges
                .pipe(
                    startWith(form.controls.garanties.value),
                    distinctUntilChanged(),
                    map(() => ({
                        routingPath: RoutingPath.GARANTIES,
                        subPaths: [
                            {
                                path: SubRoutingPath.PRENEUR,
                                valid: true,
                            },
                            {
                                path: SubRoutingPath.SURVENANCE,
                                valid: true,
                            },
                            {
                                path: SubRoutingPath.GARANTIES,
                                valid: form.controls.garanties.valid,
                            },
                        ],
                    })),
                    tap((stepper) => this.stepperService.setStepper(stepper))
                )
                .subscribe()
        )
    }

    private handleGarantiesChange(form: DeclarationHabitationForm): void {
        this.subscriptions.add(
            form.hasChangeOnGarantiesListener$().subscribe((hasChange) => {
                if (hasChange) {
                    this.form?.controls.circonstance.reset()
                    this.declarationService.updateCirconstances()

                    const updatedDommages: DommagesHabitation = {
                        degatsPieces:
                            form.controls.dommages.controls.degatsPieces.value,
                        degatsExterieurs:
                            form.controls.dommages.controls.degatsExterieurs
                                .value,
                        objetsDeDommage: [],
                        autresObjetsDeDommage: [],
                    }
                    this.declarationService.updateDommagesHabitation(
                        updatedDommages
                    )
                    this.form?.controls.dommages.resetObjetsDeDommageForm()
                }
            })
        )
    }
}
