import {
    ComponentFixture,
    fakeAsync,
    TestBed,
    tick,
} from '@angular/core/testing'

import { GarantiesPageComponent } from './garanties-page.component'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { RisquesServiceMock } from '../../../shared/services/risques/risques.service.mock'
import { By } from '@angular/platform-browser'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { provideRouter, Router } from '@angular/router'
import { garantiesMock } from '../../../shared/mocks/garanties.mock'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { StepperServiceMock } from '../../../shared/services/stepper-state/stepper.service.mock'
import { garantiesStepperMock } from '../../../shared/mocks/stepper.mock'
import { provideHttpClient } from '@angular/common/http'
import { provideDate } from '../../../core/providers/date.provider'
import { DeclarationKind } from '../../../shared/enums/declaration-kind.enum'
import { TypeGaranties } from '../../../shared/enums/type-garanties.enum'
import { WizardAction } from '../../../shared/enums/wizard-action.enum'
import { Garantie } from '../../../shared/models/garantie'
import { RoutingPath } from '../../../routing-path.enum'
import { ObjetsDeDommage } from '../../enums/objets-de-dommage.enum'
import { DeclarationServiceMock } from '../../../shared/services/declaration/declaration.service.mock'

describe('GarantiesPageComponent', () => {
    let component: GarantiesPageComponent
    let wizardService: WizardService
    let declarationService: DeclarationService
    let router: Router
    let stepperService: StepperService
    let fixture: ComponentFixture<GarantiesPageComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [GarantiesPageComponent],
            providers: [
                provideHttpClient(),
                provideDate(),
                provideRouter([]),
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: StepperService,
                    useClass: StepperServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(GarantiesPageComponent)
        component = fixture.componentInstance
        wizardService = TestBed.inject(WizardService)
        router = TestBed.inject(Router)
        declarationService = TestBed.inject(DeclarationService)
        stepperService = TestBed.inject(StepperService)

        declarationService.updateDeclaration({
            _kind: DeclarationKind.HABITATION,
            numeroRisque: '20451495-5-1',
            survenance: {
                dateDeSurvenance: new Date(),
            },
        })
        declarationService.initializeForm(DeclarationKind.HABITATION)
        fixture.detectChanges()
    })

    it('person-datatile should be present', () => {
        const personDataTileComponent = fixture.debugElement.query(
            By.css('person-datatile')
        )
        expect(personDataTileComponent).not.toBeUndefined()
    })

    describe('when we click on next button', () => {
        it('it should saves the fields into declaration service and goes to next page', fakeAsync(() => {
            component.form?.patchValue({
                referenceProducteur: undefined,
                survenance: {
                    heureDeSurvenance: '12:15',
                },
                garanties: [TypeGaranties.CNN],
            })

            spyOn(
                declarationService,
                'updateGarantiesAndReferenceProducteurForHabitation'
            )
            spyOn(declarationService, 'updateSurvenance').and.callThrough()
            spyOn(declarationService, 'updateDommagesHabitation')
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))

            wizardService.updateState(WizardAction.ON_NEXT)
            fixture.detectChanges()

            tick()

            expect(
                declarationService.updateGarantiesAndReferenceProducteurForHabitation
            ).toHaveBeenCalledOnceWith([TypeGaranties.CNN], undefined)
            expect(
                declarationService.updateSurvenance
            ).toHaveBeenCalledOnceWith(
                jasmine.objectContaining({
                    heureDeSurvenance: '12:15',
                })
            )
            expect(
                declarationService.updateDommagesHabitation
            ).not.toHaveBeenCalled()
            expect(router.navigate).toHaveBeenCalledOnceWith([
                RoutingPath.HABITATION,
                RoutingPath.CIRCONSTANCE,
            ])
        }))

        it('it should mark form as touched if form is not valid and stay on the page', () => {
            component.form?.controls.garanties.setErrors({ error: 'error' })

            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            spyOn(component.form!, 'markAllAsTouched').and.callThrough()
            spyOn(router, 'navigate')

            wizardService.updateState(WizardAction.ON_NEXT)
            fixture.detectChanges()

            expect(component.form?.markAllAsTouched).toHaveBeenCalled()
            expect(router.navigate).not.toHaveBeenCalled()
        })
    })

    describe('filtrerGarantiesByTypes', () => {
        const garanties: Garantie[] = garantiesMock()
        const typeGaranties: TypeGaranties[] = [TypeGaranties.CNN]

        it('return garanties corresponding to provided garantie types', () => {
            const result = component.filtrerGarantiesByTypes(
                garanties,
                typeGaranties
            )
            expect(result).toEqual([garantiesMock()[2]])
        })

        it('return empty list when no garanties corresponding to given garantie types', () => {
            const result = component.filtrerGarantiesByTypes(garanties, [
                TypeGaranties.VOLA,
            ])
            expect(result).toEqual([])
        })
    })

    describe('onBack', () => {
        it('should save data in current declaration', fakeAsync(() => {
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
            spyOn(
                declarationService,
                'updateGarantiesAndReferenceProducteurForHabitation'
            ).and.callThrough()

            wizardService.updateState(WizardAction.ON_PREVIOUS)
            fixture.detectChanges()

            tick(6000)

            expect(
                declarationService.updateGarantiesAndReferenceProducteurForHabitation
            ).toHaveBeenCalled()
            expect(router.navigate).toHaveBeenCalledOnceWith([
                RoutingPath.HABITATION,
                RoutingPath.SELECTION_RISQUE,
            ])
        }))
    })

    describe('When we set the stepper for garanties', () => {
        it('should call setStepper from stepperService with expected params', () => {
            spyOn(stepperService, 'setStepper')
            component.form?.controls.garanties.setValue([TypeGaranties.TGN])
            fixture.detectChanges()
            expect(stepperService.setStepper).toHaveBeenCalledOnceWith(
                garantiesStepperMock()
            )
        })
    })

    it('should reset objetsDeDommage and autresObjetsDeDommage in form when we update garanties and call updateDommagesHabitation', () => {
        spyOn(declarationService, 'updateDommagesHabitation')

        component.form?.controls.dommages.controls.objetsDeDommage.setValue([
            ObjetsDeDommage.BIJOUX,
            ObjetsDeDommage.CHAUDIERE,
        ])
        expect(
            component.form?.controls.dommages.controls.objetsDeDommage.value
                ?.length
        ).toEqual(2)

        component.form?.controls.garanties.setValue([TypeGaranties.TGN])
        expect(
            component.form?.controls.dommages.controls.objetsDeDommage.value
                ?.length
        ).toEqual(0)
        expect(
            declarationService.updateDommagesHabitation
        ).toHaveBeenCalledOnceWith({
            degatsPieces:
                component.form?.controls.dommages.controls.degatsPieces.value,
            degatsExterieurs:
                component.form?.controls.dommages.controls.degatsExterieurs
                    .value,
            objetsDeDommage: [],
            autresObjetsDeDommage: [],
        })
    })
})
