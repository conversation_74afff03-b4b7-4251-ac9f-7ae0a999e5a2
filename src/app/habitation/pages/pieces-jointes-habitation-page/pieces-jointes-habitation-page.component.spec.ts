import {
    ComponentFixture,
    fakeAsync,
    TestBed,
    tick,
} from '@angular/core/testing'
import { PiecesJointesHabitationPageComponent } from './pieces-jointes-habitation-page.component'
import { WizardAction } from '../../../shared/enums/wizard-action.enum'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { provideRouter, Router } from '@angular/router'
import { RisquesServiceMock } from '../../../shared/services/risques/risques.service.mock'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { piecesJointesMock } from '../../../shared/mocks/pieces-jointes.mock'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { validDetailsHabitationMock } from '../../../shared/mocks/circonstances.mock'
import { NotificationLevel } from '../../../shared/enums/notification-level.enum'
import { throwError } from 'rxjs'
import { TypeGaranties } from '../../../shared/enums/type-garanties.enum'
import { Circonstance } from '../../../shared/enums/circonstance.enum'
import { provideHttpClient } from '@angular/common/http'
import { piecesJointesStepperMock } from '../../../shared/mocks/stepper.mock'
import { DeclarationKind } from '../../../shared/enums/declaration-kind.enum'
import { inputFileProvider } from '../../../core/providers/input-file.provider'
import { RoutingPath } from '../../../routing-path.enum'
import { DeclarationServiceMock } from '../../../shared/services/declaration/declaration.service.mock'
import { provideHttpClientTesting } from '@angular/common/http/testing'
import { declarationHabitationMock } from '../../../shared/wrappers/declaration/declaration-wrapper.service.mock'

describe('PiecesJointesHabitationPageComponent', () => {
    let component: PiecesJointesHabitationPageComponent
    let fixture: ComponentFixture<PiecesJointesHabitationPageComponent>
    let wizardService: WizardService
    let router: Router
    let modalService: ModalService
    let declarationService: DeclarationService
    let stepperService: StepperService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [PiecesJointesHabitationPageComponent],
            providers: [
                provideHttpClient(),
                provideHttpClientTesting(),
                inputFileProvider,
                provideRouter([]),
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(PiecesJointesHabitationPageComponent)
        component = fixture.componentInstance

        declarationService = TestBed.inject(DeclarationService)
        wizardService = TestBed.inject(WizardService)
        modalService = TestBed.inject(ModalService)
        router = TestBed.inject(Router)
        stepperService = TestBed.inject(StepperService)
        declarationService.initializeForm(DeclarationKind.HABITATION)
        declarationService.updateDeclaration(declarationHabitationMock)
        fixture.detectChanges()
    })

    describe('When we save brouillon', () => {
        it('when sauver le brouillon button clicked, it calls update piecesJointes with exepcted params', () => {
            component.form?.controls.piecesJointes.setValue(piecesJointesMock())

            spyOn(declarationService, 'isUpdateInProgress').and.returnValue(
                false
            )
            spyOn(declarationService, 'updatePiecesJointes').and.callThrough()

            wizardService.updateState(WizardAction.ON_SECONDARY)
            fixture.detectChanges()

            expect(
                declarationService.updatePiecesJointes
            ).toHaveBeenCalledOnceWith(piecesJointesMock())
        })
    })

    describe('Declarer sinistre button', () => {
        describe('When form is valid', () => {
            beforeEach(() => {
                component.form?.patchValue({
                    referenceProducteur: '',
                    garanties: [TypeGaranties.TGN],
                    survenance: {
                        dateDeSurvenance: new Date(),
                    },
                    circonstance: {
                        circonstance: Circonstance.GRELE,
                        complementDeCirconstance:
                            'ceci est un complément de circonstance',
                    },
                    modalites: {
                        preneurSoumisTva: true,
                        tva6Pourcent: true,
                        pourcentageTvaRecupere: 35,
                        compteBancaire: 'BE50363051592118',
                    },
                    detailsSinistre: validDetailsHabitationMock(),
                })
                fixture.detectChanges()
            })

            it('when declarer le sinistre button clicked, it save declaration and submit declaration and then navigate to declaration-terminee page', fakeAsync(() => {
                spyOn(declarationService, 'upsertDeclaration').and.callThrough()
                spyOn(
                    declarationService,
                    'soumettreDeclaration'
                ).and.callThrough()
                spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
                spyOn(modalService, 'showNotification').and.callThrough()

                expect(component.form?.valid).toBeTruthy()

                wizardService.updateState(WizardAction.ON_NEXT)
                fixture.detectChanges()

                tick()

                expect(declarationService.upsertDeclaration).toHaveBeenCalled()
                expect(
                    declarationService.soumettreDeclaration
                ).toHaveBeenCalled()
                expect(router.navigate).toHaveBeenCalledOnceWith([
                    RoutingPath.HABITATION,
                    RoutingPath.DECLARATION_TERMINEE,
                ])
            }))

            it('should show notification with declaration sinistre error message when an error occurs during submission', () => {
                spyOn(
                    declarationService,
                    'soumettreDeclaration'
                ).and.returnValue(
                    throwError(() => ({
                        error: new Error('Erreur'),
                    }))
                )
                spyOn(modalService, 'showNotification').and.callThrough()

                expect(component.form?.valid).toBeTruthy()

                wizardService.updateState(WizardAction.ON_NEXT)
                fixture.detectChanges()

                expect(modalService.showNotification).toHaveBeenCalledOnceWith(
                    {
                        notificationLevel: NotificationLevel.DANGER,
                        message:
                            'common.notification.declaration-sinistre-error',
                    },
                    component.viewContainerHost.viewContainerRef
                )
            })
        })

        describe('When form is invalid', () => {
            it('should show notification with pieces-jointes error message when form is invalid', () => {
                component.form?.controls.piecesJointes.setErrors({
                    hasFileInError: true,
                })
                spyOn(modalService, 'showNotification').and.callThrough()

                expect(component.form?.invalid).toBeTruthy()

                wizardService.updateState(WizardAction.ON_NEXT)
                fixture.detectChanges()

                expect(modalService.showNotification).toHaveBeenCalledWith(
                    {
                        notificationLevel: NotificationLevel.DANGER,
                        message:
                            'common.notification.declaration-sinistre-error',
                    },
                    component.viewContainerHost.viewContainerRef
                )
            })
        })
    })

    describe('When we set the stepper for pieces jointes', () => {
        it('should call setStepper from stepperService with expected params', () => {
            spyOn(stepperService, 'setStepper')
            component.form?.controls.piecesJointes.setValue(piecesJointesMock())
            fixture.detectChanges()
            expect(stepperService.setStepper).toHaveBeenCalledOnceWith(
                piecesJointesStepperMock()
            )
        })
    })
})
