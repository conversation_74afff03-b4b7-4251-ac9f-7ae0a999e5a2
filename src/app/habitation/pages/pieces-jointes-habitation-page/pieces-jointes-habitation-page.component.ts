import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
} from '@angular/core'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { Router } from '@angular/router'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { ViewContainerHostDirective } from '../../../shared/directives/view-container-host.directive'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { StepperParcoursDeclarationComponent } from '../../../shared/components/stepper-parcours-declaration/stepper-parcours-declaration.component'
import {
    FormControl,
    FormGroup,
    FormsModule,
    ReactiveFormsModule,
} from '@angular/forms'

import { I18nPipe } from '@foyer/ng-i18n'
import { DeclarationHabitationFormStructure } from '../../models/forms/declaration-habitation-form-structure'
import { DeclarationHabitation } from '../../models/declaration-habitation'
import { ResumeSinistreHabitationComponent } from '../../components/resume-sinistre-habitation/resume-sinistre-habitation.component'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { FileTransferComponent } from '../../../shared/components/file-transfer/file-transfer.component'
import { PiecesJointesPageComponent } from '../../../shared/pages/pieces-jointes-page.component'
import { DeclarationHabitationForm } from '../../models/forms/declaration-habitation.form'

@Component({
    selector: 'pieces-jointes-habitation-page',
    templateUrl: './pieces-jointes-habitation-page.component.html',
    styleUrls: ['./pieces-jointes-habitation-page.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ReactiveFormsModule,
        FormsModule,
        ResumeSinistreHabitationComponent,
        StepperParcoursDeclarationComponent,
        ViewContainerHostDirective,
        I18nPipe,
        FileTransferComponent,
    ],
})
export class PiecesJointesHabitationPageComponent extends PiecesJointesPageComponent<
    FormGroup<DeclarationHabitationFormStructure>,
    DeclarationHabitation
> {
    constructor(
        protected override readonly wizardService: WizardService,
        protected override readonly declarationService: DeclarationService,
        protected override readonly modalService: ModalService,
        protected override readonly changeDetectorRef: ChangeDetectorRef,
        protected override readonly router: Router,
        protected override readonly risquesService: RisquesService,
        protected override readonly stepperService: StepperService
    ) {
        super(
            wizardService,
            declarationService,
            modalService,
            changeDetectorRef,
            router,
            risquesService,
            stepperService
        )
    }

    asFormGroup(
        form: DeclarationHabitationForm
    ): FormGroup<DeclarationHabitationFormStructure> {
        return form as unknown as FormGroup<DeclarationHabitationFormStructure>
    }

    protected override initializeForm(): void {
        this.form = this.declarationService.getDeclarationHabitationForm()
    }

    protected override initializeDeclaration(): void {
        this.declaration$ = this.declarationService.getDeclarationHabitation$()
    }

    protected override getPiecesJointesControl(
        form: FormGroup<DeclarationHabitationFormStructure>
    ): FormControl<string[]> {
        return form.controls.piecesJointes
    }
}
