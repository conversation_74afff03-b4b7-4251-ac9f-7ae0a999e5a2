import {
    ChangeDetectionStrategy,
    ChangeDetector<PERSON>ef,
    Component,
    inject,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core'
import { ViewContainerHostDirective } from '../../../shared/directives/view-container-host.directive'
import { WizardSteps } from '../../../shared/enums/wizard-steps.enum'
import { DeclarationHabitationForm } from '../../models/forms/declaration-habitation.form'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { Router } from '@angular/router'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { distinctUntilChanged, map, Observable, startWith, tap } from 'rxjs'

import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { StepperParcoursDeclarationComponent } from '../../../shared/components/stepper-parcours-declaration/stepper-parcours-declaration.component'
import { DeclarationHabitationFormStructure } from '../../models/forms/declaration-habitation-form-structure'
import { DeclarationWizardStep } from '../../../shared/components/declaration-wizard-step'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { RoutingPath } from '../../../routing-path.enum'
import { SubRoutingPath } from '../../../shared/components/stepper-parcours-declaration/enums/sub-routing-path.enum'
import { DeclarationHabitation } from '../../models/declaration-habitation'
import { ResumeSinistreHabitationComponent } from '../../components/resume-sinistre-habitation/resume-sinistre-habitation.component'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { ModalitesComponent } from '../../../shared/components/modalites/modalites.component'

@Component({
    selector: 'modalites-habitation-page',
    standalone: true,
    imports: [
        ReactiveFormsModule,
        FormsModule,
        ResumeSinistreHabitationComponent,
        StepperParcoursDeclarationComponent,
        ViewContainerHostDirective,
        ModalitesComponent,
    ],
    templateUrl: './modalites-habitation-page.component.html',
    styleUrl: './modalites-habitation-page.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ModalitesHabitationPageComponent
    extends DeclarationWizardStep<
        FormGroup<DeclarationHabitationFormStructure>,
        DeclarationHabitation
    >
    implements OnInit, OnDestroy
{
    private readonly stepperService: StepperService = inject(StepperService)

    @ViewChild(ViewContainerHostDirective, { static: true })
    override viewContainerHost!: ViewContainerHostDirective

    override currentWizardStep = WizardSteps.MODALITES
    override previousStep = WizardSteps.DOMMAGES

    readonly TRANSLATION_PREFIX: string = 'pages.modalites.'

    form?: DeclarationHabitationForm
    declaration$?: Observable<DeclarationHabitation>

    protected readonly RoutingPath = RoutingPath

    constructor(
        protected override readonly wizardService: WizardService,
        protected override readonly declarationService: DeclarationService,
        protected override readonly modalService: ModalService,
        protected override readonly changeDetectorRef: ChangeDetectorRef,
        protected override readonly router: Router,
        protected override readonly risquesService: RisquesService
    ) {
        super(
            wizardService,
            declarationService,
            modalService,
            changeDetectorRef,
            router,
            risquesService
        )
    }

    asFormGroup(
        form: DeclarationHabitationForm
    ): FormGroup<DeclarationHabitationFormStructure> {
        return form as unknown as FormGroup<DeclarationHabitationFormStructure>
    }

    ngOnInit(): void {
        this.subscriptions.add(this.init())
        this.form = this.declarationService.getDeclarationHabitationForm()
        this.declarationService.hasDeclarationOrRedirectToSelectionRisque()
        this.declaration$ = this.declarationService.getDeclarationHabitation$()
        this.initObs()
        this.initFormWithExistingData()
        this.setStepper(this.form)
        this.changeDetectorRef.markForCheck()
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe()
    }

    protected isFormValidInPage(): boolean {
        return !!this.form?.controls.modalites.valid
    }

    protected savePageContent(): void {
        if (this.form?.controls.modalites.valid) {
            this.declarationService.updateModalites(
                this.form.controls.modalites.getValueHabitation()
            )
        }
    }

    protected override setStepper(form: DeclarationHabitationForm): void {
        const modalitesForm = form.controls.modalites

        this.subscriptions.add(
            modalitesForm.valueChanges
                .pipe(
                    startWith(modalitesForm.value),
                    distinctUntilChanged(),
                    map(() => ({
                        routingPath: RoutingPath.MODALITES,
                        subPaths: [
                            {
                                path: SubRoutingPath.TVA,
                                valid: modalitesForm.valid,
                            },
                            {
                                path: SubRoutingPath.COMPTE_BANCAIRE,
                                valid: modalitesForm.controls.compteBancaire
                                    .valid,
                            },
                        ],
                    })),
                    tap((stepper) => this.stepperService.setStepper(stepper))
                )
                .subscribe()
        )
    }
}
