import {
    ComponentFixture,
    fakeAsync,
    TestBed,
    tick,
} from '@angular/core/testing'
import { ModalitesHabitationPageComponent } from './modalites-habitation-page.component'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { RisquesServiceMock } from '../../../shared/services/risques/risques.service.mock'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { provideRouter, Router } from '@angular/router'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { StepperServiceMock } from '../../../shared/services/stepper-state/stepper.service.mock'
import { provideHttpClient } from '@angular/common/http'
import { DeclarationKind } from '../../../shared/enums/declaration-kind.enum'
import { WizardAction } from '../../../shared/enums/wizard-action.enum'
import { RoutingPath } from '../../../routing-path.enum'
import { SubRoutingPath } from '../../../shared/components/stepper-parcours-declaration/enums/sub-routing-path.enum'
import { ModalitesHabitation } from '../../models/modalites-habitation'
import { DeclarationServiceMock } from '../../../shared/services/declaration/declaration.service.mock'

describe('ModalitesHabitationPageComponent', () => {
    let component: ModalitesHabitationPageComponent
    let wizardService: WizardService
    let declarationService: DeclarationService
    let router: Router
    let stepperService: StepperService
    let fixture: ComponentFixture<ModalitesHabitationPageComponent>
    const modalitesMock: ModalitesHabitation = {
        preneurSoumisTva: false,
        tva6Pourcent: true,
        compteBancaire: 'BE50363051592118',
    }

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ModalitesHabitationPageComponent],
            providers: [
                provideHttpClient(),
                provideRouter([]),
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: StepperService,
                    useClass: StepperServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(ModalitesHabitationPageComponent)
        component = fixture.componentInstance
        wizardService = TestBed.inject(WizardService)
        router = TestBed.inject(Router)
        declarationService = TestBed.inject(DeclarationService)
        stepperService = TestBed.inject(StepperService)

        declarationService.updateDeclaration({
            _kind: DeclarationKind.HABITATION,
            numeroRisque: '20451495-5-1',
            survenance: {
                dateDeSurvenance: new Date(),
            },
        })
        declarationService.initializeForm(DeclarationKind.HABITATION)
        fixture.detectChanges()
    })

    describe('when clicking next button', () => {
        it('should save modalites and navigate to next step if form is valid', fakeAsync(() => {
            component.form?.controls.modalites.patchValue(modalitesMock)

            spyOn(declarationService, 'updateModalites')
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))

            wizardService.updateState(WizardAction.ON_NEXT)
            fixture.detectChanges()

            tick()

            expect(declarationService.updateModalites).toHaveBeenCalledWith(
                modalitesMock
            )
            expect(router.navigate).toHaveBeenCalledWith([
                RoutingPath.HABITATION,
                RoutingPath.PIECES_JOINTES,
            ])
        }))

        it('should mark form as touched and not navigate if form is invalid', () => {
            component.form?.controls.modalites.patchValue({})

            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            spyOn(component.form!, 'markAllAsTouched')
            spyOn(router, 'navigate')

            wizardService.updateState(WizardAction.ON_NEXT)
            fixture.detectChanges()

            expect(component.form?.markAllAsTouched).toHaveBeenCalled()
            expect(router.navigate).not.toHaveBeenCalled()
        })
    })

    describe('stepper updates', () => {
        it('should update stepper state when modalites form changes', () => {
            spyOn(stepperService, 'setStepper')
            const expectedStepperState = {
                routingPath: RoutingPath.MODALITES,
                subPaths: [
                    {
                        path: SubRoutingPath.TVA,
                        valid: false,
                    },
                    {
                        path: SubRoutingPath.COMPTE_BANCAIRE,
                        valid: true,
                    },
                ],
            }

            component.form?.controls.modalites.updateValueAndValidity()

            expect(stepperService.setStepper).toHaveBeenCalledWith(
                expectedStepperState
            )
        })
    })

    describe('when clicking previous button', () => {
        it('should save current data and navigate to previous step', fakeAsync(() => {
            component.form?.controls.modalites.patchValue(modalitesMock)

            spyOn(declarationService, 'updateModalites')
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))

            wizardService.updateState(WizardAction.ON_PREVIOUS)
            fixture.detectChanges()

            tick(6000)

            expect(declarationService.updateModalites).toHaveBeenCalled()
            expect(router.navigate).toHaveBeenCalledWith([
                RoutingPath.HABITATION,
                RoutingPath.DOMMAGES,
            ])
        }))
    })

    describe('getValueHabitation', () => {
        it('should return the correct structure', () => {
            component.form?.controls.modalites.setPreneurSoumisTva(true)
            component.form?.controls.modalites.setTva6Pourcent(true)
            component.form?.controls.modalites.controls.compteBancaire.setValue(
                'BE50363051592118'
            )

            const valueHabitation =
                component.form?.controls.modalites.getValueHabitation()
            expect(Object.keys(valueHabitation || {}).length).toBe(3)
        })
    })
})
