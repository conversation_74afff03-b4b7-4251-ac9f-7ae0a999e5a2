<div class="LAYOUT-SECTION">
    <div class="L-CONTENT">
        @if (form) {
            <form class="Form u-is-txt-monospace" [formGroup]="asFormGroup(form)">
                <div class="GridFlex">
                    <div class="GridFlex-row">
                        <div class="GridFlex-col-12 GridFlex-col-lg-6">
                            @if (form.controls.modalites; as modalitesForm) {
                                <modalites [form]="modalitesForm" [hasPourcentageTvaRecupere]="false"></modalites>
                            }
                        </div>
                        <div class="GridFlex-col-12 GridFlex-col-lg-4">
                            <resume-sinistre-habitation></resume-sinistre-habitation>
                            <stepper-parcours-declaration [currentPath]="RoutingPath.MODALITES"></stepper-parcours-declaration>
                        </div>
                    </div>
                </div>
            </form>
        }
    </div>
</div>
<ng-template viewContainerHost></ng-template>
