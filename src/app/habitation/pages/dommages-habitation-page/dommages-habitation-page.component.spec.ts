import {
    ComponentFixture,
    fakeAsync,
    TestBed,
    tick,
} from '@angular/core/testing'
import { DommagesHabitationPageComponent } from './dommages-habitation-page.component'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { RisquesServiceMock } from '../../../shared/services/risques/risques.service.mock'
import { RoutingPath } from '../../../routing-path.enum'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { Router } from '@angular/router'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { dommagesStepperMock } from '../../../shared/mocks/stepper.mock'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import {
    circonstancesMock,
    validDetailsHabitationMock,
} from '../../../shared/mocks/circonstances.mock'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { DeclarationServiceMock } from '../../../shared/services/declaration/declaration.service.mock'
import { ObjetsDeDommage } from '../../enums/objets-de-dommage.enum'
import { ZonesMaisonInterieur } from '../../enums/zones-maison-interieur.enum'
import { ZonesMaisonExterieur } from '../../enums/zones-maison-exterieur.enum'
import { WizardAction } from '../../../shared/enums/wizard-action.enum'

describe('DommagesPageComponent', () => {
    let component: DommagesHabitationPageComponent
    let fixture: ComponentFixture<DommagesHabitationPageComponent>
    let router: Router
    let wizardService: WizardService
    let declarationService: DeclarationService
    let stepperService: StepperService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DommagesHabitationPageComponent],
            providers: [
                provideNoopAnimations(),
                provideTranslation(),
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
            ],
        }).compileComponents()
        declarationService = TestBed.inject(DeclarationService)
        router = TestBed.inject(Router)
        wizardService = TestBed.inject(WizardService)
        stepperService = TestBed.inject(StepperService)
        fixture = TestBed.createComponent(DommagesHabitationPageComponent)
        component = fixture.componentInstance
        fixture.detectChanges()
    })

    describe('next button', () => {
        beforeEach(() => {
            component.form?.controls.detailsSinistre.setValue(
                validDetailsHabitationMock()
            )
            component.form?.controls.circonstance.setValue({
                circonstance: circonstancesMock()[0].key,
                complementDeCirconstance:
                    'ceci est un complément de circonstance',
            })
        })

        it('when pieces jointes button clicked, it saves the fields into declaration service and navigate to next page', fakeAsync(() => {
            component.form?.controls.dommages.setValue({
                degatsPieces: [ZonesMaisonInterieur.SALON],
                degatsExterieurs: [ZonesMaisonExterieur.FENETRE],
                objetsDeDommage: [ObjetsDeDommage.CHAUDIERE],
                autresObjetsDeDommage: [],
            })
            spyOn(
                declarationService,
                'updateDommagesHabitation'
            ).and.callThrough()
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))

            wizardService.updateState(WizardAction.ON_NEXT)
            fixture.detectChanges()

            tick()

            expect(
                declarationService.updateDommagesHabitation
            ).toHaveBeenCalledOnceWith({
                degatsPieces: [ZonesMaisonInterieur.SALON],
                degatsExterieurs: [ZonesMaisonExterieur.FENETRE],
                objetsDeDommage: [ObjetsDeDommage.CHAUDIERE],
                autresObjetsDeDommage: [],
            })
            expect(router.navigate).toHaveBeenCalledOnceWith([
                RoutingPath.HABITATION,
                RoutingPath.MODALITES,
            ])
        }))
    })

    describe('When we set the stepper for dommages', () => {
        it('should call setStepper from stepperService with expected params', () => {
            spyOn(stepperService, 'setStepper')
            component.form?.controls.dommages.patchValue({
                degatsPieces: [ZonesMaisonInterieur.SALON],
                autresObjetsDeDommage: ['puit'],
                objetsDeDommage: [ObjetsDeDommage.BIJOUX],
            })
            fixture.detectChanges()
            expect(stepperService.setStepper).toHaveBeenCalledOnceWith(
                dommagesStepperMock()
            )
        })
    })
})
