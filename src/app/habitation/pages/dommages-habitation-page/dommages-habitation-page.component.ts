import {
    ChangeDetectionStrategy,
    ChangeDetector<PERSON><PERSON>,
    Component,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core'
import { DeclarationHabitationForm } from '../../models/forms/declaration-habitation.form'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { distinctUntilChanged, map, Observable, startWith, tap } from 'rxjs'
import { RoutingPath } from '../../../routing-path.enum'
import { Router } from '@angular/router'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { ViewContainerHostDirective } from '../../../shared/directives/view-container-host.directive'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { StepperParcoursDeclarationComponent } from '../../../shared/components/stepper-parcours-declaration/stepper-parcours-declaration.component'
import { ObjetsDeDommageComponent } from '../../components/objets-de-dommage/objets-de-dommage.component'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'

import { DegatsPiecesComponent } from '../../components/dommages-habitation/degats-pieces/degats-pieces.component'
import { DegatsExterieursComponent } from '../../components/dommages-habitation/degats-exterieurs/degats-exterieurs.component'
import { DeclarationWizardStep } from '../../../shared/components/declaration-wizard-step'
import { WizardSteps } from '../../../shared/enums/wizard-steps.enum'
import { SubRoutingPath } from '../../../shared/components/stepper-parcours-declaration/enums/sub-routing-path.enum'
import { DeclarationHabitationFormStructure } from '../../models/forms/declaration-habitation-form-structure'
import { ResumeSinistreHabitationComponent } from '../../components/resume-sinistre-habitation/resume-sinistre-habitation.component'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { DeclarationHabitation } from '../../models/declaration-habitation'

@Component({
    selector: 'dommages-habitation-page',
    templateUrl: './dommages-habitation-page.component.html',
    styleUrls: ['./dommages-habitation-page.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ReactiveFormsModule,
        FormsModule,
        DegatsPiecesComponent,
        DegatsExterieursComponent,
        ObjetsDeDommageComponent,
        ResumeSinistreHabitationComponent,
        StepperParcoursDeclarationComponent,
        ViewContainerHostDirective,
    ],
})
export class DommagesHabitationPageComponent
    extends DeclarationWizardStep<
        FormGroup<DeclarationHabitationFormStructure>,
        DeclarationHabitation
    >
    implements OnInit, OnDestroy
{
    @ViewChild(ViewContainerHostDirective, { static: true })
    viewContainerHost!: ViewContainerHostDirective

    override currentWizardStep = WizardSteps.DOMMAGES
    override previousStep: WizardSteps = WizardSteps.CIRCONSTANCE

    form?: DeclarationHabitationForm
    declaration$?: Observable<DeclarationHabitation>

    protected readonly RoutingPath = RoutingPath

    constructor(
        protected override readonly wizardService: WizardService,
        protected override readonly declarationService: DeclarationService,
        protected override readonly modalService: ModalService,
        protected override readonly changeDetectorRef: ChangeDetectorRef,
        protected override readonly router: Router,
        protected override readonly risquesService: RisquesService,
        private readonly stepperService: StepperService
    ) {
        super(
            wizardService,
            declarationService,
            modalService,
            changeDetectorRef,
            router,
            risquesService
        )
    }

    ngOnInit(): void {
        this.subscriptions.add(this.init())
        this.form = this.declarationService.getDeclarationHabitationForm()
        this.declarationService.hasDeclarationOrRedirectToSelectionRisque()
        this.declaration$ = this.declarationService.getDeclarationHabitation$()
        this.initObs()
        this.initFormWithExistingData()
        this.setStepper(this.form)
        this.changeDetectorRef.markForCheck()
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe()
    }

    protected override isFormValidInPage(): boolean {
        return !!this.form?.controls.dommages.valid
    }

    protected override savePageContent(): void {
        if (this.form?.controls.dommages) {
            this.declarationService.updateDommagesHabitation(
                this.form.controls.dommages.getValue()
            )
        }
    }

    protected override setStepper(form: DeclarationHabitationForm): void {
        const dommagesForm = form.controls.dommages

        this.subscriptions.add(
            dommagesForm.valueChanges
                .pipe(
                    startWith(dommagesForm.value),
                    distinctUntilChanged(),
                    map(() => ({
                        routingPath: RoutingPath.DOMMAGES,
                        subPaths: [
                            {
                                path: SubRoutingPath.ELEMENTS_EXTERIEURS,
                                valid: dommagesForm.controls.degatsExterieurs
                                    .valid,
                            },
                            {
                                path: SubRoutingPath.PIECES,
                                valid: dommagesForm.controls.degatsPieces.valid,
                            },
                            {
                                path: SubRoutingPath.OBJETS_DOMMAGE,
                                valid: dommagesForm.controls.objetsDeDommage
                                    .valid,
                            },
                            {
                                path: SubRoutingPath.AUTRES_OBJETS_DOMMAGE,
                                valid: dommagesForm.controls
                                    .autresObjetsDeDommage.valid,
                            },
                        ],
                    })),
                    tap((stepper) => this.stepperService.setStepper(stepper))
                )
                .subscribe()
        )
    }
}
