import {
    ChangeDetectionStrategy,
    ChangeDetector<PERSON><PERSON>,
    <PERSON><PERSON>nent,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core'
import { WizardSteps } from '../../../shared/enums/wizard-steps.enum'
import {
    combineLatest,
    distinctUntilChanged,
    map,
    Observable,
    startWith,
    take,
    tap,
} from 'rxjs'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { Router } from '@angular/router'
import { RoutingPath } from '../../../routing-path.enum'
import { ViewContainerHostDirective } from '../../../shared/directives/view-container-host.directive'
import { NotificationLevel } from '../../../shared/enums/notification-level.enum'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { ParametresService } from '../../../shared/services/parametres/parametres.service'
import { CirconstanceParametre } from '../../../shared/models/circonstance-parametre'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { DeclarationWizardStep } from '../../../shared/components/declaration-wizard-step'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { StepperParcoursDeclarationComponent } from '../../../shared/components/stepper-parcours-declaration/stepper-parcours-declaration.component'
import { CirconstancesComponent } from '../../../shared/components/circonstances/circonstances.component'
import { PersonDatatileComponent } from '../../../shared/components/person-datatile/person-datatile.component'
import { FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms'
import { AsyncPipe } from '@angular/common'
import { DetailsSinistreHabitationComponent } from '../../components/details-sinistre-habitation/details-sinistre-habitation.component'
import { DeclarationHabitationForm } from '../../models/forms/declaration-habitation.form'
import { SubRoutingPath } from '../../../shared/components/stepper-parcours-declaration/enums/sub-routing-path.enum'
import { DeclarationHabitation } from '../../models/declaration-habitation'
import { DeclarationHabitationFormStructure } from '../../models/forms/declaration-habitation-form-structure'
import { ResumeSinistreHabitationComponent } from '../../components/resume-sinistre-habitation/resume-sinistre-habitation.component'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { TypeCirconstance } from '../../../shared/enums/type-circonstance.enum'

@Component({
    selector: 'circonstance-habitation-page',
    templateUrl: './circonstance-habitation-page.component.html',
    styleUrls: ['./circonstance-habitation-page.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ReactiveFormsModule,
        FormsModule,
        PersonDatatileComponent,
        DetailsSinistreHabitationComponent,
        CirconstancesComponent,
        ResumeSinistreHabitationComponent,
        StepperParcoursDeclarationComponent,
        ViewContainerHostDirective,
        AsyncPipe,
    ],
})
export class CirconstanceHabitationPageComponent
    extends DeclarationWizardStep<
        FormGroup<DeclarationHabitationFormStructure>,
        DeclarationHabitation
    >
    implements OnInit, OnDestroy
{
    @ViewChild(ViewContainerHostDirective, { static: true })
    override viewContainerHost!: ViewContainerHostDirective

    override currentWizardStep = WizardSteps.CIRCONSTANCE
    override previousStep: WizardSteps = WizardSteps.GARANTIES

    readonly TRANSLATION_PREFIX: string = 'pages.circonstance-habitation.'

    form?: DeclarationHabitationForm
    declaration$?: Observable<DeclarationHabitation>

    protected readonly RoutingPath = RoutingPath
    protected readonly TypeCirconstance = TypeCirconstance

    constructor(
        protected override readonly wizardService: WizardService,
        protected override readonly declarationService: DeclarationService,
        protected override readonly modalService: ModalService,
        protected override readonly changeDetectorRef: ChangeDetectorRef,
        protected override readonly router: Router,
        protected override readonly risquesService: RisquesService,
        private readonly parametresService: ParametresService,
        private readonly stepperService: StepperService
    ) {
        super(
            wizardService,
            declarationService,
            modalService,
            changeDetectorRef,
            router,
            risquesService
        )
    }

    ngOnInit(): void {
        this.subscriptions.add(this.init())
        this.form = this.declarationService.getDeclarationHabitationForm()
        this.declarationService.hasDeclarationOrRedirectToSelectionRisque()
        this.declaration$ = this.declarationService.getDeclarationHabitation$()
        this.initObs()
        this.initFormWithExistingData()
        this.subscriptions.add(
            this.form?.controls.detailsSinistre
                .validationChangesListeners$()
                .subscribe()
        )
        this.setStepper(this.form)
        this.changeDetectorRef.markForCheck()
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe()
    }

    protected isFormValidInPage(): boolean {
        return (
            !!this.form?.controls.detailsSinistre.valid &&
            !!this.form?.controls.circonstance.valid
        )
    }

    protected savePageContent(): void {
        if (this.form?.controls.circonstance.valid) {
            this.updateCirconstances()
        }

        if (this.form?.controls.detailsSinistre.valid) {
            this.declarationService.updateDetailsSinistreHabitation(
                this.form.controls.detailsSinistre.getValue()
            )
        }
    }

    protected override setStepper(form: DeclarationHabitationForm): void {
        const circonstanceForm = form.controls.circonstance
        const detailsSinistreForm = form.controls.detailsSinistre

        const circonstanceValueChanges$ = circonstanceForm.valueChanges.pipe(
            startWith(circonstanceForm.value)
        )
        const detailsSinistreValueChanges$ =
            detailsSinistreForm.valueChanges.pipe(
                startWith(detailsSinistreForm.value)
            )

        this.subscriptions.add(
            combineLatest([
                circonstanceValueChanges$,
                detailsSinistreValueChanges$,
            ])
                .pipe(
                    distinctUntilChanged(),
                    map(() => ({
                        routingPath: RoutingPath.CIRCONSTANCE,
                        subPaths: [
                            {
                                path: SubRoutingPath.DETAIL_SINISTRE,
                                valid: detailsSinistreForm.valid,
                            },
                            {
                                path: SubRoutingPath.CIRCONSTANCE_SINISTRE,
                                valid: circonstanceForm.valid,
                            },
                        ],
                    })),
                    tap((stepper) => this.stepperService.setStepper(stepper))
                )
                .subscribe()
        )
    }

    protected override initFormWithExistingData(): void {
        if (this.situationContratRisque$ && this.declaration$) {
            this.subscriptions.add(
                this.declaration$.pipe(take(1)).subscribe((declaration) => {
                    declaration.detailsSinistre?.tiers?.forEach(() =>
                        this.form?.controls.detailsSinistre.controls.tiers.createArrayControl()
                    )
                    this.form?.patchValue(declaration)
                })
            )
        }
    }

    private updateCirconstances(): void {
        this.findCirconstanceParametreByCode$().subscribe(
            (circonstanceParametre) => {
                const complementDeCirconstance =
                    this.form?.controls.circonstance.controls
                        .complementDeCirconstance.value

                if (
                    !circonstanceParametre &&
                    this.form?.controls.circonstance.controls.circonstance.value
                ) {
                    this.showNotification({
                        notificationLevel: NotificationLevel.DANGER,
                        message: `common.notification.circonstance-is-missing-in-parametrage`,
                    })
                    this.changeDetectorRef.markForCheck()
                } else {
                    const circonstanceSinitre = {
                        circonstance: circonstanceParametre?.key,
                        complementDeCirconstance,
                    }
                    this.declarationService.updateCirconstances(
                        circonstanceSinitre
                    )
                }
            }
        )
    }

    private findCirconstanceParametreByCode$(): Observable<
        CirconstanceParametre | undefined
    > {
        return this.parametresService.getCirconstances$().pipe(
            take(1),
            map((circonstances) =>
                circonstances.find(
                    (circonstance) =>
                        circonstance.key ===
                        this.form?.controls.circonstance.controls.circonstance
                            .value
                )
            )
        )
    }
}
