<div class="LAYOUT-SECTION">
    <div class="L-CONTENT">
        @if (form) {
            <form class="Form u-is-txt-monospace">
                <div class="GridFlex">
                    <div class="GridFlex-row">
                        <div class="GridFlex-col-12 GridFlex-col-lg-6">
                            <div class="GridFlex-row Form-row">
                                <div class="GridFlex-col-12">
                                    @if (personne$ | async; as personne) {
                                        <div class="Panel person-panel">
                                            <person-datatile [personne]="personne"></person-datatile>
                                        </div>
                                    }
                                </div>
                            </div>
                            <div class="GridFlex-row Form-row">
                                <div class="GridFlex-col-12">
                                    <circonstances
                                        [form]="form.controls.circonstance"
                                        [typeGaranties]="form.controls.garanties.value"
                                        [typeCirconstance]="TypeCirconstance.DAB"></circonstances>
                                    <details-sinistre-habitation [form]="form.controls.detailsSinistre"></details-sinistre-habitation>
                                </div>
                            </div>
                        </div>
                        <div class="GridFlex-col-12 GridFlex-col-lg-4">
                            <resume-sinistre-habitation></resume-sinistre-habitation>
                            <stepper-parcours-declaration [currentPath]="RoutingPath.CIRCONSTANCE"></stepper-parcours-declaration>
                        </div>
                    </div>
                </div>
            </form>
        }
    </div>
</div>
<ng-template viewContainerHost></ng-template>
