import {
    ComponentFixture,
    fakeAsync,
    TestBed,
    tick,
} from '@angular/core/testing'
import { CirconstanceHabitationPageComponent } from './circonstance-habitation-page.component'
import { ParametresService } from '../../../shared/services/parametres/parametres.service'
import { ParametresServiceMock } from '../../../shared/services/parametres/parametres.service.spec.mock'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { provideRouter, Router } from '@angular/router'
import { WizardService } from '../../../shared/services/wizard/wizard.service'
import { ModalService } from '../../../shared/services/modal/modal.service'
import { of, throwError } from 'rxjs'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { RisquesServiceMock } from '../../../shared/services/risques/risques.service.mock'
import { By } from '@angular/platform-browser'
import { RoutingPath } from '../../../routing-path.enum'
import {
    circonstancesMock,
    validDetailsHabitationMock,
} from '../../../shared/mocks/circonstances.mock'
import { circonstanceHabitationStepperMock } from '../../../shared/mocks/stepper.mock'
import { StepperService } from '../../../shared/services/stepper-state/stepper.service'
import { provideTranslation } from '../../../core/providers/translation.provider'
import { DeclarationKind } from '../../../shared/enums/declaration-kind.enum'
import { Circonstance } from '../../../shared/enums/circonstance.enum'
import { WizardAction } from '../../../shared/enums/wizard-action.enum'
import { NotificationLevel } from '../../../shared/enums/notification-level.enum'
import { DeclarationServiceMock } from '../../../shared/services/declaration/declaration.service.mock'

describe('CirconstanceHabitationPageComponent', () => {
    let component: CirconstanceHabitationPageComponent
    let fixture: ComponentFixture<CirconstanceHabitationPageComponent>
    let wizardService: WizardService
    let router: Router
    let modalService: ModalService
    let declarationService: DeclarationService
    let stepperService: StepperService
    const circonstanceKey = circonstancesMock()[0].key
    const complementDeCirconstance = 'ceci est un complément de circonstance'

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [CirconstanceHabitationPageComponent],
            providers: [
                provideRouter([]),
                provideTranslation(),
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: ParametresService,
                    useClass: ParametresServiceMock,
                },
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(CirconstanceHabitationPageComponent)
        component = fixture.componentInstance
        declarationService = TestBed.inject(DeclarationService)
        wizardService = TestBed.inject(WizardService)
        modalService = TestBed.inject(ModalService)
        router = TestBed.inject(Router)
        stepperService = TestBed.inject(StepperService)
        declarationService.updateDeclaration({
            _kind: DeclarationKind.HABITATION,
            survenance: {
                dateDeSurvenance: new Date(),
            },
            numeroRisque: '20451495-5-1',
        })
        declarationService.initializeForm(DeclarationKind.HABITATION)
    })

    describe('initial page load', () => {
        it('should load existing values for référence producteur and compte bancaire into form', () => {
            spyOn(
                declarationService,
                'getDeclarationHabitation$'
            ).and.returnValue(
                of({
                    _kind: DeclarationKind.HABITATION,
                    survenance: {
                        dateDeSurvenance: new Date(),
                    },
                    numeroRisque: '20451495-5-1',
                    modalites: {
                        preneurSoumisTva: false,
                        tva6Pourcent: false,
                        compteBancaire: 'BE50363051592118',
                    },
                    referenceProducteur: 'coconut',
                })
            )
            fixture.detectChanges()

            expect(component.form?.controls.referenceProducteur.value).toEqual(
                'coconut'
            )
            expect(
                component.form?.controls.modalites.controls.compteBancaire.value
            ).toEqual('BE50363051592118')
        })
    })

    describe('save brouillon button', () => {
        beforeEach(() => {
            fixture.detectChanges()
            component.form?.controls.detailsSinistre.setValue(
                validDetailsHabitationMock()
            )
        })

        it('when save brouillon button clicked, it saves the fields into declaration service', () => {
            component.form?.controls.circonstance.setValue({
                circonstance: circonstanceKey,
                complementDeCirconstance,
            })
            spyOn(declarationService, 'updateCirconstances').and.callThrough()
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
            spyOn(modalService, 'showNotification').and.callThrough()

            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
            spyOn(component.form!, 'reset').and.callThrough()
            wizardService.updateState(WizardAction.ON_SECONDARY)
            fixture.detectChanges()

            expect(
                declarationService.updateCirconstances
            ).toHaveBeenCalledOnceWith({
                circonstance: circonstanceKey,
                complementDeCirconstance,
            })

            expect(modalService.showNotification).toHaveBeenCalledWith(
                {
                    notificationLevel: NotificationLevel.SUCCESS,
                    message: 'common.notification.save-declaration-success',
                },
                component.viewContainerHost.viewContainerRef
            )
        })

        it('show error when fail to save declaration', () => {
            component.form?.controls.circonstance.setValue({
                circonstance: circonstanceKey,
                complementDeCirconstance,
            })
            spyOn(declarationService, 'updateCirconstances').and.callThrough()
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
            spyOn(modalService, 'showNotification').and.callThrough()

            spyOn(declarationService, 'upsertDeclaration').and.returnValue(
                throwError(() => new Error('Failed to save declaration'))
            )

            wizardService.updateState(WizardAction.ON_SECONDARY)
            fixture.detectChanges()

            expect(modalService.showNotification).toHaveBeenCalledWith(
                {
                    notificationLevel: NotificationLevel.DANGER,
                    message: 'common.notification.save-declaration-error',
                },
                component.viewContainerHost.viewContainerRef
            )
        })

        it('when save brouillon button clicked, it shows error if circonstance code is missing in parameters', () => {
            component.form?.controls.circonstance.controls.circonstance.setValue(
                'WRONG' as Circonstance
            )
            spyOn(declarationService, 'updateCirconstances').and.callThrough()
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
            spyOn(modalService, 'showNotification').and.callThrough()
            wizardService.updateState(WizardAction.ON_SECONDARY)
            fixture.detectChanges()

            expect(modalService.showNotification).toHaveBeenCalledWith(
                {
                    notificationLevel: NotificationLevel.DANGER,
                    message:
                        'common.notification.circonstance-is-missing-in-parametrage',
                },
                component.viewContainerHost.viewContainerRef
            )
        })
    })

    describe('next button', () => {
        beforeEach(() => {
            fixture.detectChanges()
            component.form?.controls.detailsSinistre.setValue(
                validDetailsHabitationMock()
            )
        })

        it('when detail du dommage button clicked, it saves the fields into declaration service and navigate to next page', fakeAsync(() => {
            component.form?.controls.circonstance.setValue({
                circonstance: circonstanceKey,
                complementDeCirconstance,
            })
            spyOn(declarationService, 'updateCirconstances').and.callThrough()
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
            spyOn(modalService, 'showNotification').and.callThrough()

            wizardService.updateState(WizardAction.ON_NEXT)
            fixture.detectChanges()

            tick()

            expect(
                declarationService.updateCirconstances
            ).toHaveBeenCalledOnceWith({
                circonstance: circonstanceKey,
                complementDeCirconstance,
            })
            expect(router.navigate).toHaveBeenCalledOnceWith([
                RoutingPath.HABITATION,
                RoutingPath.DOMMAGES,
            ])
        }))

        it('is invalid when circonstance is missing in form', () => {
            component.form?.reset()
            component.form?.controls.circonstance.markAsTouched()
            fixture.detectChanges()
            wizardService.updateState(WizardAction.ON_NEXT)
            expect(component.form?.valid).toBeFalsy()
            expect(
                component.form?.controls.circonstance.controls.circonstance
                    .errors &&
                    component.form?.controls.circonstance.controls.circonstance
                        .errors['required']
            ).toBeTruthy()
        })

        it('show error if circonstance code is missing in parametrage', () => {
            component.form?.controls.circonstance.setValue({
                circonstance: 'WRONG' as Circonstance,
                complementDeCirconstance: 'complement de circonstance',
            })
            component.form?.controls.circonstance.markAllAsTouched()
            fixture.detectChanges()
            spyOn(declarationService, 'updateCirconstances').and.callThrough()
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
            spyOn(modalService, 'showNotification').and.callThrough()
            wizardService.updateState(WizardAction.ON_NEXT)
            fixture.detectChanges()

            expect(modalService.showNotification).toHaveBeenCalledWith(
                {
                    notificationLevel: NotificationLevel.DANGER,
                    message:
                        'common.notification.circonstance-is-missing-in-parametrage',
                },
                component.viewContainerHost.viewContainerRef
            )
        })
    })

    describe('show person panel', () => {
        it('should show a person panel by default', () => {
            fixture.detectChanges()
            const element = fixture.debugElement.query(
                By.css('person-datatile')
            )

            expect(element).not.toBeUndefined()
        })
    })

    describe('onBack', () => {
        beforeEach(() => {
            fixture.detectChanges()
            component.form?.controls.detailsSinistre.setValue(
                validDetailsHabitationMock()
            )
            component.form?.controls.circonstance.patchValue({
                circonstance: Circonstance.GRELE,
            })
        })

        it('should save data in current declaration', fakeAsync(() => {
            spyOn(router, 'navigate').and.returnValue(Promise.resolve(true))
            spyOn(
                declarationService,
                'updateDetailsSinistreHabitation'
            ).and.callThrough()

            wizardService.updateState(WizardAction.ON_PREVIOUS)
            fixture.detectChanges()

            tick(8000)

            expect(
                declarationService.updateDetailsSinistreHabitation
            ).toHaveBeenCalled()
            expect(router.navigate).toHaveBeenCalledOnceWith([
                RoutingPath.HABITATION,
                RoutingPath.GARANTIES,
            ])
        }))
    })

    describe('When we set the stepper for circonstances', () => {
        it('should call setStepper from stepperService with expected params', () => {
            fixture.detectChanges()
            spyOn(stepperService, 'setStepper')
            component.form?.controls.detailsSinistre.setValue(
                validDetailsHabitationMock()
            )
            component.form?.controls.circonstance.patchValue({
                circonstance: circonstanceKey,
            })
            fixture.detectChanges()
            expect(stepperService.setStepper).toHaveBeenCalledWith(
                circonstanceHabitationStepperMock()
            )
        })
    })
})
