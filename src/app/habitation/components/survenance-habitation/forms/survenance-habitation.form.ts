import { FormControl, FormGroup, Validators } from '@angular/forms'
import { SurvenanceHabitationFormStructure } from './survenance-habitation-form-structure'
import { Survenance } from '../../../../shared/models/survenance'

export class SurvenanceHabitationForm extends FormGroup<SurvenanceHabitationFormStructure> {
    constructor() {
        super({
            dateDeSurvenance: new FormControl(
                { value: undefined, disabled: true },
                {
                    nonNullable: true,
                    validators: [Validators.required],
                }
            ),
            heureDeSurvenance: new FormControl(undefined, {
                nonNullable: true,
            }),
        })
    }

    getValue(): Survenance {
        return this.value as Survenance
    }
}
