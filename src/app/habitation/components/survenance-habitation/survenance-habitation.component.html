@if (form) {
    <ng-container [formGroup]="asFormGroup(form)">
        <div class="Panel u-is-txt-monospace u-has-margin-0 u-has-margin-bottom-16">
            <div class="Panel-header has-small-title">
                <span>{{ TRANSLATION_PREFIX + 'title' | translate }}</span>
            </div>
            <div class="Panel-body u-has-padding-24">
                <div class="GridFlex-row Form-row">
                    <div class="GridFlex-col-6">
                        <div class="Form-field is-medium">
                            <label for="dateDeSurvenance" class="Form-field-label">
                                {{ TRANSLATION_PREFIX + 'date-survenance' | translate }}
                            </label>
                            <div class="Form-field-input">
                                <input
                                    id="dateDeSurvenance"
                                    formControlName="dateDeSurvenance"
                                    class="Input u-is-full-width"
                                    [placeholder]="TRANSLATION_PREFIX + 'date-survenance-format' | translate"
                                    [matDatepickerFilter]="maxDateFilter"
                                    [matDatepicker]="myDatepicker" />
                                <i>
                                    <mat-datepicker-toggle class="is-clickable" [for]="myDatepicker"></mat-datepicker-toggle>
                                    <mat-datepicker #myDatepicker></mat-datepicker>
                                </i>
                            </div>
                        </div>
                    </div>
                    <div class="GridFlex-col-6">
                        <div class="Form-field is-medium">
                            <label for="heureDeSurvenance" class="Form-field-label">
                                {{ TRANSLATION_PREFIX + 'heure-survenance' | translate }}
                                <span class="Form-field-label-extra">{{ 'common.facultatif' | translate }}</span>
                            </label>
                            <div class="Form-field-input">
                                <input id="heureDeSurvenance" formControlName="heureDeSurvenance" class="Input" type="time" />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </ng-container>
}
