import { ComponentFixture, TestBed } from '@angular/core/testing'
import { SurvenanceHabitationComponent } from './survenance-habitation.component'
import { SurvenanceHabitationForm } from './forms/survenance-habitation.form'
import { By } from '@angular/platform-browser'
import { provideDate } from '../../../core/providers/date.provider'
import { maxDateFilter } from '../../../shared/utils/date.utils'

describe('SurvenanceHabitationComponent', () => {
    let component: SurvenanceHabitationComponent
    let fixture: ComponentFixture<SurvenanceHabitationComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [SurvenanceHabitationComponent],
            providers: [provideDate()],
        }).compileComponents()

        fixture = TestBed.createComponent(SurvenanceHabitationComponent)
        component = fixture.componentInstance
        component.form = new SurvenanceHabitationForm()
    })

    describe('date et heure de survenance', () => {
        it('should display form correctly in the template', () => {
            component.form?.patchValue({
                dateDeSurvenance: new Date(2023, 5, 15),
                heureDeSurvenance: '14:30',
            })
            fixture.detectChanges()

            const dateDeSurvenance = fixture.debugElement.query(
                By.css('#dateDeSurvenance')
            ).nativeElement
            expect(dateDeSurvenance.value).toBe('15.06.2023')
            const heureInput = fixture.debugElement.query(
                By.css('#heureDeSurvenance')
            ).nativeElement
            expect(heureInput.value).toBe('14:30')
        })

        it('should apply maxDateFilter correctly', () => {
            const today = new Date()
            const tomorrow = new Date()
            tomorrow.setDate(today.getDate() + 1)
            expect(maxDateFilter(today)).toBeTrue()
            expect(maxDateFilter(tomorrow)).toBeFalse()
        })
    })
})
