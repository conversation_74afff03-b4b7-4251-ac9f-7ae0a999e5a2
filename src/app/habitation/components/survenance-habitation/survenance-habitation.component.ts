import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { SurvenanceHabitationForm } from './forms/survenance-habitation.form'
import { SurvenanceHabitationFormStructure } from './forms/survenance-habitation-form-structure'
import { I18nPipe } from '@foyer/ng-i18n'

import {
    MatDatepicker,
    MatDatepickerInput,
    MatDatepickerToggle,
} from '@angular/material/datepicker'
import { maxDateFilter } from '../../../shared/utils/date.utils'

@Component({
    selector: 'survenance-habitation',
    standalone: true,
    imports: [
        I18nPipe,
        ReactiveFormsModule,
        MatDatepickerToggle,
        MatDatepicker,
        MatDatepickerInput,
    ],
    templateUrl: './survenance-habitation.component.html',
    styleUrl: './survenance-habitation.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SurvenanceHabitationComponent {
    @Input()
    form?: SurvenanceHabitationForm

    readonly TRANSLATION_PREFIX: string = 'common.survenance-habitation.'

    protected readonly maxDateFilter = maxDateFilter

    asFormGroup(
        form: SurvenanceHabitationForm
    ): FormGroup<SurvenanceHabitationFormStructure> {
        return form as unknown as FormGroup<SurvenanceHabitationFormStructure>
    }
}
