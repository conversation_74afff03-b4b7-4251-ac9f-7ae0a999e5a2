import {
    ChangeDetectionStrategy,
    Component,
    Input,
    OnInit,
} from '@angular/core'
import { DommagesHabitationForm } from '../dommages-habitation/forms/dommages-habitation-form'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { TypeGaranties } from '../../../shared/enums/type-garanties.enum'
import { ObjetsDeDommage } from '../../enums/objets-de-dommage.enum'
import { AutresObjetsDeDommageComponent } from './autres-objets-de-dommage/autres-objets-de-dommage.component'

import { I18nPipe } from '@foyer/ng-i18n'
import { ICON_BY_OBJET_DOMMAGE } from '../../../shared/utils/icone.utils'
import { OBJETS_DOMMAGE_BY_GARANTIE } from '../../../shared/utils/objets-de-dommage.utils'
import { ChoiceCardComponent } from '../../../shared/components/choice-card/choice-card.component'

@Component({
    selector: 'objets-de-dommage',
    templateUrl: './objets-de-dommage.component.html',
    styleUrls: ['./objets-de-dommage.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ReactiveFormsModule,
        AutresObjetsDeDommageComponent,
        I18nPipe,
        ChoiceCardComponent,
    ],
})
export class ObjetsDeDommageComponent implements OnInit {
    @Input()
    form?: DommagesHabitationForm
    @Input()
    selectedGaranties?: TypeGaranties[]

    readonly TRANSLATION_PREFIX: string = 'common.objets-de-dommage.'

    objetsDeDommages: ObjetsDeDommage[] = []

    protected readonly ICON_BY_OBJET_DOMMAGE = ICON_BY_OBJET_DOMMAGE

    asFormGroup(form: DommagesHabitationForm): FormGroup {
        return form as unknown as FormGroup
    }

    ngOnInit(): void {
        if (this.selectedGaranties) {
            this.objetsDeDommages = this.getFilteredObjetsDommageByGaranties(
                this.selectedGaranties
            )
        }
    }

    isObjetSelected(objetDeDommage: ObjetsDeDommage): boolean {
        return (
            this.form?.controls.objetsDeDommage.value?.includes(
                objetDeDommage
            ) ?? false
        )
    }

    onObjetsDeDommageChange(value: ObjetsDeDommage): void {
        const current = this.form?.controls.objetsDeDommage.value ?? []
        const index = current.indexOf(value)
        if (index > -1) {
            this.form?.controls.objetsDeDommage.setValue(
                current.filter((v) => v !== value)
            )
        } else {
            this.form?.controls.objetsDeDommage.setValue([...current, value])
        }
    }

    private getFilteredObjetsDommageByGaranties(
        typeGaranties?: TypeGaranties[]
    ): ObjetsDeDommage[] {
        return (typeGaranties ?? [])
            .flatMap(
                (garantie) => OBJETS_DOMMAGE_BY_GARANTIE.get(garantie) ?? []
            )
            .filter((value) => value)
    }
}
