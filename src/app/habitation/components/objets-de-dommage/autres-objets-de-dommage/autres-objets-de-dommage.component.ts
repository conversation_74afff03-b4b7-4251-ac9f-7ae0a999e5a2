import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { FormControl, ReactiveFormsModule } from '@angular/forms'

import { EnterKeyEventDirective } from '../../../../shared/directives/enter-key-event.directive'
import { I18nPipe } from '@foyer/ng-i18n'
import { DommagesHabitationForm } from '../../dommages-habitation/forms/dommages-habitation-form'
import { titleCase } from '../../../../shared/utils/string.utils'

@Component({
    selector: 'autres-objets-de-dommage',
    templateUrl: './autres-objets-de-dommage.component.html',
    styleUrls: ['./autres-objets-de-dommage.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [ReactiveFormsModule, EnterKeyEventDirective, I18nPipe],
})
export class AutresObjetsDeDommageComponent {
    @Input()
    form?: DommagesHabitationForm

    readonly TRANSLATION_PREFIX: string =
        'common.objets-de-dommage.autres-objets-de-dommage.'

    autreObjetForm = new FormControl<string | undefined>(undefined, {
        nonNullable: true,
    })

    enterKeyPressed(): void {
        this.onAddAutreDommageClick()
    }

    onAddAutreDommageClick(): void {
        const formText = titleCase(this.autreObjetForm.value)
        if (formText && !this.hasAutresObjetsDeDommage(formText)) {
            this.toggleAutresObjetsDeDommage(formText)
        }
        this.autreObjetForm.reset()
    }

    toggleAutresObjetsDeDommage(text: string): void {
        const currentAutresObjetsDeDommage =
            this.form?.controls.autresObjetsDeDommage.value ?? []
        const hasAutresObjetsDeDommage = this.hasAutresObjetsDeDommage(text)
        const updatedAutresObjetsDeDommage = hasAutresObjetsDeDommage
            ? currentAutresObjetsDeDommage.filter(
                  (currentValue) => currentValue !== text
              )
            : [...currentAutresObjetsDeDommage, text]
        this.form?.controls.autresObjetsDeDommage.setValue(
            updatedAutresObjetsDeDommage
        )
        this.form?.controls.autresObjetsDeDommage.markAsDirty()
    }

    private hasAutresObjetsDeDommage(text: string): boolean {
        return (this.form?.controls.autresObjetsDeDommage.value ?? []).includes(
            text
        )
    }
}
