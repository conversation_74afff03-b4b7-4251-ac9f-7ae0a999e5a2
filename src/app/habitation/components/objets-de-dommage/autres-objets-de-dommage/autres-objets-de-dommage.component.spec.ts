import { ComponentFixture, TestBed } from '@angular/core/testing'
import { AutresObjetsDeDommageComponent } from './autres-objets-de-dommage.component'
import { DommagesHabitationForm } from '../../dommages-habitation/forms/dommages-habitation-form'
import { By } from '@angular/platform-browser'
import { DebugElement } from '@angular/core'

describe('AutresObjetsDeDommageComponent', () => {
    let component: AutresObjetsDeDommageComponent
    let fixture: ComponentFixture<AutresObjetsDeDommageComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [AutresObjetsDeDommageComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(AutresObjetsDeDommageComponent)
        component = fixture.componentInstance
        component.form = new DommagesHabitationForm()
    })

    describe('When we add autreDommage from the input', () => {
        let button: DebugElement

        beforeEach(() => {
            button = fixture.debugElement.query(By.css('.ButtonContained'))
        })

        it('should call onAddAutreDommageClick when enter key is pressed', () => {
            spyOn(component, 'onAddAutreDommageClick')
            component.enterKeyPressed()
            expect(component.onAddAutreDommageClick).toHaveBeenCalled()
        })

        it('should execute onAddAutreDommageClick and toggleAutresObjetsDeDommage with text value from input when clicking on button', () => {
            component.autreObjetForm.setValue('lit nuptial')
            fixture.detectChanges()
            spyOn(component, 'onAddAutreDommageClick').and.callThrough()
            spyOn(component, 'toggleAutresObjetsDeDommage')

            button.nativeElement.click()

            expect(component.onAddAutreDommageClick).toHaveBeenCalled()
            expect(
                component.toggleAutresObjetsDeDommage
            ).toHaveBeenCalledOnceWith('Lit nuptial')
            expect(component.autreObjetForm.value).toBeNull()
        })

        it('should not execute toggleAutresObjetsDeDommage when clicking on button if input value has no text', () => {
            spyOn(component, 'onAddAutreDommageClick').and.callThrough()
            spyOn(component, 'toggleAutresObjetsDeDommage')

            expect(component.autreObjetForm.value).toBeNull()

            button.nativeElement.click()

            expect(component.onAddAutreDommageClick).toHaveBeenCalled()
            expect(component.toggleAutresObjetsDeDommage).not.toHaveBeenCalled()
            expect(component.autreObjetForm.value).toBeNull()
        })
    })

    describe('onAddAutreDommageClick', () => {
        it('should add multiple text to the autresObjetsDeDommage form control', () => {
            component.autreObjetForm.setValue('lit nuptial')
            fixture.detectChanges()
            component.onAddAutreDommageClick()
            component.autreObjetForm.setValue('lit baldaquin')
            fixture.detectChanges()
            component.onAddAutreDommageClick()
            expect(
                component.form?.controls.autresObjetsDeDommage.value
            ).toEqual(['Lit nuptial', 'Lit baldaquin'])
        })

        it('should not add same text to the autresObjetsDeDommage form control', () => {
            component.autreObjetForm.setValue('lit baldaquin')
            fixture.detectChanges()
            component.onAddAutreDommageClick()
            component.autreObjetForm.setValue('lit baldaquin')
            fixture.detectChanges()
            component.onAddAutreDommageClick()
            expect(
                component.form?.controls.autresObjetsDeDommage.value
            ).toEqual(['Lit baldaquin'])
        })
    })

    describe('When we want to remove badge', () => {
        it('should call toggleAutresObjetsDeDommage with expected text value and remove it from form', () => {
            spyOn(component, 'toggleAutresObjetsDeDommage')

            component.form?.controls.autresObjetsDeDommage.setValue([
                'Lit nuptial',
                'Lit baldaquin',
            ])
            fixture.detectChanges()
            expect(
                component.form?.controls.autresObjetsDeDommage.value
            ).toEqual(['Lit nuptial', 'Lit baldaquin'])

            const icons = fixture.debugElement.queryAll(By.css('.mi-close'))
            icons[1].nativeElement.click()
            expect(
                component.toggleAutresObjetsDeDommage
            ).toHaveBeenCalledOnceWith('Lit baldaquin')
        })
    })
})
