<div class="Form-row">
    <div class="Form-field is-medium">
        <label class="Form-field-label">
            {{ TRANSLATION_PREFIX + 'objets-endommages' | translate }}
        </label>
        <div class="u-is-flex-row u-is-flex-gap-8">
            <div class="GridFlex-col-9 Form-field-input u-has-padding-0">
                <input
                    class="Input is-full-width is-medium"
                    data-testid="autreObjetDommageInput"
                    type="text"
                    [formControl]="autreObjetForm"
                    (enterKeyEvent)="enterKeyPressed()"
                    [placeholder]="TRANSLATION_PREFIX + 'ajouter-un-objet-de-dommage-placeholder' | translate" />
            </div>
            <div class="GridFlex-col-3">
                <button
                    class="ButtonContained is-medium is-light u-has-no-border is-full-width"
                    data-testid="autreObjetDommageButton"
                    (click)="onAddAutreDommageClick()"
                    type="button">
                    <i class="mi-add"></i>
                    <span>
                        {{ TRANSLATION_PREFIX + 'ajouter-un-objet-de-dommage' | translate }}
                    </span>
                </button>
            </div>
        </div>
        @if (form && form.controls.autresObjetsDeDommage.value; as autresObjetsDeDommage) {
            <div class="u-is-flex-gap-4 u-has-margin-top-4 wrap" data-testid="autresObjetsDommages">
                @for (autreDommage of autresObjetsDeDommage; track autreDommage) {
                    <div>
                        <span class="Badge is-inverted is-primary">
                            {{ autreDommage }}
                            <i class="mi-close" (click)="toggleAutresObjetsDeDommage(autreDommage)"></i>
                        </span>
                    </div>
                }
            </div>
        }
    </div>
</div>
