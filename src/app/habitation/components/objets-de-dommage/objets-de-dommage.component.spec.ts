import { ComponentFixture, TestBed } from '@angular/core/testing'

import { ObjetsDeDommageComponent } from './objets-de-dommage.component'
import { TypeGaranties } from '../../../shared/enums/type-garanties.enum'
import { ObjetsDeDommage } from '../../enums/objets-de-dommage.enum'
import { DommagesHabitationForm } from '../dommages-habitation/forms/dommages-habitation-form'

describe('ObjetsDeDommageComponent', () => {
    let component: ObjetsDeDommageComponent
    let fixture: ComponentFixture<ObjetsDeDommageComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ObjetsDeDommageComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(ObjetsDeDommageComponent)
        component = fixture.componentInstance
        component.form = new DommagesHabitationForm()
    })

    describe('When we want to filter objetsDeDommages from selectedGaranties', () => {
        it('should return empty array expected list for ACC, ARAG', () => {
            component.selectedGaranties = [
                TypeGaranties.ACC,
                TypeGaranties.ARAG,
            ]
            fixture.detectChanges()
            expect(component.objetsDeDommages).toEqual([])
        })

        it('should return expected list for VOLN, DELN, BDGN', () => {
            component.selectedGaranties = [
                TypeGaranties.VOLN,
                TypeGaranties.DELN,
                TypeGaranties.BDGN,
            ]
            fixture.detectChanges()
            expect(component.objetsDeDommages.length).toBe(14)
            expect(component.objetsDeDommages).toEqual([
                ObjetsDeDommage.BIJOUX,
                ObjetsDeDommage.DEGRADATION_IMMOBILIERE,
                ObjetsDeDommage.MOBILIER,
                ObjetsDeDommage.OBJETS_DE_VALEUR,
                ObjetsDeDommage.MACHINE_A_LAVER,
                ObjetsDeDommage.CHAUDIERE,
                ObjetsDeDommage.LAVE_VAISSELLE,
                ObjetsDeDommage.SECHE_LINGE,
                ObjetsDeDommage.PLAQUE_DE_CUISSON,
                ObjetsDeDommage.REFRIGERATEUR,
                ObjetsDeDommage.TELEVISION,
                ObjetsDeDommage.VITRES,
                ObjetsDeDommage.PORTES_VITREE,
                ObjetsDeDommage.PLAQUE_VITROCERAMIQUE_OU_INDUCTION,
            ])
        })

        it('should return expected list for VOLN, CNN, ACC', () => {
            component.selectedGaranties = [
                TypeGaranties.VOLN,
                TypeGaranties.CNN,
                TypeGaranties.ACC,
            ]
            fixture.detectChanges()
            expect(component.objetsDeDommages.length).toBe(4)
            expect(component.objetsDeDommages).toEqual([
                ObjetsDeDommage.BIJOUX,
                ObjetsDeDommage.DEGRADATION_IMMOBILIERE,
                ObjetsDeDommage.MOBILIER,
                ObjetsDeDommage.OBJETS_DE_VALEUR,
            ])
        })
    })

    describe('When we select on choice card', () => {
        beforeEach(() => {
            component.form?.controls.objetsDeDommage.setValue([])
            fixture.detectChanges()
        })

        it('should add and remove objetsDeDommage in form control when onObjetsDeDommageChange is called', () => {
            const value = ObjetsDeDommage.PLAQUE_DE_CUISSON

            component.onObjetsDeDommageChange(value)
            expect(component.form?.controls.objetsDeDommage.value).toContain(
                value
            )

            component.onObjetsDeDommageChange(value)
            expect(
                component.form?.controls.objetsDeDommage.value
            ).not.toContain(value)
        })

        it('should select multiple objetsDeDommage', () => {
            const values = [
                ObjetsDeDommage.PLAQUE_DE_CUISSON,
                ObjetsDeDommage.OBJETS_DE_VALEUR,
                ObjetsDeDommage.BIJOUX,
            ]

            values.forEach((v) => component.onObjetsDeDommageChange(v))

            expect(component.form?.controls.objetsDeDommage.value?.length).toBe(
                3
            )
            expect(component.form?.controls.objetsDeDommage.value).toEqual(
                values
            )
        })

        it('should return true if objetDeDommage is selected or false when not selected', () => {
            const value = ObjetsDeDommage.PLAQUE_DE_CUISSON
            component.form?.controls.objetsDeDommage.setValue([value])
            expect(component.isObjetSelected(value)).toBeTruthy()

            component.form?.controls.objetsDeDommage.setValue([])
            expect(component.isObjetSelected(value)).toBeFalsy()
        })
    })
})
