<div class="Panel u-is-txt-monospace">
    @if (form) {
        <form class="Form" [formGroup]="asFormGroup(form)">
            <div class="Panel-header has-small-title">
                <span>{{ TRANSLATION_PREFIX + 'title' | translate }}</span>
            </div>
            <div class="Panel-body">
                <div class="GridFlex-row">
                    @for (objetDeDommage of objetsDeDommages; track objetDeDommage) {
                        <div class="GridFlex-col-4">
                            <choice-card
                                [value]="objetDeDommage"
                                [selected]="isObjetSelected(objetDeDommage)"
                                [withMultiChoice]="true"
                                (selectionChange)="onObjetsDeDommageChange($event)">
                                <ng-container dataTile>
                                    <div class="DataTile-icon is-medium">
                                        <i [class]="ICON_BY_OBJET_DOMMAGE.get(objetDeDommage)" aria-hidden="true"></i>
                                    </div>
                                    <div class="DataTile-content">
                                        <div class="DataTile-value u-is-txt-feather-grey-900">
                                            {{ 'enums.objets-de-dommage.' + objetDeDommage | translate }}
                                        </div>
                                    </div>
                                </ng-container>
                            </choice-card>
                        </div>
                    }
                </div>
                <div class="GridFlex-row">
                    <div class="GridFlex-col-12 u-has-margin-top-8">
                        <autres-objets-de-dommage [form]="form"></autres-objets-de-dommage>
                    </div>
                </div>
            </div>
        </form>
    }
</div>
