import { ChangeDetectorRef, Component, forwardRef } from '@angular/core'
import { SvgHandlersComponent } from '../../../shared/components/svg.handlers.component'
import { fadeInAnimation } from '../../../shared/animations/fade-in.animation'
import { fadeHoverInAnimation } from '../../../shared/animations/fade-hover-in.animation'
import { NG_VALUE_ACCESSOR } from '@angular/forms'
import { ZonesMaisonExterieur } from '../../enums/zones-maison-exterieur.enum'

import { NgIf } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'maison-exterieur',
    templateUrl: './maison-exterieur.component.svg',
    styleUrls: ['./maison-exterieur.component.scss'],
    animations: [fadeInAnimation, fadeHoverInAnimation],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => MaisonExterieurComponent),
        },
    ],
    standalone: true,
    imports: [NgIf, I18nPipe],
})
export class MaisonExterieurComponent extends SvgHandlersComponent<ZonesMaisonExterieur> {
    readonly ZonesMaisonExterieur = ZonesMaisonExterieur

    constructor(
        protected override readonly changeDetectorRef: ChangeDetectorRef
    ) {
        super(changeDetectorRef, Object.keys(ZonesMaisonExterieur))
    }
}
