import { ChangeDetectorRef, Component, forwardRef } from '@angular/core'
import { SvgHandlersComponent } from '../../../shared/components/svg.handlers.component'
import { ZonesMaisonInterieur } from '../../enums/zones-maison-interieur.enum'
import { fadeInAnimation } from '../../../shared/animations/fade-in.animation'
import { fadeHoverInAnimation } from '../../../shared/animations/fade-hover-in.animation'
import { NG_VALUE_ACCESSOR } from '@angular/forms'

import { NgIf } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'

@Component({
    selector: 'maison-interieur',
    templateUrl: './maison-interieur.component.svg',
    styleUrls: ['./maison-interieur.component.scss'],
    animations: [fadeInAnimation, fadeHoverInAnimation],
    providers: [
        {
            provide: NG_VALUE_ACCESSOR,
            multi: true,
            useExisting: forwardRef(() => MaisonInterieurComponent),
        },
    ],
    standalone: true,
    imports: [NgIf, I18nPipe],
})
export class MaisonInterieurComponent extends SvgHandlersComponent<ZonesMaisonInterieur> {
    readonly ZoneMaisonInterieur = ZonesMaisonInterieur

    constructor(
        protected override readonly changeDetectorRef: ChangeDetectorRef
    ) {
        super(changeDetectorRef, Object.keys(ZonesMaisonInterieur))
    }
}
