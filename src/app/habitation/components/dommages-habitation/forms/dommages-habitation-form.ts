import { FormControl, FormGroup } from '@angular/forms'
import { DommagesHabitationFormStructure } from './dommages-habitation-form-structure'
import { DommagesHabitation } from '../../../models/dommages-habitation'

export class DommagesHabitationForm extends FormGroup<DommagesHabitationFormStructure> {
    constructor() {
        super({
            degatsPieces: new FormControl([], {
                nonNullable: true,
            }),
            degatsExterieurs: new FormControl([], {
                nonNullable: true,
            }),
            objetsDeDommage: new FormControl([], {
                nonNullable: true,
            }),
            autresObjetsDeDommage: new FormControl([], {
                nonNullable: true,
            }),
        })
    }

    resetObjetsDeDommageForm(): void {
        this.controls.objetsDeDommage.reset()
        this.controls.autresObjetsDeDommage.reset()
        this.controls.objetsDeDommage.updateValueAndValidity()
        this.controls.autresObjetsDeDommage.updateValueAndValidity()
    }

    getValue(): DommagesHabitation {
        return this.value as DommagesHabitation
    }
}
