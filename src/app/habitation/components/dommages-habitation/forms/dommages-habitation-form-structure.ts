import { FormControl } from '@angular/forms'
import { ZonesMaisonInterieur } from '../../../enums/zones-maison-interieur.enum'
import { ZonesMaisonExterieur } from '../../../enums/zones-maison-exterieur.enum'
import { ObjetsDeDommage } from '../../../enums/objets-de-dommage.enum'

export interface DommagesHabitationFormStructure {
    degatsPieces: FormControl<ZonesMaisonInterieur[] | undefined>
    degatsExterieurs: FormControl<ZonesMaisonExterieur[] | undefined>
    objetsDeDommage: FormControl<ObjetsDeDommage[] | undefined>
    autresObjetsDeDommage: FormControl<string[] | undefined>
}
