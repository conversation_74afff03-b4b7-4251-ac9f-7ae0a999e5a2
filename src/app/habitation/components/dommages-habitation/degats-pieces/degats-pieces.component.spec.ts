import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DegatsPiecesComponent } from './degats-pieces.component'
import { DommagesHabitationForm } from '../forms/dommages-habitation-form'
import { expectElementToExist } from '../../../../shared/utils/test.utils'

describe('DegatsPiecesComponent', () => {
    let component: DegatsPiecesComponent
    let fixture: ComponentFixture<DegatsPiecesComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DegatsPiecesComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(DegatsPiecesComponent)
        component = fixture.componentInstance
        component.form = new DommagesHabitationForm()
        fixture.detectChanges()
    })

    it('should show maison interieur component', () => {
        expectElementToExist(fixture, 'maison-interieur')
    })
})
