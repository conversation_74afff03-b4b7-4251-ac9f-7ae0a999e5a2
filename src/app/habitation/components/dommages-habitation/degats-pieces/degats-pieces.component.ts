import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'

import { I18nPipe } from '@foyer/ng-i18n'
import { MaisonInterieurComponent } from '../../maison-interieur/maison-interieur.component'
import { DommagesHabitationForm } from '../forms/dommages-habitation-form'

@Component({
    selector: 'degats-pieces',
    templateUrl: './degats-pieces.component.html',
    styleUrls: ['./degats-pieces.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [ReactiveFormsModule, MaisonInterieurComponent, I18nPipe],
})
export class DegatsPiecesComponent {
    @Input()
    form?: DommagesHabitationForm

    readonly TRANSLATION_PREFIX: string =
        'common.dommages-habitation.degats-pieces.'

    asFormGroup(form: DommagesHabitationForm): FormGroup {
        return form as unknown as FormGroup
    }
}
