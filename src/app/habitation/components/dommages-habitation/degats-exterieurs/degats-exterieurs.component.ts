import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'

import { I18nPipe } from '@foyer/ng-i18n'
import { MaisonExterieurComponent } from '../../maison-exterieur/maison-exterieur.component'
import { DommagesHabitationForm } from '../forms/dommages-habitation-form'

@Component({
    selector: 'degats-exterieurs',
    templateUrl: './degats-exterieurs.component.html',
    styleUrls: ['./degats-exterieurs.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [ReactiveFormsModule, MaisonExterieurComponent, I18nPipe],
})
export class DegatsExterieursComponent {
    @Input()
    form?: DommagesHabitationForm

    readonly TRANSLATION_PREFIX: string =
        'common.dommages-habitation.degats-exterieurs.'

    asFormGroup(form: DommagesHabitationForm): FormGroup {
        return form as unknown as FormGroup
    }
}
