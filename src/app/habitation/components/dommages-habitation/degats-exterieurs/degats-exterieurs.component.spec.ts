import { ComponentFixture, TestBed } from '@angular/core/testing'
import { DegatsExterieursComponent } from './degats-exterieurs.component'
import { DommagesHabitationForm } from '../forms/dommages-habitation-form'
import { expectElementToExist } from '../../../../shared/utils/test.utils'

describe('DegatsExterieursComponent', () => {
    let component: DegatsExterieursComponent
    let fixture: ComponentFixture<DegatsExterieursComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DegatsExterieursComponent],
        }).compileComponents()

        fixture = TestBed.createComponent(DegatsExterieursComponent)
        component = fixture.componentInstance
        component.form = new DommagesHabitationForm()
        fixture.detectChanges()
    })

    it('should show maison exterieur component', () => {
        expectElementToExist(fixture, 'maison-exterieur')
    })
})
