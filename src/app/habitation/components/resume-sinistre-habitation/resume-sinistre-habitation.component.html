<div class="Panel u-is-txt-monospace">
    <div class="Panel-header has-small-title">
        <span>{{ TRANSLATION_PREFIX + 'title' | translate }}</span>
    </div>

    @if (
        {
            declaration: declaration$ | async,
            risque: risque$ | async,
            form: form$ | async,
        };
        as obs
    ) {
        @if (obs.form; as form) {
            <div class="Panel-body u-has-padding-24">
                @if (obs.risque) {
                    <div class="Panel risquesPanel u-has-margin-bottom-0">
                        <risque-panel [risque]="obs.risque"></risque-panel>
                    </div>
                }

                <div class="u-has-padding-top-16">
                    <resume-survenance-habitation [survenance]="form.controls.survenance.getValue()"></resume-survenance-habitation>

                    @if (form.controls.referenceProducteur.value) {
                        <data-tile class="reference-producteur-data-tile">
                            <span label>{{ TRANSLATION_PREFIX + 'reference-producteur' | translate }}</span>
                            <span value>
                                {{ form.controls.referenceProducteur.value }}
                            </span>
                        </data-tile>
                    }

                    @if (form.controls.garanties.value; as garantiesForm) {
                        @if (garantiesForm.length > 0) {
                            <data-tile class="garantie-concernee-data-tile" [isTxtTruncated]="true">
                                <span label>{{ TRANSLATION_PREFIX + 'garantie-concernee' | translate }}</span>
                                <span value>
                                    @if (garantiesForm | getTranslationForArrayEnum: 'enums.code-garantie.' | async; as garanties) {
                                        <with-tooltip [text]="garanties" [textType]="TextType.TEXT_TRANSLATED">
                                            {{ garanties }}
                                        </with-tooltip>
                                    }
                                </span>
                            </data-tile>
                        }
                    }
                </div>

                @if (resumeDetailSinistreHabitationIsShowed(form)) {
                    <div class="u-has-padding-top-16">
                        <resume-detail-sinistre-habitation
                            [detailsSinistre]="form.controls.detailsSinistre.getValue()"
                            [circonstance]="form.controls.circonstance.getValue()"></resume-detail-sinistre-habitation>
                    </div>
                }

                @if (resumeDommagesHabitationIsShowed(form.controls.dommages)) {
                    <div class="u-has-padding-top-16">
                        <resume-dommages-habitation [dommages]="form.controls.dommages.getValue()"></resume-dommages-habitation>
                    </div>
                }

                @if (resumeModalitesIsShowed(form.controls.modalites)) {
                    <div class="u-has-padding-top-16">
                        <resume-modalites [modalites]="form.controls.modalites.getValueHabitation()"></resume-modalites>
                    </div>
                }

                @if (form.controls.piecesJointes.value && form.controls.piecesJointes.value.length > 0) {
                    <div class="u-has-padding-top-16">
                        <resume-pieces-jointes [piecesJointes]="form.controls.piecesJointes.value"></resume-pieces-jointes>
                    </div>
                }
            </div>
        }
    }
</div>
