import { ComponentFixture, TestBed } from '@angular/core/testing'

import { ResumeSinistreHabitationComponent } from './resume-sinistre-habitation.component'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { DeclarationServiceMock } from '../../../shared/services/declaration/declaration.service.mock'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import { RisquesServiceMock } from '../../../shared/services/risques/risques.service.mock'
import { provideTranslation } from '../../../core/providers/translation.provider'
import {
    checkTextContentForElement,
    expectElementToExist,
} from '../../../shared/utils/test.utils'
import { DeclarationKind } from '../../../shared/enums/declaration-kind.enum'
import { TypeGaranties } from '../../../shared/enums/type-garanties.enum'
import { circonstancesMock } from '../../../shared/mocks/circonstances.mock'
import { ZonesMaisonInterieur } from '../../enums/zones-maison-interieur.enum'
import { ZonesMaisonExterieur } from '../../enums/zones-maison-exterieur.enum'

describe('ResumeSinistreHabitationComponent', () => {
    let component: ResumeSinistreHabitationComponent
    let fixture: ComponentFixture<ResumeSinistreHabitationComponent>
    let declarationService: DeclarationService

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ResumeSinistreHabitationComponent],
            providers: [
                provideTranslation(),
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
                {
                    provide: RisquesService,
                    useClass: RisquesServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(ResumeSinistreHabitationComponent)
        component = fixture.componentInstance
        declarationService = TestBed.inject(DeclarationService)
        declarationService.initializeForm(DeclarationKind.HABITATION)
        fixture.detectChanges()
    })

    it('should display reference producteur when in form', () => {
        component.form$?.subscribe((form) => {
            form.patchValue({ referenceProducteur: 'michel' })
        })
        fixture.detectChanges()
        expectElementToExist(fixture, '.reference-producteur-data-tile')
    })

    it('should display resume survenance component when there is a value in surevenance form', () => {
        component.form$?.subscribe((form) => {
            form.controls.survenance.patchValue({
                dateDeSurvenance: new Date(),
            })
        })
        fixture.detectChanges()
        expectElementToExist(fixture, 'resume-survenance-habitation')
    })

    it('should display resume of sinitre on the template with garanties', () => {
        component.form$?.subscribe((form) => {
            form.patchValue({
                garanties: [TypeGaranties.CNN, TypeGaranties.ACC],
            })
        })
        fixture.detectChanges()

        expectElementToExist(fixture, '.risquesPanel')
        checkTextContentForElement(
            fixture,
            '.garantie-concernee-data-tile .DataTile-label span',
            'Garantie concernée'
        )
        checkTextContentForElement(
            fixture,
            '.garantie-concernee-data-tile .DataTile-value span',
            'Catastrophes naturelles, Accident de la circulation'
        )
    })

    it('should display resume detail sinistre component when there is a value in detailsSinistre form', () => {
        component.form$?.subscribe((form) => {
            form.controls.detailsSinistre.markAsDirty()
            form.controls.detailsSinistre.controls.tiersImplique.setValue(true)
        })
        fixture.detectChanges()
        expectElementToExist(fixture, 'resume-detail-sinistre-habitation')
    })

    it('should display resume detail sinistre component when there is a value in circonstance form', () => {
        component.form$?.subscribe((form) => {
            form.controls.circonstance.markAsDirty()
            form.controls.circonstance.controls.circonstance.setValue(
                circonstancesMock()[0].key
            )
        })
        fixture.detectChanges()
        expectElementToExist(fixture, 'resume-detail-sinistre-habitation')
    })

    it('should display resume dommages component when there is a value in degats pieces', () => {
        component.form$?.subscribe((form) => {
            form.controls.dommages.markAllAsTouched()
            form.controls.dommages.controls.degatsPieces.setValue([
                ZonesMaisonInterieur.CUISINE,
            ])
        })
        fixture.detectChanges()
        expectElementToExist(fixture, 'resume-dommages-habitation')
    })

    it('should display resume dommages component when there is a value in degats exterieurs', () => {
        component.form$?.subscribe((form) => {
            form.controls.dommages.markAllAsTouched()
            form.controls.dommages.controls.degatsExterieurs.setValue([
                ZonesMaisonExterieur.PISCINE,
            ])
        })
        fixture.detectChanges()
        expectElementToExist(fixture, 'resume-dommages-habitation')
    })

    it('should display resume pieces jointes component when there is a pieces jointes', () => {
        component.form$?.subscribe((form) => {
            form.controls.piecesJointes.setValue(['une piece jointe'])
        })
        fixture.detectChanges()
        expectElementToExist(fixture, 'resume-pieces-jointes')
    })
})
