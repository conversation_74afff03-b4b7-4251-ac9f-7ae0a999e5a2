import {
    ChangeDetectionStrategy,
    Component,
    inject,
    OnInit,
} from '@angular/core'
import { RisquesService } from '../../../shared/services/risques/risques.service'
import {
    distinctUntilChanged,
    map,
    Observable,
    startWith,
    switchMap,
} from 'rxjs'
import { DeclarationHabitationForm } from '../../models/forms/declaration-habitation.form'
import { DommagesHabitationForm } from '../dommages-habitation/forms/dommages-habitation-form'
import { ResumeDommagesHabitationComponent } from './resume-dommages-habitation/resume-dommages-habitation.component'
import { TextType } from '../../../shared/enums/text-type.enum'
import { ModalitesForm } from '../../../shared/components/modalites/forms/modalites.form'
import { ResumeDetailSinistreHabitationComponent } from './resume-detail-sinistre-habitation/resume-detail-sinistre-habitation.component'
import { hasNonNullValues } from '../../../shared/utils/objects.utils'
import { Risque } from '../../../shared/models/risque'
import { AsyncPipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { RisquePanelComponent } from '../../../shared/components/risque-panel/risque-panel.component'
import { DataTileComponent } from '../../../shared/components/data-tile/data-tile.component'
import { ResumeModalitesComponent } from '../../../shared/components/resume-sinistre/resume-modalites/resume-modalites.component'
import { ResumePiecesJointesComponent } from '../../../shared/components/resume-sinistre/resume-pieces-jointes/resume-pieces-jointes.component'
import { GetTranslationForArrayEnumPipe } from '../../../shared/pipes/translation/get-translation-for-array-enum.pipe'
import { WithTooltipComponent } from '../../../shared/components/with-tootip/with-tooltip.component'
import { DeclarationService } from '../../../shared/services/declaration/declaration.service'
import { DeclarationHabitation } from '../../models/declaration-habitation'
import { ResumeSurvenanceHabitationComponent } from './resume-survenance-habitation/resume-survenance-habitation.component'

@Component({
    selector: 'resume-sinistre-habitation',
    templateUrl: './resume-sinistre-habitation.component.html',
    styleUrls: ['./resume-sinistre-habitation.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        ResumeDommagesHabitationComponent,
        ResumeDetailSinistreHabitationComponent,
        AsyncPipe,
        I18nPipe,
        RisquePanelComponent,
        DataTileComponent,
        ResumeModalitesComponent,
        ResumePiecesJointesComponent,
        GetTranslationForArrayEnumPipe,
        WithTooltipComponent,
        ResumeSurvenanceHabitationComponent,
    ],
})
export class ResumeSinistreHabitationComponent implements OnInit {
    private readonly declarationService: DeclarationService =
        inject(DeclarationService)
    private readonly risquesService: RisquesService = inject(RisquesService)

    readonly TRANSLATION_PREFIX: string = 'common.resume-sinistre-habitation.'

    declaration$?: Observable<DeclarationHabitation>
    risque$?: Observable<Risque>
    form$?: Observable<DeclarationHabitationForm>

    protected readonly TextType = TextType

    ngOnInit(): void {
        this.declaration$ = this.declarationService.getDeclarationHabitation$()
        this.risque$ = this.getRisque$(this.declaration$)
        this.form$ = this.getForm$()
    }

    resumeDetailSinistreHabitationIsShowed(
        form: DeclarationHabitationForm
    ): boolean {
        return (
            hasNonNullValues(form.controls.circonstance.getValue()) ||
            hasNonNullValues(form.controls.detailsSinistre.getValue())
        )
    }

    resumeDommagesHabitationIsShowed(form: DommagesHabitationForm): boolean {
        return hasNonNullValues(form.getValue())
    }

    resumeModalitesIsShowed(form: ModalitesForm): boolean {
        return hasNonNullValues(form.getValueHabitation())
    }

    private getRisque$(
        declaration$: Observable<DeclarationHabitation>
    ): Observable<Risque> {
        return declaration$.pipe(
            switchMap((declaration: DeclarationHabitation) =>
                this.risquesService.getSituationContratRisque(
                    declaration.numeroRisque
                )
            )
        )
    }

    private getForm$(): Observable<DeclarationHabitationForm> {
        const form = this.declarationService.getDeclarationHabitationForm()
        return form.valueChanges.pipe(
            startWith(form),
            distinctUntilChanged(),
            map(() => form)
        )
    }
}
