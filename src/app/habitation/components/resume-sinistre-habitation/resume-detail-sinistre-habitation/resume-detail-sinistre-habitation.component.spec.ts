import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ResumeDetailSinistreHabitationComponent } from './resume-detail-sinistre-habitation.component'
import { provideTranslation } from '../../../../core/providers/translation.provider'
import {
    checkElementToContainClass,
    checkTextContentForElement,
    expectElementToExist,
} from 'src/app/shared/utils/test.utils'
import { OrigineDegats } from '../../../enums/origine-degats.enum'
import { singleTiersMock } from '../../../../shared/mocks/tiers.mock'

describe('ResumeDetailSinistreHabitationComponent', () => {
    let component: ResumeDetailSinistreHabitationComponent
    let fixture: ComponentFixture<ResumeDetailSinistreHabitationComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ResumeDetailSinistreHabitationComponent],
            providers: [provideTranslation()],
        }).compileComponents()

        fixture = TestBed.createComponent(
            ResumeDetailSinistreHabitationComponent
        )
        component = fixture.componentInstance
    })

    it('should display detail of sinistre on template with correct element', () => {
        component.detailsSinistre = {
            presentAuMomentDesFaits: false,
            origine: OrigineDegats.AUTRE,
            interventionPoliceOuPompiers: false,
            avecNumeroProcesVerbal: false,
            tiersImplique: false,
        }
        fixture.detectChanges()
        checkTextContentForElement(
            fixture,
            '.presence-data-tile .DataTile-label span',
            'Le client était-il présent au moment des faits ?'
        )
        checkTextContentForElement(
            fixture,
            '.presence-data-tile .DataTile-value span',
            'Non'
        )

        checkTextContentForElement(
            fixture,
            '.origine-data-tile .DataTile-label span',
            "Quelle est l'origine des dégâts ?"
        )
        checkTextContentForElement(
            fixture,
            '.origine-data-tile .DataTile-value span',
            'Autre'
        )

        checkTextContentForElement(
            fixture,
            '.intervention-data-tile .DataTile-label span',
            'Y-a-t-il eu intervention de la police/pompiers ?'
        )
        checkTextContentForElement(
            fixture,
            '.intervention-data-tile .DataTile-value span',
            'Non'
        )

        checkTextContentForElement(
            fixture,
            '.avec-numero-proces-verbal-data-tile .DataTile-label span',
            'Existe-t-il un numéro du procès verbal ?'
        )
        checkTextContentForElement(
            fixture,
            '.avec-numero-proces-verbal-data-tile .DataTile-value span',
            'Non'
        )

        checkTextContentForElement(
            fixture,
            '.tiers-implique-data-tile .DataTile-label span',
            'Un tiers est-il impliqué dans le sinistre ?'
        )

        checkTextContentForElement(
            fixture,
            '.tiers-implique-data-tile .DataTile-value span',
            'Non'
        )
    })

    it('should display detail for numero de police with sub-data-tile class when avecNumeroProcesVerbal is true', () => {
        component.detailsSinistre = {
            avecNumeroProcesVerbal: true,
            numeroProcesVerbal: '123456',
        }
        fixture.detectChanges()

        checkTextContentForElement(
            fixture,
            '.avec-numero-proces-verbal-data-tile .DataTile-label span',
            'Existe-t-il un numéro du procès verbal ?'
        )
        checkTextContentForElement(
            fixture,
            '.avec-numero-proces-verbal-data-tile .DataTile-value span',
            'Oui'
        )

        checkTextContentForElement(
            fixture,
            '.numero-proces-verbal-data-tile .DataTile-label span',
            'N° du procès verbal ?'
        )
        checkTextContentForElement(
            fixture,
            '.numero-proces-verbal-data-tile .DataTile-value span',
            '123456'
        )
        checkElementToContainClass(
            fixture,
            '.numero-proces-verbal-data-tile .DataTile-content',
            'sub-data-tile'
        )
    })

    it('should display detail for tiers with tiers component when tiersImplique is true', () => {
        component.detailsSinistre = {
            tiersImplique: true,
            tiers: [singleTiersMock()],
        }
        fixture.detectChanges()

        checkTextContentForElement(
            fixture,
            '.tiers-implique-data-tile .DataTile-label span',
            'Un tiers est-il impliqué dans le sinistre ?'
        )
        checkTextContentForElement(
            fixture,
            '.tiers-implique-data-tile .DataTile-value span',
            'Oui'
        )

        expectElementToExist(fixture, 'resume-tiers')
    })
})
