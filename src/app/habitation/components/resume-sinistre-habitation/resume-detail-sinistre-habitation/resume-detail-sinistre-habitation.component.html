<h5 class="u-has-margin-bottom-4">{{ 'common.circonstance-sinistre' | translate }}</h5>

<resume-circonstance [circonstance]="circonstance"></resume-circonstance>

@if (detailsSinistre && hasNonNullValues(detailsSinistre)) {
    <h5 class="u-has-margin-top-12 u-has-margin-bottom-4">
        <span>{{ TRANSLATION_PREFIX + 'title' | translate }}</span>
    </h5>

    @if (detailsSinistre.presentAuMomentDesFaits === true || detailsSinistre.presentAuMomentDesFaits === false) {
        <data-tile class="presence-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'present' | translate }}</span>
            <span value>
                {{ 'common.' + detailsSinistre.presentAuMomentDesFaits | translate }}
            </span>
        </data-tile>
    }
    @if (detailsSinistre.origine) {
        <data-tile class="origine-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'origine' | translate }}</span>
            <span value>
                {{ 'enums.origine-degats.' + detailsSinistre.origine | translate }}
            </span>
        </data-tile>
    }
    @if (detailsSinistre.interventionPoliceOuPompiers === true || detailsSinistre.interventionPoliceOuPompiers === false) {
        <data-tile class="intervention-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'intervention-police-ou-pompiers' | translate }}</span>
            <span value>
                {{ 'common.' + detailsSinistre.interventionPoliceOuPompiers | translate }}
            </span>
        </data-tile>
    }
    @if (detailsSinistre.avecNumeroProcesVerbal === true || detailsSinistre.avecNumeroProcesVerbal === false) {
        <data-tile class="avec-numero-proces-verbal-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'avec-numero-proces-verbal' | translate }}</span>
            <span value>
                {{ 'common.' + detailsSinistre.avecNumeroProcesVerbal | translate }}
            </span>
        </data-tile>
    }
    @if (detailsSinistre.avecNumeroProcesVerbal && detailsSinistre.numeroProcesVerbal) {
        <data-tile class="numero-proces-verbal-data-tile" [isSubDataTile]="true">
            <span label>{{ TRANSLATION_PREFIX + 'numero-proces-verbal' | translate }}</span>
            <span value>
                {{ detailsSinistre.numeroProcesVerbal }}
            </span>
        </data-tile>
    }
    @if (detailsSinistre.tiersImplique === true || detailsSinistre.tiersImplique === false) {
        <data-tile class="tiers-implique-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'tiers-implique' | translate }}</span>
            <span value>
                {{ 'common.' + detailsSinistre.tiersImplique | translate }}
            </span>
        </data-tile>
    }

    @if (detailsSinistre.tiersImplique && detailsSinistre.tiers) {
        <resume-tiers [tiersList]="detailsSinistre.tiers"></resume-tiers>
    }
}
