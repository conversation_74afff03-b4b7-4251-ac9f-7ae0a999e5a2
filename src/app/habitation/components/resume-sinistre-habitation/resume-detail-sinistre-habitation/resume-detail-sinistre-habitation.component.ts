import { ChangeDetectionStrategy, Component, Input } from '@angular/core'

import { DataTileComponent } from '../../../../shared/components/data-tile/data-tile.component'

import { I18nPipe } from '@foyer/ng-i18n'
import { ResumeCirconstanceComponent } from '../../../../shared/components/resume-sinistre/resume-circonstance/resume-circonstance.component'
import { DetailsSinistreHabitation } from '../../../models/details-sinistre-habitation'
import { CirconstanceSinistre } from '../../../../shared/models/circonstance-sinistre'
import { ResumeTiersComponent } from '../../../../shared/components/resume-sinistre/resume-tiers/resume-tiers.component'
import { hasNonNullValues } from '../../../../shared/utils/objects.utils'

@Component({
    selector: 'resume-detail-sinistre-habitation',
    templateUrl: './resume-detail-sinistre-habitation.component.html',
    styleUrls: ['./resume-detail-sinistre-habitation.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        DataTileComponent,
        I18nPipe,
        ResumeCirconstanceComponent,
        ResumeTiersComponent,
    ],
})
export class ResumeDetailSinistreHabitationComponent {
    @Input()
    detailsSinistre?: DetailsSinistreHabitation
    @Input()
    circonstance?: CirconstanceSinistre

    readonly TRANSLATION_PREFIX: string = 'common.details-sinistre-habitation.'

    protected readonly hasNonNullValues = hasNonNullValues
}
