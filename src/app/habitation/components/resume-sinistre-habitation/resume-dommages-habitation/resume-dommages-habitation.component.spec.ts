import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ResumeDommagesHabitationComponent } from './resume-dommages-habitation.component'
import { ZonesMaisonInterieur } from '../../../enums/zones-maison-interieur.enum'
import { ZonesMaisonExterieur } from '../../../enums/zones-maison-exterieur.enum'
import { ObjetsDeDommage } from '../../../enums/objets-de-dommage.enum'
import { provideTranslation } from '../../../../core/providers/translation.provider'
import { checkTextContentForElement } from '../../../../shared/utils/test.utils'

describe('ResumeDommagesHabitationComponent', () => {
    let component: ResumeDommagesHabitationComponent
    let fixture: ComponentFixture<ResumeDommagesHabitationComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ResumeDommagesHabitationComponent],
            providers: [provideTranslation()],
        }).compileComponents()

        fixture = TestBed.createComponent(ResumeDommagesHabitationComponent)
        component = fixture.componentInstance
    })

    it('should display detail of dommages on template with correct element for degats pieces', () => {
        component.dommages = {
            degatsPieces: [
                ZonesMaisonInterieur.CUISINE,
                ZonesMaisonInterieur.COMBLES,
            ],
        }
        fixture.detectChanges()

        checkTextContentForElement(
            fixture,
            '.degats-pieces-data-tile .DataTile-label span',
            'Pièces ayant subi des dégats'
        )
        checkTextContentForElement(
            fixture,
            '.degats-pieces-data-tile .DataTile-value span',
            'Cuisine, Combles'
        )
    })

    it('should display detail of dommages on template with correct element for degats exterieurs', () => {
        component.dommages = {
            degatsExterieurs: [ZonesMaisonExterieur.TOITURE],
        }
        fixture.detectChanges()

        checkTextContentForElement(
            fixture,
            '.degats-exterieurs-data-tile .DataTile-label span',
            'Eléments extérieurs ayant subi des dégats'
        )
        checkTextContentForElement(
            fixture,
            '.degats-exterieurs-data-tile .DataTile-value span',
            'Toiture'
        )
    })

    it('should display detail of dommages on template with correct element for objets de dommage and autres objets de dommage', () => {
        component.dommages = {
            objetsDeDommage: [ObjetsDeDommage.BIJOUX],
            autresObjetsDeDommage: ['Planche ouija'],
        }
        fixture.detectChanges()

        checkTextContentForElement(
            fixture,
            '.objets-dommage-data-tile .DataTile-label span',
            'Objets endommagés'
        )
        checkTextContentForElement(
            fixture,
            '.objets-dommage-data-tile .DataTile-value span',
            'Bijoux, Planche ouija'
        )
    })
})
