import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { FormatArrayForTooltipPipe } from '../../../../shared/pipes/format-array-for-tooltip.pipe'
import { GetTranslationForArrayEnumPipe } from '../../../../shared/pipes/translation/get-translation-for-array-enum.pipe'

import { WithTooltipComponent } from '../../../../shared/components/with-tootip/with-tooltip.component'
import { DataTileComponent } from '../../../../shared/components/data-tile/data-tile.component'
import { AsyncPipe } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { TextType } from '../../../../shared/enums/text-type.enum'
import { DommagesHabitation } from '../../../models/dommages-habitation'

@Component({
    selector: 'resume-dommages-habitation',
    templateUrl: './resume-dommages-habitation.component.html',
    styleUrls: ['./resume-dommages-habitation.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [
        DataTileComponent,
        WithTooltipComponent,
        AsyncPipe,
        I18nPipe,
        GetTranslationForArrayEnumPipe,
        FormatArrayForTooltipPipe,
    ],
})
export class ResumeDommagesHabitationComponent {
    @Input()
    dommages?: DommagesHabitation

    readonly TRANSLATION_PREFIX: string = 'common.dommages-habitation.'

    protected readonly TextType = TextType
}
