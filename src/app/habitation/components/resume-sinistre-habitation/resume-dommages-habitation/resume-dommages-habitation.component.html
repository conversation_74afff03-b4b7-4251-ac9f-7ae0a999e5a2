@if (dommages) {
    <h5 class="u-has-margin-bottom-4 u-has-margin-top-4">{{ TRANSLATION_PREFIX + 'title' | translate }}</h5>
    @if (dommages.degatsPieces && dommages.degatsPieces.length > 0) {
        <data-tile class="degats-pieces-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'degats-pieces.title' | translate }}</span>
            <span value>
                @if (dommages.degatsPieces! | getTranslationForArrayEnum: 'enums.zones-maison-interieur.' | async; as degatsPieces) {
                    <with-tooltip [text]="degatsPieces" [textType]="TextType.TEXT_TRANSLATED">
                        {{ degatsPieces }}
                    </with-tooltip>
                }
            </span>
        </data-tile>
    }
    @if (dommages.degatsExterieurs && dommages.degatsExterieurs.length > 0) {
        <data-tile class="degats-exterieurs-data-tile">
            <span label>{{ TRANSLATION_PREFIX + 'degats-exterieurs.title' | translate }}</span>
            <span value>
                @if (dommages.degatsExterieurs! | getTranslationForArrayEnum: 'enums.zones-maison-exterieur.' | async; as degatsExterieurs) {
                    <with-tooltip [text]="degatsExterieurs" [textType]="TextType.TEXT_TRANSLATED">
                        {{ degatsExterieurs }}
                    </with-tooltip>
                }
            </span>
        </data-tile>
    }
    @if ((dommages.objetsDeDommage && dommages.objetsDeDommage.length > 0) || (dommages.autresObjetsDeDommage && dommages.autresObjetsDeDommage.length > 0)) {
        <data-tile class="objets-dommage-data-tile">
            <span label>{{ 'common.objets-de-dommage.title' | translate }}</span>
            <span value>
                @if (
                    {
                        objetsDommage: dommages.objetsDeDommage! | getTranslationForArrayEnum: 'enums.objets-de-dommage.' | async,
                        autresObjetsDommage: dommages.autresObjetsDeDommage! | formatArrayForTooltip,
                    };
                    as combined
                ) {
                    @if ([combined.objetsDommage!, combined.autresObjetsDommage] | formatArrayForTooltip; as combinedObjetsDommage) {
                        <with-tooltip [text]="combinedObjetsDommage" [textType]="TextType.TEXT_TRANSLATED">
                            {{ combinedObjetsDommage }}
                        </with-tooltip>
                    }
                }
            </span>
        </data-tile>
    }
}
