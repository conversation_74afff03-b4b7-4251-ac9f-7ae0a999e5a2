import { ComponentFixture, TestBed } from '@angular/core/testing'
import { ResumeSurvenanceHabitationComponent } from './resume-survenance-habitation.component'
import { DeclarationService } from '../../../../shared/services/declaration/declaration.service'
import { DeclarationServiceMock } from '../../../../shared/services/declaration/declaration.service.mock'
import { provideTranslation } from '../../../../core/providers/translation.provider'
import { checkTextContentForElement } from '../../../../shared/utils/test.utils'

describe('ResumeSurvenanceHabitationComponent', () => {
    let component: ResumeSurvenanceHabitationComponent
    let fixture: ComponentFixture<ResumeSurvenanceHabitationComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [ResumeSurvenanceHabitationComponent],
            providers: [
                provideTranslation(),
                {
                    provide: DeclarationService,
                    useClass: DeclarationServiceMock,
                },
            ],
        }).compileComponents()

        fixture = TestBed.createComponent(ResumeSurvenanceHabitationComponent)
        component = fixture.componentInstance
    })

    it('should add heure de survenance when it is set in the form', () => {
        component.survenance = {
            dateDeSurvenance: new Date(2020, 6, 11, 9, 15, 0),
            heureDeSurvenance: '14:02',
        }
        fixture.detectChanges()

        checkTextContentForElement(
            fixture,
            '.date-de-survenance-data-tile .DataTile-value span',
            '11.07.2020 - 14:02'
        )
    })
})
