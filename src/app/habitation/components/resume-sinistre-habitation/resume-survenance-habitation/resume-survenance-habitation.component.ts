import { ChangeDetectionStrategy, Component, Input } from '@angular/core'
import { DataTileComponent } from '../../../../shared/components/data-tile/data-tile.component'
import { FoyerDatePipe } from '../../../../shared/pipes/foyer/foyer-date.pipe'
import { I18nPipe } from '@foyer/ng-i18n'

import { Survenance } from '../../../../shared/models/survenance'

@Component({
    selector: 'resume-survenance-habitation',
    standalone: true,
    imports: [DataTileComponent, FoyerDatePipe, I18nPipe],
    templateUrl: './resume-survenance-habitation.component.html',
    styleUrl: './resume-survenance-habitation.component.scss',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResumeSurvenanceHabitationComponent {
    @Input()
    survenance?: Survenance

    readonly TRANSLATION_PREFIX: string = 'common.survenance-habitation.'
}
