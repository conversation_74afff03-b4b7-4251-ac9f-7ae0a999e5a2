@if (form) {
    <form [formGroup]="asFormGroup(form)">
        <div class="Panel u-is-txt-monospace u-has-margin-top-16">
            <div class="Panel-header has-small-title">
                <span>{{ TRANSLATION_PREFIX + 'title' | translate }}</span>
            </div>
            <div class="Panel-body">
                <div class="poll-question">
                    <span>{{ TRANSLATION_PREFIX + 'present' | translate }}</span>
                    <yes-no-choice-card formControlName="presentAuMomentDesFaits"></yes-no-choice-card>
                </div>
                @if (
                    (form.controls.presentAuMomentDesFaits.touched || form.controls.presentAuMomentDesFaits.dirty) &&
                    form.controls.presentAuMomentDesFaits.invalid
                ) {
                    <message-banner>
                        @if (form.controls.presentAuMomentDesFaits.errors && form.controls.presentAuMomentDesFaits.errors['required']) {
                            <span>
                                {{ 'common.is-option-required' | translate }}
                            </span>
                        }
                    </message-banner>
                }
            </div>
            <div class="Panel-body">
                <div class="poll-question">
                    <span>{{ TRANSLATION_PREFIX + 'origine' | translate }}</span>
                    <select
                        class="Select is-medium"
                        [ngClass]="{
                            'is-danger': (form.controls.origine.touched || form.controls.origine.dirty) && form.controls.origine.invalid,
                        }"
                        formControlName="origine">
                        @for (origine of OrigineDegats | keyvalue; track origine) {
                            <option [value]="origine.value">
                                {{ 'enums.origine-degats.' + origine.value | translate }}
                            </option>
                        }
                    </select>
                </div>
                <div class="GridFlex-col-12 u-has-margin-top-16 u-has-padding-0">
                    @if ((form.controls.origine.touched || form.controls.origine.dirty) && form.controls.origine.invalid) {
                        <message-banner>
                            @if (form.controls.origine.errors && form.controls.origine.errors['required']) {
                                <span>
                                    {{ 'common.is-option-required' | translate }}
                                </span>
                            }
                        </message-banner>
                    }
                </div>
            </div>
            @if (form.controls.interventionPoliceOuPompiers.enabled) {
                <div class="Panel-body">
                    <div class="poll-question">
                        <span>{{ TRANSLATION_PREFIX + 'intervention-police-ou-pompiers' | translate }}</span>
                        <yes-no-choice-card formControlName="interventionPoliceOuPompiers"></yes-no-choice-card>
                    </div>
                    @if (
                        (form.controls.interventionPoliceOuPompiers.touched || form.controls.interventionPoliceOuPompiers.dirty) &&
                        form.controls.interventionPoliceOuPompiers.invalid
                    ) {
                        <message-banner>
                            @if (form.controls.interventionPoliceOuPompiers.errors && form.controls.interventionPoliceOuPompiers.errors['required']) {
                                <span>
                                    {{ 'common.is-option-required' | translate }}
                                </span>
                            }
                        </message-banner>
                    }
                </div>
            }
            @if (form.controls.interventionPoliceOuPompiers.value === true) {
                <div class="Panel-body" @expand>
                    <div class="poll-question">
                        <span>{{ TRANSLATION_PREFIX + 'avec-numero-proces-verbal' | translate }}</span>
                        <yes-no-choice-card formControlName="avecNumeroProcesVerbal"></yes-no-choice-card>
                    </div>
                    @if (
                        (form.controls.avecNumeroProcesVerbal.touched || form.controls.avecNumeroProcesVerbal.dirty) &&
                        form.controls.avecNumeroProcesVerbal.invalid
                    ) {
                        <message-banner>
                            @if (form.controls.avecNumeroProcesVerbal.errors && form.controls.avecNumeroProcesVerbal.errors['required']) {
                                <span>
                                    {{ 'common.is-option-required' | translate }}
                                </span>
                            }
                        </message-banner>
                    }
                    @if (form.controls.avecNumeroProcesVerbal.value === true) {
                        <div class="GridFlex u-has-margin-top-24" @expand>
                            <div class="GridFlex-row Form-row">
                                <div class="GridFlex-col-12">
                                    <div
                                        class="Form-field is-medium"
                                        [class.is-danger]="
                                            (form.controls.numeroProcesVerbal.touched || form.controls.numeroProcesVerbal.dirty) &&
                                            form.controls.numeroProcesVerbal.invalid
                                        ">
                                        <label for="numeroProcesVerbal" class="Form-field-label">
                                            {{ TRANSLATION_PREFIX + 'numero-proces-verbal' | translate }}
                                        </label>
                                        <div class="Form-field-input">
                                            <input
                                                id="numeroProcesVerbal"
                                                formControlName="numeroProcesVerbal"
                                                class="Input u-is-full-width"
                                                [placeholder]="TRANSLATION_PREFIX + 'numero-proces-verbal-placeholder' | translate" />
                                        </div>
                                    </div>
                                    @if (
                                        (form.controls.numeroProcesVerbal.touched || form.controls.numeroProcesVerbal.dirty) &&
                                        form.controls.numeroProcesVerbal.invalid
                                    ) {
                                        <message-banner>
                                            @if (form.controls.numeroProcesVerbal.errors && form.controls.numeroProcesVerbal.errors['required']) {
                                                <span>
                                                    {{ 'common.is-required' | translate: { field: TRANSLATION_PREFIX + 'numero-proces-verbal' | translate } }}
                                                </span>
                                            }
                                        </message-banner>
                                    }
                                </div>
                            </div>
                        </div>
                    }
                </div>
            }
        </div>

        @if (form.controls.tiersImplique.enabled) {
            <tiers [parentFormGroup]="asFormGroup(form)" [form]="form.controls.tiers" [withTiersForm]="form.controls.tiersImplique.value === true">
                <ng-container specificForm>
                    <div class="poll-question">
                        <span>{{ TRANSLATION_PREFIX + 'tiers-implique' | translate }}</span>
                        <yes-no-choice-card formControlName="tiersImplique"></yes-no-choice-card>
                    </div>
                    @if ((form.controls.tiersImplique.touched || form.controls.tiersImplique.dirty) && form.controls.tiersImplique.invalid) {
                        <message-banner>
                            @if (form.controls.tiersImplique.errors && form.controls.tiersImplique.errors['required']) {
                                <span>
                                    {{ 'common.is-option-required' | translate }}
                                </span>
                            }
                        </message-banner>
                    }
                </ng-container>
            </tiers>
        }
    </form>
}
