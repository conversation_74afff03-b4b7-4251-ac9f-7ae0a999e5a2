import { FormControl } from '@angular/forms'
import { OrigineDegats } from '../../../enums/origine-degats.enum'
import { TiersForm } from '../../../../shared/models/forms/tiers.form'

export interface DetailsSinistreHabitationFormStructure {
    presentAuMomentDesFaits: FormControl<boolean | null>
    origine: FormControl<OrigineDegats | null>
    interventionPoliceOuPompiers: FormControl<boolean | null>
    avecNumeroProcesVerbal: FormControl<boolean | null>
    numeroProcesVerbal: FormControl<string | null>
    tiersImplique: FormControl<boolean | null>
    tiers: TiersForm
}
