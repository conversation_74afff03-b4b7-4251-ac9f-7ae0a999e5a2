import { FormControl, FormGroup, Validators } from '@angular/forms'
import { DetailsSinistreHabitationFormStructure } from './details-sinistre-habitation-form-structure'
import {
    combineLatest,
    distinctUntilChanged,
    Observable,
    startWith,
    tap,
} from 'rxjs'
import { DetailsSinistreHabitation } from '../../../models/details-sinistre-habitation'
import { TiersForm } from '../../../../shared/models/forms/tiers.form'

export class DetailsSinistreHabitationForm extends FormGroup<DetailsSinistreHabitationFormStructure> {
    constructor() {
        super({
            presentAuMomentDesFaits: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            origine: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            interventionPoliceOuPompiers: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            avecNumeroProcesVerbal: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            numeroProcesVerbal: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            tiersImplique: new FormControl(null, {
                nonNullable: true,
                validators: [Validators.required],
            }),
            tiers: new TiersForm(),
        })
    }

    getValue(): DetailsSinistreHabitation {
        return this.value as DetailsSinistreHabitation
    }

    validationChangesListeners$(): Observable<unknown> {
        const validationChangesOnIntervention$ =
            this.validationChangesOnIntervention()
        const validationChangesOnTiers$ = this.validationChangesOnTiers()

        return combineLatest([
            validationChangesOnIntervention$,
            validationChangesOnTiers$,
        ])
    }

    private validationChangesOnIntervention() {
        const interventionPoliceOuPompiersValueChanges$ =
            this.controls.interventionPoliceOuPompiers.valueChanges.pipe(
                startWith(this.controls.interventionPoliceOuPompiers.value),
                distinctUntilChanged(),
                tap((intervention) => {
                    if (intervention === true) {
                        this.controls.avecNumeroProcesVerbal.enable()
                    } else {
                        this.controls.avecNumeroProcesVerbal.disable()
                        this.controls.avecNumeroProcesVerbal.reset()
                        this.controls.numeroProcesVerbal.disable()
                        this.controls.numeroProcesVerbal.reset()
                    }
                })
            )

        const avecNumeroProcesVerbalValueChanges$ =
            this.controls.avecNumeroProcesVerbal.valueChanges.pipe(
                startWith(this.controls.avecNumeroProcesVerbal.value),
                distinctUntilChanged(),
                tap((avecNumeroProcesVerbal) => {
                    if (avecNumeroProcesVerbal === true) {
                        this.controls.numeroProcesVerbal.enable()
                    } else {
                        this.controls.numeroProcesVerbal.disable()
                        this.controls.numeroProcesVerbal.reset()
                    }
                })
            )

        return combineLatest([
            interventionPoliceOuPompiersValueChanges$,
            avecNumeroProcesVerbalValueChanges$,
        ])
    }

    private validationChangesOnTiers() {
        return this.controls.tiersImplique.valueChanges.pipe(
            startWith(this.controls.tiersImplique.value),
            distinctUntilChanged(),
            tap((tiersImplique) => {
                if (tiersImplique === true) {
                    this.controls.tiers.enable()
                    if (this.controls.tiers.length === 0) {
                        this.controls.tiers.createArrayControl()
                    }
                } else {
                    this.controls.tiers.disable()
                    this.controls.tiers.reset()
                }
            })
        )
    }
}
