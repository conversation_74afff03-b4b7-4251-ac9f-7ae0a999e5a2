import {
    AfterViewChecked,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    inject,
    Input,
} from '@angular/core'
import { DetailsSinistreHabitationForm } from './forms/details-sinistre-habitation.form'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import { OrigineDegats } from '../../enums/origine-degats.enum'
import { KeyValuePipe, NgClass } from '@angular/common'
import { I18nPipe } from '@foyer/ng-i18n'
import { expandAnimation } from '../../../shared/animations/expand.animation'
import { YesNoChoiceCardComponent } from '../../../shared/components/yes-no-choice-card/yes-no-choice-card.component'
import { MessageBannerComponent } from '../../../shared/components/message-banner/message-banner.component'
import { TiersComponent } from '../../../shared/components/tiers/tiers.component'

@Component({
    selector: 'details-sinistre-habitation',
    templateUrl: './details-sinistre-habitation.component.html',
    styleUrls: ['./details-sinistre-habitation.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: [expandAnimation],
    standalone: true,
    imports: [
        ReactiveFormsModule,
        YesNoChoiceCardComponent,
        MessageBannerComponent,
        NgClass,
        KeyValuePipe,
        I18nPipe,
        TiersComponent,
    ],
})
export class DetailsSinistreHabitationComponent implements AfterViewChecked {
    private readonly changeDetectorRef: ChangeDetectorRef =
        inject(ChangeDetectorRef)

    @Input()
    form?: DetailsSinistreHabitationForm

    readonly TRANSLATION_PREFIX: string = 'common.details-sinistre-habitation.'

    protected readonly OrigineDegats = OrigineDegats

    asFormGroup(form: DetailsSinistreHabitationForm): FormGroup {
        return form as unknown as FormGroup
    }

    ngAfterViewChecked(): void {
        this.changeDetectorRef.markForCheck()
    }
}
