import { ComponentFixture, TestBed } from '@angular/core/testing'

import { DetailsSinistreHabitationComponent } from './details-sinistre-habitation.component'
import { DetailsSinistreHabitationForm } from './forms/details-sinistre-habitation.form'
import { Validators } from '@angular/forms'
import { provideNoopAnimations } from '@angular/platform-browser/animations'
import { validDetailsHabitationMock } from '../../../shared/mocks/circonstances.mock'
import { expectElementToNotExist } from '../../../shared/utils/test.utils'

describe('DetailsSinistreComponent', () => {
    let component: DetailsSinistreHabitationComponent
    let fixture: ComponentFixture<DetailsSinistreHabitationComponent>

    beforeEach(async () => {
        await TestBed.configureTestingModule({
            imports: [DetailsSinistreHabitationComponent],
            providers: [provideNoopAnimations()],
        }).compileComponents()

        fixture = TestBed.createComponent(DetailsSinistreHabitationComponent)
        component = fixture.componentInstance
        component.form = new DetailsSinistreHabitationForm()
    })

    it('should be valid when all answers set to something valid', () => {
        component.form?.patchValue(validDetailsHabitationMock())
        fixture.detectChanges()
        expect(component.form?.valid).toBeTruthy()
    })

    describe('numeroProcesVerbal', () => {
        it('should be hidden by default', () => {
            fixture.detectChanges()
            expectElementToNotExist(fixture, '#numeroProcesVerbal')
        })

        it('should enable field and be required when avecNumeroProcesVerbal is true', () => {
            component.form?.validationChangesListeners$().subscribe()
            component.form?.controls.avecNumeroProcesVerbal.setValue(true)
            expect(
                component.form?.controls.avecNumeroProcesVerbal.enable
            ).toBeTruthy()
            expect(
                component.form?.controls.numeroProcesVerbal.hasValidator(
                    Validators.required
                )
            ).toBeTruthy()
        })

        it('should disable numeroProcesVerbal field when avecNumeroProcesVerbal is false ', () => {
            component.form?.validationChangesListeners$().subscribe()
            component.form?.controls.avecNumeroProcesVerbal.setValue(false)
            expect(
                component.form?.controls.numeroProcesVerbal.disabled
            ).toBeTruthy()
        })
    })

    describe('tiers', () => {
        it('should disable tiers form when tiersImplique is false', () => {
            component.form?.validationChangesListeners$().subscribe()
            component.form?.controls.tiersImplique.setValue(false)
            expect(component.form?.controls.tiers.disabled).toBeTruthy()
        })

        it('should enable tiers form when tiersImplique is true', () => {
            component.form?.validationChangesListeners$().subscribe()
            component.form?.controls.tiersImplique.setValue(true)
            expect(component.form?.controls.tiers.enabled).toBeTruthy()
        })
    })
})
