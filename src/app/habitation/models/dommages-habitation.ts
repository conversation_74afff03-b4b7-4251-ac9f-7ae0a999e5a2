import { ZonesMaisonInterieur } from '../enums/zones-maison-interieur.enum'
import { ZonesMaisonExterieur } from '../enums/zones-maison-exterieur.enum'
import { ObjetsDeDommage } from '../enums/objets-de-dommage.enum'

export interface DommagesHabitation {
    degatsPieces?: ZonesMaisonInterieur[]
    degatsExterieurs?: ZonesMaisonExterieur[]
    objetsDeDommage?: ObjetsDeDommage[]
    autresObjetsDeDommage?: string[]
}
