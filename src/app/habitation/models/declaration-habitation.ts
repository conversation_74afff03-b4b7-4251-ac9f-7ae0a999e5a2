import { DeclarationKind } from '../../shared/enums/declaration-kind.enum'
import { DeclarationBase } from '../../shared/models/declaration'
import { DetailsSinistreHabitation } from './details-sinistre-habitation'
import { DommagesHabitation } from './dommages-habitation'
import { ModalitesHabitation } from './modalites-habitation'

export interface DeclarationHabitation extends DeclarationBase {
    _kind: DeclarationKind.HABITATION
    detailsSinistre?: DetailsSinistreHabitation
    dommages?: DommagesHabitation
    modalites?: ModalitesHabitation
}
