import { FormControl } from '@angular/forms'
import { SurvenanceHabitationForm } from '../../components/survenance-habitation/forms/survenance-habitation.form'
import { TypeGaranties } from '../../../shared/enums/type-garanties.enum'
import { CirconstancesForm } from '../../../shared/components/circonstances/forms/circonstances-form'
import { DetailsSinistreHabitationForm } from '../../components/details-sinistre-habitation/forms/details-sinistre-habitation.form'
import { DommagesHabitationForm } from '../../components/dommages-habitation/forms/dommages-habitation-form'
import { ModalitesForm } from '../../../shared/components/modalites/forms/modalites.form'

export interface DeclarationHabitationFormStructure {
    referenceProducteur: FormControl<string | undefined>
    survenance: SurvenanceHabitationForm
    garanties: FormControl<TypeGaranties[] | undefined>
    circonstance: CirconstancesForm
    detailsSinistre: DetailsSinistreHabitationForm
    dommages: DommagesHabitationForm
    modalites: ModalitesForm
    piecesJointes: FormControl<string[]>
}
