import { FormControl, FormGroup } from '@angular/forms'
import { DeclarationHabitationFormStructure } from './declaration-habitation-form-structure'
import { map, Observable, pairwise, startWith } from 'rxjs'
import { SurvenanceHabitationForm } from '../../components/survenance-habitation/forms/survenance-habitation.form'
import { nonNullableArrayValidator } from '../../../shared/validators/non-nullable-array.validator'
import { CirconstancesForm } from '../../../shared/components/circonstances/forms/circonstances-form'
import { DetailsSinistreHabitationForm } from '../../components/details-sinistre-habitation/forms/details-sinistre-habitation.form'
import { DommagesHabitationForm } from '../../components/dommages-habitation/forms/dommages-habitation-form'
import { ModalitesForm } from '../../../shared/components/modalites/forms/modalites.form'

export class DeclarationHabitationForm extends FormGroup<DeclarationHabitationFormStructure> {
    constructor() {
        super({
            referenceProducteur: new FormControl(undefined, {
                nonNullable: true,
            }),
            survenance: new SurvenanceHabitationForm(),
            garanties: new FormControl(undefined, {
                nonNullable: true,
                validators: [nonNullableArrayValidator()],
            }),
            circonstance: new CirconstancesForm(),
            detailsSinistre: new DetailsSinistreHabitationForm(),
            dommages: new DommagesHabitationForm(),
            modalites: new ModalitesForm(false),
            piecesJointes: new FormControl([], { nonNullable: true }),
        })
    }

    hasChangeOnGarantiesListener$(): Observable<boolean> {
        const initialValue = this.controls.garanties.value
        return this.controls.garanties.valueChanges.pipe(
            startWith(initialValue),
            pairwise(),
            map(
                ([previous, current]) =>
                    (previous?.length ?? 0) !== (current?.length ?? 0)
            )
        )
    }
}
