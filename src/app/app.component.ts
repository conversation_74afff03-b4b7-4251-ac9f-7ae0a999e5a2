import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild } from '@angular/core'
import { AuthenticationService } from '@foyer/authentication'
import { Subscription } from 'rxjs'
import { createPopupMessage } from './shared/utils/error-display.utils'
import { WizardComponent } from './shared/components/wizard/wizard.component'
import { ViewContainerHostDirective } from './shared/directives/view-container-host.directive'
import { ModalService } from './shared/services/modal/modal.service'
import { StorageService } from './shared/services/storage/storage.service'
import { ErrorMessage } from './shared/models/error-message.model'
import { ErrorPopupComponent } from './shared/components/error-popup/error-popup.component'
import { ErrorLevel } from './shared/enums/error-type.enum'

@Component({
    selector: 'app-root',
    templateUrl: './app.component.html',
    styleUrls: ['./app.component.scss'],
    standalone: true,
    imports: [WizardComponent, ViewContainerHostDirective],
})
export class AppComponent implements OnInit, OnDestroy {
    @ViewChild(ViewContainerHostDirective, { static: true })
    popupHost!: ViewContainerHostDirective

    private readonly PORTIMA_SESSION_STORAGE_KEY = 'portima_session_storage'
    private readonly subscriptions: Subscription = new Subscription()

    constructor(
        private readonly authenticationService: AuthenticationService,
        private readonly modalService: ModalService,
        private readonly storageService: StorageService
    ) {}

    ngOnInit(): void {
        this.subscriptions.add(
            this.authenticationService.isLoggedIn$.subscribe((isLoggedIn) => {
                this.storageService.store(
                    this.PORTIMA_SESSION_STORAGE_KEY,
                    isLoggedIn
                )
                if (
                    !this.storageService.retrieve(
                        this.PORTIMA_SESSION_STORAGE_KEY
                    ) ||
                    this.storageService.isTokenExpired()
                ) {
                    this.authenticationService.login(
                        window.location.pathname + window.location.search
                    )
                }
            })
        )

        const invalidateContext = () => {
            this.storageService.clear()
            this.authenticationService.login(
                window.location.pathname + window.location.search
            )
        }

        this.subscriptions.add(
            this.authenticationService.accessErrors$.subscribe((_) => {
                this.showErrorPopup(
                    'common.authentication.access-denied',
                    'common.authentication.access-denied-message',
                    'common.authentication.access-denied-action',
                    invalidateContext
                )
            })
        )

        this.subscriptions.add(
            this.authenticationService.authenticationErrors$.subscribe((_) => {
                this.showErrorPopup(
                    'common.authentication.session-expired',
                    'common.authentication.session-expired-message',
                    'common.authentication.session-expired-action',
                    invalidateContext
                )
            })
        )
    }

    ngOnDestroy(): void {
        this.subscriptions.unsubscribe()
        this.storageService.clear()
    }

    private showErrorPopup(
        title: string,
        message: string,
        action: string,
        invalidateContext: () => void
    ): void {
        this.modalService.showAsComponent<
            void,
            ErrorMessage,
            ErrorPopupComponent
        >(
            ErrorPopupComponent,
            this.popupHost.viewContainerRef,
            createPopupMessage(title, message, ErrorLevel.DANGER, action, () =>
                invalidateContext()
            )
        )
    }
}
